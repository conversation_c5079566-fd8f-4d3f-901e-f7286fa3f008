<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="#f5f5f5" stroke="#20c997" stroke-width="10"/>

  <!-- B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Vertical Bar -->
    <rect x="15" y="20" width="18" height="90"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M15 20 L65 20 Q85 20 85 35 Q85 50 65 50 L33 50 L33 35 L65 35 Q75 35 75 37.5 Q75 40 65 40 L33 40 L33 50 L65 50 Q85 50 85 35 Q85 20 65 20 L15 20 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M15 58 L70 58 Q90 58 90 73 Q90 88 70 88 L33 88 L33 73 L70 73 Q80 73 80 75.5 Q80 78 70 78 L33 78 L33 88 L70 88 Q90 88 90 73 Q90 58 70 58 L15 58 Z"/>
    <!-- B Letter Middle Connection -->
    <rect x="15" y="50" width="18" height="8"/>
  </g>

  <!-- R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Main Vertical Bar -->
    <rect x="110" y="20" width="18" height="90"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M110 20 L155 20 Q175 20 175 35 Q175 50 155 50 L128 50 L128 35 L155 35 Q165 35 165 37.5 Q165 40 155 40 L128 40 L128 50 L155 50 Q175 50 175 35 Q175 20 155 20 L110 20 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M128 50 L155 50 L185 110 Q187 113 184 113 L178 113 Q175 113 174 110 L148 65 L128 65 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(75, 70)">
    <!-- Car Main Body -->
    <path d="M0 9 Q0 4 4 4 L46 4 Q50 4 50 9 L50 18 L46 18 L46 23 L42 23 Q40 25 38 23 L12 23 Q10 25 8 23 L4 23 L4 18 L0 18 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M8 4 Q8 0 12 0 L38 0 Q42 0 42 4 L38 4 L12 4 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="10" y="1" width="30" height="3" rx="1" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="6" y="2" width="6" height="2" rx="0.5" fill="#f5f5f5"/>
    <rect x="38" y="2" width="6" height="2" rx="0.5" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="3" cy="8" r="2" fill="#1e3a5f"/>
    <circle cx="47" cy="8" r="2" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="-2" cy="14" rx="3" ry="2.5" fill="#20c997"/>
    <ellipse cx="52" cy="14" rx="3" ry="2.5" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="10" cy="23" r="6" fill="#1e3a5f"/>
    <circle cx="40" cy="23" r="6" fill="#1e3a5f"/>
    <circle cx="10" cy="23" r="3" fill="#6c757d"/>
    <circle cx="40" cy="23" r="3" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 125)">
    <!-- Wave 1 -->
    <path d="M10 0 Q50 -7 100 0 Q150 7 190 0 L190 10 Q150 17 100 10 Q50 3 10 10 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M10 7 Q50 0 100 7 Q150 14 190 7 L190 17 Q150 24 100 17 Q50 10 10 17 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M10 14 Q50 7 100 14 Q150 21 190 14 L190 24 Q150 31 100 24 Q50 17 10 24 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M10 21 Q50 14 100 21 Q150 28 190 21 L190 31 Q150 38 100 31 Q50 24 10 31 Z" fill="#0f6674"/>
  </g>

  <!-- BARO RIDE Text -->
  <text x="100" y="175" font-family="Arial, sans-serif" font-size="12" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
</svg>
