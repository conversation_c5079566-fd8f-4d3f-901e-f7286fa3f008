<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="#f5f5f5" stroke="#20c997" stroke-width="10"/>

  <!-- B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Vertical Bar -->
    <rect x="20" y="20" width="15" height="80" rx="5"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M20 20 L55 20 Q70 20 70 35 Q70 50 55 50 L35 50 L35 35 L55 35 Q60 35 60 37.5 Q60 40 55 40 L35 40 L35 50 L55 50 Q70 50 70 35 Q70 20 55 20 L20 20 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M20 55 L60 55 Q75 55 75 70 Q75 85 60 85 L35 85 L35 70 L60 70 Q65 70 65 72.5 Q65 75 60 75 L35 75 L35 85 L60 85 Q75 85 75 70 Q75 55 60 55 L20 55 Z"/>
  </g>

  <!-- R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Vertical Bar -->
    <rect x="130" y="20" width="15" height="80" rx="5"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M130 20 L165 20 Q180 20 180 35 Q180 50 165 50 L145 50 L145 35 L165 35 Q170 35 170 37.5 Q170 40 165 40 L145 40 L145 50 L165 50 Q180 50 180 35 Q180 20 165 20 L130 20 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M145 50 L165 50 L185 85 Q187 88 184 88 L178 88 Q175 88 174 85 L156 60 L145 60 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(75, 65)">
    <!-- Car Body -->
    <path d="M5 15 Q5 10 10 10 L40 10 Q45 10 45 15 L45 25 L40 25 L40 30 L35 30 Q32 32 30 30 L20 30 Q18 32 15 30 L10 30 L10 25 L5 25 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M12 10 Q12 5 17 5 L33 5 Q38 5 38 10 L33 10 L17 10 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="15" y="6" width="20" height="4" rx="1" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="11" y="7" width="4" height="3" rx="1" fill="#f5f5f5"/>
    <rect x="35" y="7" width="4" height="3" rx="1" fill="#f5f5f5"/>
    <!-- Headlights -->
    <ellipse cx="4" cy="20" rx="2" ry="1.5" fill="#20c997"/>
    <ellipse cx="46" cy="20" rx="2" ry="1.5" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="15" cy="30" r="4" fill="#1e3a5f"/>
    <circle cx="35" cy="30" r="4" fill="#1e3a5f"/>
    <circle cx="15" cy="30" r="2" fill="#6c757d"/>
    <circle cx="35" cy="30" r="2" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 120)">
    <!-- Wave 1 -->
    <path d="M10 0 Q50 -10 100 0 Q150 10 190 0 L190 15 Q150 25 100 15 Q50 5 10 15 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M10 10 Q50 0 100 10 Q150 20 190 10 L190 25 Q150 35 100 25 Q50 15 10 25 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M10 20 Q50 10 100 20 Q150 30 190 20 L190 35 Q150 45 100 35 Q50 25 10 35 Z" fill="#138496"/>
  </g>

  <!-- BARO RIDE Text -->
  <text x="100" y="175" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
</svg>
