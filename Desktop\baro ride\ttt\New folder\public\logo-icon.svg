<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="#f5f5f5" stroke="#20c997" stroke-width="10"/>

  <!-- B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Shape -->
    <path d="M15 15 L15 85 L20 85 L20 15 Z"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M15 15 L55 15 Q75 15 75 30 Q75 45 55 45 L20 45 L20 30 L55 30 Q65 30 65 32.5 Q65 35 55 35 L20 35 L20 45 L55 45 Q75 45 75 30 Q75 15 55 15 L15 15 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M15 50 L60 50 Q80 50 80 65 Q80 80 60 80 L20 80 L20 65 L60 65 Q70 65 70 67.5 Q70 70 60 70 L20 70 L20 80 L60 80 Q80 80 80 65 Q80 50 60 50 L15 50 Z"/>
    <!-- B Letter Middle Connection -->
    <rect x="15" y="45" width="5" height="5"/>
  </g>

  <!-- R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Main Shape -->
    <path d="M125 15 L125 85 L130 85 L130 15 Z"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M125 15 L165 15 Q185 15 185 30 Q185 45 165 45 L130 45 L130 30 L165 30 Q175 30 175 32.5 Q175 35 165 35 L130 35 L130 45 L165 45 Q185 45 185 30 Q185 15 165 15 L125 15 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M130 45 L165 45 L185 85 Q187 88 184 88 L178 88 Q175 88 174 85 L148 55 L130 55 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(70, 60)">
    <!-- Car Main Body -->
    <path d="M12 20 Q12 15 17 15 L53 15 Q58 15 58 20 L58 30 L53 30 L53 35 L48 35 Q45 37 43 35 L27 35 Q25 37 22 35 L17 35 L17 30 L12 30 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M20 15 Q20 10 25 10 L45 10 Q50 10 50 15 L45 15 L25 15 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="22" y="11" width="26" height="4" rx="1" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="18" y="12" width="5" height="3" rx="0.5" fill="#f5f5f5"/>
    <rect x="47" y="12" width="5" height="3" rx="0.5" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="15" cy="18" r="2" fill="#1e3a5f"/>
    <circle cx="55" cy="18" r="2" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="10" cy="25" rx="3" ry="2.5" fill="#20c997"/>
    <ellipse cx="60" cy="25" rx="3" ry="2.5" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="22" cy="35" r="5" fill="#1e3a5f"/>
    <circle cx="48" cy="35" r="5" fill="#1e3a5f"/>
    <circle cx="22" cy="35" r="3" fill="#6c757d"/>
    <circle cx="48" cy="35" r="3" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 110)">
    <!-- Wave 1 -->
    <path d="M10 0 Q50 -10 100 0 Q150 10 190 0 L190 12 Q150 22 100 12 Q50 2 10 12 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M10 8 Q50 -2 100 8 Q150 18 190 8 L190 20 Q150 30 100 20 Q50 10 10 20 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M10 16 Q50 6 100 16 Q150 26 190 16 L190 28 Q150 38 100 28 Q50 18 10 28 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M10 24 Q50 14 100 24 Q150 34 190 24 L190 36 Q150 46 100 36 Q50 26 10 36 Z" fill="#0f6674"/>
  </g>

  <!-- BARO RIDE Text -->
  <text x="100" y="170" font-family="Arial, sans-serif" font-size="12" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
</svg>
