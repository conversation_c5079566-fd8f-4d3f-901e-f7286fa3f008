(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[616],{7805:function(e,r,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/signup",function(){return t(6254)}])},6254:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return h}});var a=t(5893),l=t(7294),s=t(1517),o=t(109),i=t(404),n=t(1163),d=t(9008),c=t.n(d),u=t(6492),m=t(8082);function h(){let[e,r]=(0,l.useState)(""),[t,d]=(0,l.useState)(""),[h,x]=(0,l.useState)(!1),[b,p]=(0,l.useState)(""),[f,g]=(0,l.useState)(""),[v,w]=(0,l.useState)("rider"),[y,j]=(0,l.useState)(""),[N,C]=(0,l.useState)(!1),k=(0,n.useRouter)(),{showNotification:z}=(0,u.l)(),[S,R]=(0,l.useState)(""),[P,E]=(0,l.useState)(""),[M,F]=(0,l.useState)(""),[B,A]=(0,l.useState)(""),[D,I]=(0,l.useState)(""),[L,_]=(0,l.useState)(!1),q=async r=>{r.preventDefault(),C(!0),j("");try{if(!e||!t||!b||!f)throw Error("Please fill in all required fields");if("driver"===v&&(!S||!P||!M||!B))throw Error("Please fill in all vehicle details");if("driver"===v&&"244117"!==D)throw Error("Invalid BaroRide ID password. Please contact support if you need assistance.");let r=(await (0,s.createUserWithEmailAndPassword)(i.auth,e,t)).user.uid,a=(0,m.XL)(f),l={id:r,email:e,phoneNumber:a,fullName:b,role:v,createdAt:new Date,updatedAt:new Date};if("rider"===v){let e={...l,role:"rider",bookingHistory:[]};await (0,o.setDoc)((0,o.doc)(i.db,"users",r),e)}else{let e={...l,role:"driver",isOnline:!1,isVerified:!0,vehicleDetails:{make:S,model:P,color:M,licensePlate:B},rating:5,completedRides:0};await (0,o.setDoc)((0,o.doc)(i.db,"users",r),e)}z("Account created successfully! Redirecting...","success",3e3),"driver"===v?k.push("/driver/dashboard"):k.push("/")}catch(r){let e=r instanceof Error?r.message:"Failed to create account. Please try again.";j(e),z(e,"error",3e3),C(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 safe-area-top safe-area-bottom",children:[(0,a.jsxs)(c(),{children:[(0,a.jsx)("title",{children:"BaroRide - Sign Up"}),(0,a.jsx)("meta",{name:"description",content:"Create your BaroRide account"}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"})]}),(0,a.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,a.jsxs)("div",{className:"flex-shrink-0 pt-8 pb-4 px-4 text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-16 w-16 sm:h-20 sm:w-20"})}),(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Join BaroRide"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Create your account to get started"})]}),(0,a.jsx)("div",{className:"flex-1 flex items-start justify-center px-4 pb-8",children:(0,a.jsxs)("div",{className:"w-full max-w-sm",children:[y&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 text-sm",children:y}),(0,a.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter your email address",value:e,onChange:e=>r(e.target.value),autoCapitalize:"none",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:h?"text":"password",autoComplete:"new-password",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter your password",value:t,onChange:e=>d(e.target.value),autoCapitalize:"none",autoCorrect:"off",spellCheck:"false"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target",onClick:()=>x(!h),"aria-label":h?"Hide password":"Show password",children:h?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,a.jsx)("input",{id:"fullName",name:"fullName",type:"text",autoComplete:"name",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter your full name",value:b,onChange:e=>p(e.target.value),autoCapitalize:"words",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phoneNumber",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{id:"phoneNumber",name:"phoneNumber",type:"tel",autoComplete:"tel",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter your phone number",value:f,onChange:e=>g(e.target.value),autoCapitalize:"none",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"I want to:"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target",children:[(0,a.jsx)("input",{id:"rider",name:"role",type:"radio",checked:"rider"===v,onChange:()=>w("rider"),className:"focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300"}),(0,a.jsx)("label",{htmlFor:"rider",className:"ml-3 block text-base text-gray-900 cursor-pointer flex-1",children:"Book rides (Rider)"})]}),(0,a.jsxs)("div",{className:"flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target",children:[(0,a.jsx)("input",{id:"driver",name:"role",type:"radio",checked:"driver"===v,onChange:()=>w("driver"),className:"focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300"}),(0,a.jsx)("label",{htmlFor:"driver",className:"ml-3 block text-base text-gray-900 cursor-pointer flex-1",children:"Drive (Driver)"})]})]})]}),"driver"===v&&(0,a.jsxs)("div",{className:"space-y-6 border-t-2 border-gray-200 pt-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Vehicle Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleMake",className:"block text-sm font-medium text-gray-700 mb-2",children:"Vehicle Make"}),(0,a.jsx)("input",{id:"vehicleMake",name:"vehicleMake",type:"text",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"e.g., Toyota, Honda, Ford",value:S,onChange:e=>R(e.target.value),autoCapitalize:"words",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleModel",className:"block text-sm font-medium text-gray-700 mb-2",children:"Vehicle Model"}),(0,a.jsx)("input",{id:"vehicleModel",name:"vehicleModel",type:"text",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"e.g., Camry, Civic, Focus",value:P,onChange:e=>E(e.target.value),autoCapitalize:"words",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleColor",className:"block text-sm font-medium text-gray-700 mb-2",children:"Vehicle Color"}),(0,a.jsx)("input",{id:"vehicleColor",name:"vehicleColor",type:"text",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"e.g., White, Black, Silver",value:M,onChange:e=>F(e.target.value),autoCapitalize:"words",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"licensePlate",className:"block text-sm font-medium text-gray-700 mb-2",children:"License Plate Number"}),(0,a.jsx)("input",{id:"licensePlate",name:"licensePlate",type:"text",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter license plate number",value:B,onChange:e=>A(e.target.value),autoCapitalize:"characters",autoCorrect:"off",spellCheck:"false"})]}),(0,a.jsxs)("div",{className:"border-t-2 border-gray-200 pt-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Driver Verification"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"baroRideIdPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"BaroRide ID Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"baroRideIdPassword",name:"baroRideIdPassword",type:L?"text":"password",required:!0,className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors",placeholder:"Enter BaroRide ID Password",value:D,onChange:e=>I(e.target.value),autoCapitalize:"none",autoCorrect:"off",spellCheck:"false"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target",onClick:()=>_(!L),"aria-label":L?"Hide password":"Show password",children:L?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"This password is required for all driver registrations. Contact support if you don't have it."})]})]})]}),(0,a.jsx)("button",{type:"submit",disabled:N,className:"mobile-button w-full py-4 px-6 rounded-lg font-semibold text-base transition-all duration-200 touch-target ".concat(N?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl"," text-white flex items-center justify-center mt-8"),children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mobile-spinner mr-3"}),"Creating Account..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})}),"Create Account"]})})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)("a",{href:"/login",className:"text-blue-600 hover:text-blue-700 font-semibold transition-colors touch-target",children:"Sign in here"})]})})]})})]})]})}},8082:function(e,r,t){"use strict";t.d(r,{So:function(){return c},XL:function(){return i},xT:function(){return n}});var a=t(404),l=t(1517),s=t(109);let o=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),i=e=>{let r=e.replace(/[^\d+]/g,"");return r.startsWith("+")?"+"+r.substring(1).replace(/\+/g,""):r.replace(/\+/g,"")},n=e=>{let r=i(e);return/^(\+\d{1,3})?\d{7,15}$/.test(r)||[/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,/^[+]?[0-9]{1,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}$/,/^[0-9]{10,15}$/,/^\+[0-9]{7,15}$/].some(r=>r.test(e))},d=async e=>{try{let r=(0,s.hJ)(a.db,"users"),t=(0,s.IO)(r,(0,s.ar)("phoneNumber","==",e)),l=await (0,s.PL)(t);if(!l.empty){let e=l.docs[0];return{id:e.id,...e.data()}}let o=i(e),n=(0,s.IO)(r);for(let e of(await (0,s.PL)(n)).docs){let r=e.data();if(r.phoneNumber&&i(r.phoneNumber)===o)return{id:e.id,...r}}return null}catch(e){return console.error("Error finding user by phone number:",e),null}},c=async(e,r)=>{try{let t,i;if(o(e)){t=await (0,l.e5)(a.auth,e,r);let o=await (0,s.QT)((0,s.doc)(a.db,"users",t.user.uid));i={id:o.id,...o.data()}}else if(n(e)){let s=await d(e);if(!s)throw Error("User not found with this phone number");t=await (0,l.e5)(a.auth,s.email,r),i=s}else throw Error("Invalid identifier format. Please enter a valid email or phone number.");return i}catch(e){throw console.error("Authentication error:",e),e}}},9008:function(e,r,t){e.exports=t(3867)}},function(e){e.O(0,[888,774,179],function(){return e(e.s=7805)}),_N_E=e.O()}]);