"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[994],{7994:function(e,t,s){s.d(t,{Z:function(){return p}});var a=s(5893),r=s(9008),l=s.n(r),i=s(7294),n=s(1664),o=s.n(n),c=s(1163),d=s(837),x=s(404),h=s(109);function m(){let{user:e}=(0,d.a)(),[t,s]=(0,i.useState)([]),[r,l]=(0,i.useState)(!1),[n,o]=(0,i.useState)(0);(0,i.useEffect)(()=>{if(!e)return;let t=(0,h.IO)((0,h.hJ)(x.db,"notifications"),(0,h.ar)("userId","==",e.id)),a=(0,h.cf)(t,e=>{let t=[],a=0;e.forEach(e=>{var s;let r=e.data(),l={id:e.id,...r,createdAt:(null===(s=r.createdAt)||void 0===s?void 0:s.toDate())||new Date};t.push(l),!l.read&&a++}),t.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime()),s(t),o(a)});return()=>a()},[e]);let c=async t=>{if(e)try{await (0,h.r7)((0,h.JU)(x.db,"notifications",t),{read:!0})}catch(e){console.error("Error marking notification as read:",e)}},m=async()=>{if(e)try{let e=t.filter(e=>!e.read).map(e=>(0,h.r7)((0,h.JU)(x.db,"notifications",e.id),{read:!0}));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}},u=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"warning":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-blue-100 text-blue-800"}};return e?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>{l(!r)},className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),n>0&&(0,a.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:n})]}),r&&(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20",children:(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("div",{className:"px-4 py-2 bg-gray-100 flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Notifications"}),n>0&&(0,a.jsx)("button",{onClick:m,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===t.length?(0,a.jsx)("div",{className:"px-4 py-3 text-sm text-gray-500",children:"No notifications"}):t.map(e=>(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-100 ".concat(e.read?"":"bg-blue-50"),onClick:()=>c(e.id),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsxs)("div",{className:"flex-shrink-0 rounded-full p-1 ".concat(u(e.type)),children:["success"===e.type&&(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"warning"===e.type&&(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),"error"===e.type&&(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),"info"===e.type&&(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})]}),(0,a.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900 whitespace-pre-line",children:e.message}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()}),e.driverDetails&&(0,a.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs font-semibold",children:e.driverDetails.fullName}),(0,a.jsxs)("p",{className:"text-xs",children:[e.driverDetails.vehicleColor," ",e.driverDetails.vehicleMake," ",e.driverDetails.vehicleModel]}),(0,a.jsxs)("p",{className:"text-xs font-medium",children:["License: ",e.driverDetails.licensePlate]})]})]})})]})]})},e.id))})]})})]}):null}function u(){let{user:e,signOut:t}=(0,d.a)(),[s,r]=(0,i.useState)(!1),[l,n]=(0,i.useState)(!1),x=(0,c.useRouter)();(0,i.useEffect)(()=>{n("/"!==x.pathname)},[x.pathname]);let h=()=>{r(!s)};return(0,a.jsxs)("nav",{className:"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50",children:[(0,a.jsx)("div",{className:"container mx-auto px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"flex justify-between h-14 sm:h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[l&&(0,a.jsx)("button",{onClick:()=>{x.back()},className:"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors","aria-label":"Go back",style:{touchAction:"manipulation"},children:(0,a.jsx)("svg",{className:"w-5 h-5 sm:w-7 sm:h-7 text-gray-700",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,a.jsx)(o(),{href:"/",className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-bold text-lg sm:text-xl",children:"BaroRide"})})]}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:e?(0,a.jsxs)(a.Fragment,{children:["rider"===e.role?(0,a.jsx)(o(),{href:"/book",className:"text-gray-700 hover:text-blue-600",children:"Book a Ride"}):(0,a.jsx)(o(),{href:"/driver/dashboard",className:"text-gray-700 hover:text-blue-600",children:"Dashboard"}),(0,a.jsx)(m,{}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:h,className:"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none",children:[(0,a.jsx)("span",{className:"mr-1",children:e.fullName}),(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),s&&(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:(0,a.jsx)("button",{onClick:t,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign Out"})})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login",className:"text-gray-700 hover:text-blue-600",children:"Login"}),(0,a.jsx)(o(),{href:"/signup",className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Sign Up"})]})}),(0,a.jsxs)("div",{className:"md:hidden flex items-center space-x-1",children:[e&&(0,a.jsx)(m,{}),(0,a.jsx)("button",{onClick:h,className:"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors",style:{touchAction:"manipulation"},"aria-label":"Toggle menu",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})})]})]})}),s&&(0,a.jsx)("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,a.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100 mb-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.fullName}),(0,a.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.role})]}),"rider"===e.role?(0,a.jsx)(o(),{href:"/book",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDCF1 Book a Ride"}):"driver"===e.role?(0,a.jsx)(o(),{href:"/driver/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDE97 Driver Dashboard"}):(0,a.jsx)(o(),{href:"/admin/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"⚙️ Admin Dashboard"}),(0,a.jsx)("button",{onClick:()=>{t(),r(!1)},className:"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},children:"\uD83D\uDEAA Sign Out"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/login",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDD11 Login"}),(0,a.jsx)(o(),{href:"/signup",className:"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"✨ Sign Up"})]})})})]})}function p(e){let{children:t,title:s="BaroRide"}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l(),{children:[(0,a.jsx)("title",{children:s}),(0,a.jsx)("meta",{name:"description",content:"Book your ride with BaroRide - fixed fares and reliable service"}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"}),(0,a.jsx)("meta",{name:"theme-color",content:"#2563eb"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,a.jsx)("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),(0,a.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,a.jsx)("link",{rel:"apple-touch-icon",href:"/favicon.ico"})]}),(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-white text-gray-900 overflow-x-hidden",children:[(0,a.jsx)(u,{}),(0,a.jsx)("main",{className:"flex-grow w-full",children:t}),(0,a.jsx)("footer",{className:"bg-gray-100 border-t border-gray-200 py-4 mt-auto",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center text-gray-600 text-sm",children:["\xa9 ",new Date().getFullYear()," BaroRide. All rights reserved."]})})]})]})}}}]);