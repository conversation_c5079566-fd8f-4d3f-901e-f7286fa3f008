"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[302],{3302:function(e,o,t){t.d(o,{Z:function(){return i}});var n=t(5893),l=t(7294),a=t(445);function i(e){let{height:o="300px",selectable:t=!1,onLocationSelected:i,initialLocation:r}=e,c=(0,l.useRef)(null),[s,d]=(0,l.useState)(!1),p=(0,l.useRef)(null),[u]=(0,l.useState)(()=>(0,a.dz)()),g=e=>{p.current&&e&&0!==e.lat&&0!==e.lng&&(document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([e.lng,e.lat]).addTo(p.current),p.current.flyTo({center:[e.lng,e.lat],zoom:14,essential:!0}),e.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([e.lng,e.lat]).setHTML('<p style="margin: 0;">'.concat(e.address,"</p>")).addTo(p.current))};return(0,l.useEffect)(()=>{let e=()=>{if(!c.current||!window.mapboxgl)return;window.mapboxgl.accessToken="pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A";let e=34.5925,o=8.2483;r&&0!==r.lat&&0!==r.lng&&(e=r.lng,o=r.lat),console.log("Using manual location selection only - automatic geolocation disabled");let n=new window.mapboxgl.Map({container:c.current,style:"mapbox://styles/mapbox/streets-v11",center:[e,o],zoom:u.isMobile?12:13,touchZoomRotate:u.isMobile,touchPitch:u.isMobile,dragRotate:!u.isMobile,pitchWithRotate:!u.isMobile,doubleClickZoom:!0,scrollZoom:!0,boxZoom:!u.isMobile,dragPan:!0,keyboard:!u.isMobile,cooperativeGestures:u.isMobile});p.current=n;let l=new window.mapboxgl.NavigationControl({showCompass:!u.isMobile,showZoom:!0,visualizePitch:!u.isMobile});if(n.addControl(l,u.isMobile?"bottom-right":"top-right"),u.isMobile&&c.current&&(0,a.t9)(c.current),r&&0!==r.lat&&0!==r.lng?(new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([r.lng,r.lat]).addTo(n),r.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([r.lng,r.lat]).setHTML('<p style="margin: 0;">'.concat(r.address,"</p>")).addTo(n)):new window.mapboxgl.Marker().setLngLat([e,o]).addTo(n),t&&i){n.getCanvas().style.cursor="crosshair";let e=async e=>{let{lng:o,lat:t}=e.lngLat;console.log("Location selected:",{lng:o,lat:t}),document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),document.querySelectorAll(".mapboxgl-popup").forEach(e=>e.remove());let l=new window.mapboxgl.Marker({color:"#3b82f6",scale:1.2}).setLngLat([o,t]).addTo(n);d(!0);try{let e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(o,",").concat(t,".json?access_token=").concat(window.mapboxgl.accessToken),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Geocoding failed: ".concat(e.status));let a=await e.json();console.log("Geocoding response:",a);let r="Selected location";a.features&&a.features.length>0&&(r=a.features[0].place_name);let c=new window.mapboxgl.Popup({offset:25,closeButton:!0,closeOnClick:!1,maxWidth:"300px"}).setLngLat([o,t]).setHTML('\n                <div style="text-align: center; padding: 8px;">\n                  <p style="margin: 0 0 12px 0; font-weight: 500; color: #374151;">'.concat(r,'</p>\n                  <div style="display: flex; gap: 8px; justify-content: center;">\n                    <button id="select-location" style="\n                      background-color: #3b82f6;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      font-weight: 500;\n                      min-height: 44px;\n                      min-width: 100px;\n                      touch-action: manipulation;\n                    ">\n                      ✓ Select\n                    </button>\n                    <button id="cancel-location" style="\n                      background-color: #6b7280;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      font-weight: 500;\n                      min-height: 44px;\n                      min-width: 80px;\n                      touch-action: manipulation;\n                    ">\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              ')).addTo(n);setTimeout(()=>{let e=document.getElementById("select-location"),n=document.getElementById("cancel-location");e&&(e.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),console.log("Select button clicked, calling onLocationSelected"),i({lat:t,lng:o,address:r}),c.remove()}),e.addEventListener("touchend",e=>{e.preventDefault(),e.stopPropagation(),console.log("Select button touched, calling onLocationSelected"),i({lat:t,lng:o,address:r}),c.remove()})),n&&(n.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),c.remove(),l.remove()}),n.addEventListener("touchend",e=>{e.preventDefault(),e.stopPropagation(),c.remove(),l.remove()}))},100)}catch(l){console.error("Error geocoding location:",l);let e=new window.mapboxgl.Popup({offset:25,closeButton:!0,closeOnClick:!1}).setLngLat([o,t]).setHTML('\n                <div style="text-align: center; padding: 8px;">\n                  <p style="margin: 0 0 12px 0; color: #374151;">Selected location</p>\n                  <p style="margin: 0 0 12px 0; font-size: 12px; color: #6b7280;">Coordinates: '.concat(t.toFixed(6),", ").concat(o.toFixed(6),'</p>\n                  <div style="display: flex; gap: 8px; justify-content: center;">\n                    <button id="select-location-fallback" style="\n                      background-color: #3b82f6;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      min-height: 44px;\n                      touch-action: manipulation;\n                    ">\n                      ✓ Select\n                    </button>\n                  </div>\n                </div>\n              ')).addTo(n);setTimeout(()=>{let n=document.getElementById("select-location-fallback");if(n){let l=n=>{n.preventDefault(),n.stopPropagation(),i({lat:t,lng:o,address:"Selected location"}),e.remove()};n.addEventListener("click",l),n.addEventListener("touchend",l)}},100)}finally{d(!1)}};n.on("click",e),n.on("touchend",e)}else n.getCanvas().style.cursor="";return()=>n.remove()};(()=>{if(window.mapboxgl){e();return}let o=document.createElement("script");o.src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js",o.async=!0,o.onload=()=>{e()},document.head.appendChild(o);let t=document.createElement("link");t.href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css",t.rel="stylesheet",document.head.appendChild(t)})()},[t,i]),(0,l.useEffect)(()=>{if(r&&0!==r.lat&&0!==r.lng){let e=setInterval(()=>{p.current&&(g(r),clearInterval(e))},100);setTimeout(()=>clearInterval(e),5e3)}},[r]),(0,n.jsxs)("div",{style:{position:"relative"},children:[(0,n.jsx)("div",{ref:c,style:{width:"100%",height:o,position:"relative",border:"1px solid #ddd",borderRadius:"4px"}}),s&&(0,n.jsx)("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.2)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:10},children:(0,n.jsx)("div",{style:{backgroundColor:"white",padding:"12px",borderRadius:"50%",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",opacity:"0.25"}),(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",strokeDasharray:"60 30",style:{animation:"spin 1s linear infinite"}}),(0,n.jsx)("style",{children:"\n                @keyframes spin {\n                  0% { transform: rotate(0deg); }\n                  100% { transform: rotate(360deg); }\n                }\n              "})]})})}),t&&(0,n.jsx)("div",{style:{position:"absolute",top:"10px",left:"10px",right:"10px",backgroundColor:"#3b82f6",color:"white",padding:"12px 16px",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",zIndex:5,fontSize:"14px",fontWeight:"500",textAlign:"center",border:"2px solid #1e40af"},children:"\uD83D\uDCCD Tap anywhere on the map to select your pickup location"})]})}}}]);