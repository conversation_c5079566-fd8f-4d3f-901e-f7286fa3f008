import { auth, db } from '@/firebase/config';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import type { User } from '@/types/user';

/**
 * Validates if the input is an email address
 * @param input The string to check
 * @returns True if the input is a valid email address
 */
export const isEmail = (input: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(input);
};

/**
 * Normalizes a phone number by removing all non-digit characters except the leading +
 * @param phoneNumber The phone number to normalize
 * @returns Normalized phone number
 */
export const normalizePhoneNumber = (phoneNumber: string): string => {
  // Remove all non-digit characters except the leading +
  let normalized = phoneNumber.replace(/[^\d+]/g, '');

  // If it starts with +, keep it, otherwise remove any + in the middle
  if (normalized.startsWith('+')) {
    normalized = '+' + normalized.substring(1).replace(/\+/g, '');
  } else {
    normalized = normalized.replace(/\+/g, '');
  }

  return normalized;
};

/**
 * Validates if the input is a phone number
 * @param input The string to check
 * @returns True if the input is a valid phone number
 */
export const isPhoneNumber = (input: string): boolean => {
  // Normalize the input first
  const normalized = normalizePhoneNumber(input);

  // More comprehensive phone number validation
  // Supports international format (+country code) and local formats
  const phoneRegex = /^(\+\d{1,3})?\d{7,15}$/;

  // Also check for common formats without normalization
  const commonFormats = [
    /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/, // US format
    /^[+]?[0-9]{1,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}$/, // International
    /^[0-9]{10,15}$/, // Simple digit format
    /^\+[0-9]{7,15}$/ // International with +
  ];

  return phoneRegex.test(normalized) || commonFormats.some(regex => regex.test(input));
};

/**
 * Finds a user by phone number
 * @param phoneNumber The phone number to search for
 * @returns The user document if found, null otherwise
 */
export const findUserByPhoneNumber = async (phoneNumber: string): Promise<User | null> => {
  try {
    const usersRef = collection(db, 'users');

    // First, try to find with the exact phone number as entered
    let q = query(usersRef, where('phoneNumber', '==', phoneNumber));
    let querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      return { id: userDoc.id, ...userDoc.data() } as User;
    }

    // If not found, try with normalized phone number
    const normalizedInput = normalizePhoneNumber(phoneNumber);

    // Get all users and check for phone number matches with normalization
    const allUsersQuery = query(usersRef);
    const allUsersSnapshot = await getDocs(allUsersQuery);

    for (const userDoc of allUsersSnapshot.docs) {
      const userData = userDoc.data();
      if (userData.phoneNumber) {
        const normalizedStored = normalizePhoneNumber(userData.phoneNumber);
        if (normalizedStored === normalizedInput) {
          return { id: userDoc.id, ...userData } as User;
        }
      }
    }

    return null;
  } catch (error) {
    console.error('Error finding user by phone number:', error);
    return null;
  }
};

/**
 * Authenticates a user with either email or phone number
 * @param identifier Email or phone number
 * @param password User password
 * @returns User data if authentication is successful
 */
export const authenticateUser = async (identifier: string, password: string): Promise<User> => {
  try {
    let userCredential;
    let userData: User;

    if (isEmail(identifier)) {
      // If the identifier is an email, use Firebase's email authentication
      userCredential = await signInWithEmailAndPassword(auth, identifier, password);

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
      userData = { id: userDoc.id, ...userDoc.data() } as User;
    } else if (isPhoneNumber(identifier)) {
      // If the identifier is a phone number, find the user first
      const user = await findUserByPhoneNumber(identifier);

      if (!user) {
        throw new Error('User not found with this phone number');
      }

      // Then authenticate with the associated email
      userCredential = await signInWithEmailAndPassword(auth, user.email, password);
      userData = user;
    } else {
      throw new Error('Invalid identifier format. Please enter a valid email or phone number.');
    }

    return userData;
  } catch (error) {
    console.error('Authentication error:', error);
    throw error;
  }
};
