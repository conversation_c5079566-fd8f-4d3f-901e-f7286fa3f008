import { db } from '@/firebase/config';
import { doc, updateDoc, getDoc } from 'firebase/firestore';

interface MFACode {
  code: string;
  expiresAt: number;
  attempts: number;
  used: boolean;
  createdAt: number;
}

interface MFASession {
  userId: string;
  phoneNumber: string;
  code: MFACode;
  verified: boolean;
}

class MFAManager {
  private sessions: Map<string, MFASession> = new Map();
  private readonly CODE_LENGTH = 6;
  private readonly CODE_EXPIRY = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_ATTEMPTS = 3;
  private readonly RESEND_COOLDOWN = 60 * 1000; // 1 minute

  // Generate a secure random code
  private generateCode(): string {
    const digits = '0123456789';
    let code = '';

    // Use crypto.getRandomValues for secure random generation
    const array = new Uint8Array(this.CODE_LENGTH);
    if (typeof window !== 'undefined' && window.crypto) {
      window.crypto.getRandomValues(array);
    } else {
      // Fallback for server-side - use Math.random for build compatibility
      for (let i = 0; i < this.CODE_LENGTH; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }

    for (let i = 0; i < this.CODE_LENGTH; i++) {
      code += digits[array[i] % digits.length];
    }

    return code;
  }

  // Send MFA code via SMS (mock implementation)
  private async sendSMS(phoneNumber: string, code: string): Promise<boolean> {
    try {
      // In a real implementation, you would integrate with an SMS service like:
      // - Twilio
      // - AWS SNS
      // - Firebase Cloud Functions with SMS provider

      console.log(`[MFA] Sending code ${code} to ${phoneNumber}`);

      // Mock SMS sending - in production, replace with actual SMS service
      if (process.env.NODE_ENV === 'development') {
        console.log(`SMS Code for ${phoneNumber}: ${code}`);
        return true;
      }

      // Example Twilio integration (commented out):
      /*
      const twilio = require('twilio');
      const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_AUTH_TOKEN);

      await client.messages.create({
        body: `Your BaroRide verification code is: ${code}. This code expires in 10 minutes.`,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phoneNumber
      });
      */

      return true;
    } catch (error) {
      console.error('Failed to send SMS:', error);
      return false;
    }
  }

  // Generate and send MFA code
  public async generateMFACode(userId: string, phoneNumber: string): Promise<{
    success: boolean;
    sessionId?: string;
    error?: string;
    retryAfter?: number;
  }> {
    try {
      // Check for existing session
      const existingSession = Array.from(this.sessions.entries())
        .find(([_, session]) => session.userId === userId);

      if (existingSession) {
        const [, session] = existingSession;
        const timeSinceCreated = Date.now() - session.code.createdAt;

        // Enforce resend cooldown
        if (timeSinceCreated < this.RESEND_COOLDOWN) {
          const retryAfter = Math.ceil((this.RESEND_COOLDOWN - timeSinceCreated) / 1000);
          return {
            success: false,
            error: `Please wait ${retryAfter} seconds before requesting a new code`,
            retryAfter
          };
        }
      }

      // Generate new code
      const code = this.generateCode();
      const now = Date.now();
      const sessionId = `mfa_${userId}_${now}`;

      const mfaSession: MFASession = {
        userId,
        phoneNumber,
        code: {
          code,
          expiresAt: now + this.CODE_EXPIRY,
          attempts: 0,
          used: false,
          createdAt: now
        },
        verified: false
      };

      // Send SMS
      const smsSent = await this.sendSMS(phoneNumber, code);
      if (!smsSent) {
        return {
          success: false,
          error: 'Failed to send verification code. Please try again.'
        };
      }

      // Store session
      this.sessions.set(sessionId, mfaSession);

      // Clean up old sessions for this user
      this.cleanupUserSessions(userId, sessionId);

      return {
        success: true,
        sessionId
      };

    } catch (error) {
      console.error('MFA code generation failed:', error);
      return {
        success: false,
        error: 'Failed to generate verification code'
      };
    }
  }

  // Verify MFA code
  public async verifyMFACode(sessionId: string, inputCode: string): Promise<{
    success: boolean;
    error?: string;
    attemptsRemaining?: number;
  }> {
    const session = this.sessions.get(sessionId);

    if (!session) {
      return {
        success: false,
        error: 'Invalid or expired verification session'
      };
    }

    const now = Date.now();

    // Check if code has expired
    if (now > session.code.expiresAt) {
      this.sessions.delete(sessionId);
      return {
        success: false,
        error: 'Verification code has expired. Please request a new one.'
      };
    }

    // Check if code has been used
    if (session.code.used) {
      return {
        success: false,
        error: 'This verification code has already been used'
      };
    }

    // Check attempts limit
    if (session.code.attempts >= this.MAX_ATTEMPTS) {
      this.sessions.delete(sessionId);
      return {
        success: false,
        error: 'Too many failed attempts. Please request a new verification code.'
      };
    }

    // Increment attempts
    session.code.attempts++;

    // Verify code
    if (session.code.code !== inputCode.trim()) {
      const attemptsRemaining = this.MAX_ATTEMPTS - session.code.attempts;

      if (attemptsRemaining === 0) {
        this.sessions.delete(sessionId);
        return {
          success: false,
          error: 'Invalid verification code. Maximum attempts exceeded.'
        };
      }

      return {
        success: false,
        error: 'Invalid verification code',
        attemptsRemaining
      };
    }

    // Code is valid
    session.code.used = true;
    session.verified = true;

    // Update user's MFA status in database
    try {
      await this.updateUserMFAStatus(session.userId, true);
    } catch (error) {
      console.error('Failed to update user MFA status:', error);
    }

    return {
      success: true
    };
  }

  // Update user's MFA verification status in Firestore
  private async updateUserMFAStatus(userId: string, verified: boolean): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        mfaVerified: verified,
        mfaVerifiedAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Failed to update MFA status in database:', error);
      throw error;
    }
  }

  // Check if user has completed MFA
  public async isUserMFAVerified(userId: string): Promise<boolean> {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        return false;
      }

      const userData = userDoc.data();
      return userData.mfaVerified === true;
    } catch (error) {
      console.error('Failed to check MFA status:', error);
      return false;
    }
  }

  // Clean up old sessions for a user
  private cleanupUserSessions(userId: string, excludeSessionId?: string): void {
    const sessionsToDelete: string[] = [];

    this.sessions.forEach((session, sessionId) => {
      if (session.userId === userId && sessionId !== excludeSessionId) {
        sessionsToDelete.push(sessionId);
      }
    });

    sessionsToDelete.forEach(sessionId => {
      this.sessions.delete(sessionId);
    });
  }

  // Clean up expired sessions
  public cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    this.sessions.forEach((session, sessionId) => {
      if (now > session.code.expiresAt) {
        expiredSessions.push(sessionId);
      }
    });

    expiredSessions.forEach(sessionId => {
      this.sessions.delete(sessionId);
    });
  }

  // Get session info (for debugging/admin purposes)
  public getSessionInfo(sessionId: string): Partial<MFASession> | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    // Return session info without the actual code
    return {
      userId: session.userId,
      phoneNumber: session.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'), // Mask phone number
      verified: session.verified,
      code: {
        ...session.code,
        code: '******' // Hide actual code
      }
    };
  }
}

// Create singleton instance
export const mfaManager = new MFAManager();

// Clean up expired sessions every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    mfaManager.cleanupExpiredSessions();
  }, 5 * 60 * 1000);
}

export default mfaManager;
