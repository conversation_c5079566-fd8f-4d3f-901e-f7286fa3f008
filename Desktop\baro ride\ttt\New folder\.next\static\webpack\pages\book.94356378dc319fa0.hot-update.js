"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/book",{

/***/ "./src/components/MobileOptimizer.tsx":
/*!********************************************!*\
  !*** ./src/components/MobileOptimizer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileStyles: function() { return /* binding */ MobileStyles; },\n/* harmony export */   \"default\": function() { return /* binding */ UniversalDeviceOptimizer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/mobile-optimization */ \"./src/utils/mobile-optimization.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction UniversalDeviceOptimizer(param) {\n    let { children } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if ( false || typeof document === \"undefined\") {\n            return;\n        }\n        // Initialize mobile optimizations when component mounts\n        (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n        // Add device-specific classes to body\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        // Clean up existing device classes\n        document.body.classList.remove(\"is-mobile\", \"is-desktop\", \"is-tablet\", \"is-ios\", \"is-android\", \"has-touch\", \"no-touch\");\n        // Add current device classes\n        const classesToAdd = [\n            deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\",\n            deviceInfo.isTablet ? \"is-tablet\" : \"\",\n            deviceInfo.isIOS ? \"is-ios\" : \"\",\n            deviceInfo.isAndroid ? \"is-android\" : \"\",\n            deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\"\n        ].filter(Boolean); // Remove empty strings\n        document.body.classList.add(...classesToAdd);\n        // Add mobile-specific meta tags if on mobile\n        if (deviceInfo.isMobile) {\n            // Ensure viewport meta tag is properly set\n            let viewportMeta = document.querySelector('meta[name=\"viewport\"]');\n            if (!viewportMeta) {\n                viewportMeta = document.createElement(\"meta\");\n                viewportMeta.setAttribute(\"name\", \"viewport\");\n                document.head.appendChild(viewportMeta);\n            }\n            viewportMeta.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\");\n            // Add mobile web app meta tags\n            const addMetaTag = (name, content)=>{\n                let meta = document.querySelector('meta[name=\"'.concat(name, '\"]'));\n                if (!meta) {\n                    meta = document.createElement(\"meta\");\n                    meta.setAttribute(\"name\", name);\n                    document.head.appendChild(meta);\n                }\n                meta.setAttribute(\"content\", content);\n            };\n            addMetaTag(\"mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-status-bar-style\", \"default\");\n            addMetaTag(\"apple-mobile-web-app-title\", \"BaroRide\");\n            addMetaTag(\"theme-color\", \"#1e3a5f\");\n            addMetaTag(\"format-detection\", \"telephone=no\");\n        }\n        // Handle orientation changes\n        const handleOrientationChange = ()=>{\n            // Re-initialize optimizations after orientation change\n            setTimeout(()=>{\n                (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n            }, 100);\n        };\n        window.addEventListener(\"orientationchange\", handleOrientationChange);\n        window.addEventListener(\"resize\", handleOrientationChange);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", handleOrientationChange);\n            window.removeEventListener(\"resize\", handleOrientationChange);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(UniversalDeviceOptimizer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = UniversalDeviceOptimizer;\n// CSS-in-JS styles for mobile optimizations\nconst MobileStyles = ()=>{\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if ( false || typeof document === \"undefined\") {\n            return;\n        }\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        if (deviceInfo.isMobile) {\n            // Add mobile-specific styles\n            const style = document.createElement(\"style\");\n            style.textContent = \"\\n        /* Mobile-specific overrides */\\n        .is-mobile input,\\n        .is-mobile select,\\n        .is-mobile textarea {\\n          font-size: 16px !important;\\n          -webkit-appearance: none;\\n          -moz-appearance: none;\\n          appearance: none;\\n        }\\n\\n        .is-mobile button {\\n          min-height: 44px;\\n          touch-action: manipulation;\\n          -webkit-tap-highlight-color: transparent;\\n        }\\n\\n        .is-mobile .map-container {\\n          touch-action: none;\\n        }\\n\\n        /* Prevent zoom on input focus */\\n        .is-mobile input:focus,\\n        .is-mobile select:focus,\\n        .is-mobile textarea:focus {\\n          font-size: 16px !important;\\n        }\\n\\n        /* Better scrolling on mobile */\\n        .is-mobile {\\n          -webkit-overflow-scrolling: touch;\\n        }\\n\\n        /* Hide scrollbars on mobile for cleaner look */\\n        .is-mobile ::-webkit-scrollbar {\\n          width: 0px;\\n          background: transparent;\\n        }\\n\\n        /* Mobile-specific form improvements */\\n        .is-mobile .mobile-form {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-form input,\\n        .is-mobile .mobile-form select,\\n        .is-mobile .mobile-form textarea {\\n          padding: 16px;\\n          border-radius: 12px;\\n          border: 2px solid #e5e7eb;\\n          font-size: 16px !important;\\n        }\\n\\n        .is-mobile .mobile-form button {\\n          padding: 16px;\\n          border-radius: 12px;\\n          font-size: 16px;\\n          font-weight: 600;\\n        }\\n\\n        /* Mobile navigation improvements */\\n        .is-mobile .mobile-nav {\\n          position: sticky;\\n          top: 0;\\n          z-index: 50;\\n          background: white;\\n          border-bottom: 1px solid #e5e7eb;\\n          padding: 12px 16px;\\n        }\\n\\n        /* Mobile card improvements */\\n        .is-mobile .mobile-card {\\n          margin: 8px;\\n          border-radius: 16px;\\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n        }\\n\\n        /* Mobile modal improvements */\\n        .is-mobile .mobile-modal {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-modal-content {\\n          border-radius: 16px;\\n          max-height: 85vh;\\n        }\\n\\n        /* Mobile table improvements */\\n        .is-mobile .mobile-table {\\n          font-size: 14px;\\n        }\\n\\n        .is-mobile .mobile-table th,\\n        .is-mobile .mobile-table td {\\n          padding: 12px 8px;\\n        }\\n\\n        /* Mobile notification improvements */\\n        .is-mobile .mobile-notification {\\n          margin: 16px;\\n          border-radius: 12px;\\n          padding: 16px;\\n          font-size: 16px;\\n        }\\n\\n        /* Keyboard handling */\\n        .is-mobile.keyboard-open {\\n          position: fixed;\\n          width: 100%;\\n        }\\n\\n        /* Safe area handling for devices with notches */\\n        .is-mobile .safe-area-top {\\n          padding-top: max(16px, env(safe-area-inset-top));\\n        }\\n\\n        .is-mobile .safe-area-bottom {\\n          padding-bottom: max(16px, env(safe-area-inset-bottom));\\n        }\\n\\n        .is-mobile .safe-area-left {\\n          padding-left: max(16px, env(safe-area-inset-left));\\n        }\\n\\n        .is-mobile .safe-area-right {\\n          padding-right: max(16px, env(safe-area-inset-right));\\n        }\\n      \";\n            document.head.appendChild(style);\n            return ()=>{\n                document.head.removeChild(style);\n            };\n        }\n    }, []);\n    return null;\n};\n_s1(MobileStyles, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = MobileStyles;\nvar _c, _c1;\n$RefreshReg$(_c, \"UniversalDeviceOptimizer\");\n$RefreshReg$(_c1, \"MobileStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9Nb2JpbGVPcHRpbWl6ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFrQztBQUN5RDtBQU01RSxTQUFTRyx5QkFBeUIsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDL0NKLGdEQUFTQSxDQUFDO1FBQ1Isa0NBQWtDO1FBQ2xDLElBQUksTUFBa0IsSUFBZSxPQUFPSyxhQUFhLGFBQWE7WUFDcEU7UUFDRjtRQUVBLHdEQUF3RDtRQUN4REoseUZBQTZCQTtRQUU3QixzQ0FBc0M7UUFDdEMsTUFBTUssYUFBYUoseUVBQWFBO1FBRWhDLG1DQUFtQztRQUNuQ0csU0FBU0UsSUFBSSxDQUFDQyxTQUFTLENBQUNDLE1BQU0sQ0FDNUIsYUFBYSxjQUFjLGFBQWEsVUFBVSxjQUFjLGFBQWE7UUFHL0UsNkJBQTZCO1FBQzdCLE1BQU1DLGVBQWU7WUFDbkJKLFdBQVdLLFFBQVEsR0FBRyxjQUFjO1lBQ3BDTCxXQUFXTSxRQUFRLEdBQUcsY0FBYztZQUNwQ04sV0FBV08sS0FBSyxHQUFHLFdBQVc7WUFDOUJQLFdBQVdRLFNBQVMsR0FBRyxlQUFlO1lBQ3RDUixXQUFXUyxZQUFZLEdBQUcsY0FBYztTQUN6QyxDQUFDQyxNQUFNLENBQUNDLFVBQVUsdUJBQXVCO1FBRTFDWixTQUFTRSxJQUFJLENBQUNDLFNBQVMsQ0FBQ1UsR0FBRyxJQUFJUjtRQUUvQiw2Q0FBNkM7UUFDN0MsSUFBSUosV0FBV0ssUUFBUSxFQUFFO1lBQ3ZCLDJDQUEyQztZQUMzQyxJQUFJUSxlQUFlZCxTQUFTZSxhQUFhLENBQUM7WUFDMUMsSUFBSSxDQUFDRCxjQUFjO2dCQUNqQkEsZUFBZWQsU0FBU2dCLGFBQWEsQ0FBQztnQkFDdENGLGFBQWFHLFlBQVksQ0FBQyxRQUFRO2dCQUNsQ2pCLFNBQVNrQixJQUFJLENBQUNDLFdBQVcsQ0FBQ0w7WUFDNUI7WUFDQUEsYUFBYUcsWUFBWSxDQUN2QixXQUNBO1lBR0YsK0JBQStCO1lBQy9CLE1BQU1HLGFBQWEsQ0FBQ0MsTUFBY0M7Z0JBQ2hDLElBQUlDLE9BQU92QixTQUFTZSxhQUFhLENBQUMsY0FBbUIsT0FBTE0sTUFBSztnQkFDckQsSUFBSSxDQUFDRSxNQUFNO29CQUNUQSxPQUFPdkIsU0FBU2dCLGFBQWEsQ0FBQztvQkFDOUJPLEtBQUtOLFlBQVksQ0FBQyxRQUFRSTtvQkFDMUJyQixTQUFTa0IsSUFBSSxDQUFDQyxXQUFXLENBQUNJO2dCQUM1QjtnQkFDQUEsS0FBS04sWUFBWSxDQUFDLFdBQVdLO1lBQy9CO1lBRUFGLFdBQVcsMEJBQTBCO1lBQ3JDQSxXQUFXLGdDQUFnQztZQUMzQ0EsV0FBVyx5Q0FBeUM7WUFDcERBLFdBQVcsOEJBQThCO1lBQ3pDQSxXQUFXLGVBQWU7WUFDMUJBLFdBQVcsb0JBQW9CO1FBQ2pDO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU1JLDBCQUEwQjtZQUM5Qix1REFBdUQ7WUFDdkRDLFdBQVc7Z0JBQ1Q3Qix5RkFBNkJBO1lBQy9CLEdBQUc7UUFDTDtRQUVBOEIsT0FBT0MsZ0JBQWdCLENBQUMscUJBQXFCSDtRQUM3Q0UsT0FBT0MsZ0JBQWdCLENBQUMsVUFBVUg7UUFFbEMsVUFBVTtRQUNWLE9BQU87WUFDTEUsT0FBT0UsbUJBQW1CLENBQUMscUJBQXFCSjtZQUNoREUsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUo7UUFDdkM7SUFDRixHQUFHLEVBQUU7SUFFTCxxQkFBTztrQkFBR3pCOztBQUNaO0dBakZ3QkQ7S0FBQUE7QUFtRnhCLDRDQUE0QztBQUNyQyxNQUFNK0IsZUFBZTs7SUFDMUJsQyxnREFBU0EsQ0FBQztRQUNSLGtDQUFrQztRQUNsQyxJQUFJLE1BQWtCLElBQWUsT0FBT0ssYUFBYSxhQUFhO1lBQ3BFO1FBQ0Y7UUFFQSxNQUFNQyxhQUFhSix5RUFBYUE7UUFFaEMsSUFBSUksV0FBV0ssUUFBUSxFQUFFO1lBQ3ZCLDZCQUE2QjtZQUM3QixNQUFNd0IsUUFBUTlCLFNBQVNnQixhQUFhLENBQUM7WUFDckNjLE1BQU1DLFdBQVcsR0FBSTtZQWlJckIvQixTQUFTa0IsSUFBSSxDQUFDQyxXQUFXLENBQUNXO1lBRTFCLE9BQU87Z0JBQ0w5QixTQUFTa0IsSUFBSSxDQUFDYyxXQUFXLENBQUNGO1lBQzVCO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO0FBQ1QsRUFBRTtJQXRKV0Q7TUFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvTW9iaWxlT3B0aW1pemVyLnRzeD9kY2JiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGluaXRpYWxpemVNb2JpbGVPcHRpbWl6YXRpb25zLCBnZXREZXZpY2VJbmZvIH0gZnJvbSAnQC91dGlscy9tb2JpbGUtb3B0aW1pemF0aW9uJztcblxuaW50ZXJmYWNlIFVuaXZlcnNhbERldmljZU9wdGltaXplclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVW5pdmVyc2FsRGV2aWNlT3B0aW1pemVyKHsgY2hpbGRyZW4gfTogVW5pdmVyc2FsRGV2aWNlT3B0aW1pemVyUHJvcHMpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBPbmx5IHJ1biBpbiBicm93c2VyIGVudmlyb25tZW50XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBkb2N1bWVudCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBJbml0aWFsaXplIG1vYmlsZSBvcHRpbWl6YXRpb25zIHdoZW4gY29tcG9uZW50IG1vdW50c1xuICAgIGluaXRpYWxpemVNb2JpbGVPcHRpbWl6YXRpb25zKCk7XG5cbiAgICAvLyBBZGQgZGV2aWNlLXNwZWNpZmljIGNsYXNzZXMgdG8gYm9keVxuICAgIGNvbnN0IGRldmljZUluZm8gPSBnZXREZXZpY2VJbmZvKCk7XG5cbiAgICAvLyBDbGVhbiB1cCBleGlzdGluZyBkZXZpY2UgY2xhc3Nlc1xuICAgIGRvY3VtZW50LmJvZHkuY2xhc3NMaXN0LnJlbW92ZShcbiAgICAgICdpcy1tb2JpbGUnLCAnaXMtZGVza3RvcCcsICdpcy10YWJsZXQnLCAnaXMtaW9zJywgJ2lzLWFuZHJvaWQnLCAnaGFzLXRvdWNoJywgJ25vLXRvdWNoJ1xuICAgICk7XG5cbiAgICAvLyBBZGQgY3VycmVudCBkZXZpY2UgY2xhc3Nlc1xuICAgIGNvbnN0IGNsYXNzZXNUb0FkZCA9IFtcbiAgICAgIGRldmljZUluZm8uaXNNb2JpbGUgPyAnaXMtbW9iaWxlJyA6ICdpcy1kZXNrdG9wJyxcbiAgICAgIGRldmljZUluZm8uaXNUYWJsZXQgPyAnaXMtdGFibGV0JyA6ICcnLFxuICAgICAgZGV2aWNlSW5mby5pc0lPUyA/ICdpcy1pb3MnIDogJycsXG4gICAgICBkZXZpY2VJbmZvLmlzQW5kcm9pZCA/ICdpcy1hbmRyb2lkJyA6ICcnLFxuICAgICAgZGV2aWNlSW5mby50b3VjaFN1cHBvcnQgPyAnaGFzLXRvdWNoJyA6ICduby10b3VjaCdcbiAgICBdLmZpbHRlcihCb29sZWFuKTsgLy8gUmVtb3ZlIGVtcHR5IHN0cmluZ3NcblxuICAgIGRvY3VtZW50LmJvZHkuY2xhc3NMaXN0LmFkZCguLi5jbGFzc2VzVG9BZGQpO1xuXG4gICAgLy8gQWRkIG1vYmlsZS1zcGVjaWZpYyBtZXRhIHRhZ3MgaWYgb24gbW9iaWxlXG4gICAgaWYgKGRldmljZUluZm8uaXNNb2JpbGUpIHtcbiAgICAgIC8vIEVuc3VyZSB2aWV3cG9ydCBtZXRhIHRhZyBpcyBwcm9wZXJseSBzZXRcbiAgICAgIGxldCB2aWV3cG9ydE1ldGEgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdtZXRhW25hbWU9XCJ2aWV3cG9ydFwiXScpO1xuICAgICAgaWYgKCF2aWV3cG9ydE1ldGEpIHtcbiAgICAgICAgdmlld3BvcnRNZXRhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbWV0YScpO1xuICAgICAgICB2aWV3cG9ydE1ldGEuc2V0QXR0cmlidXRlKCduYW1lJywgJ3ZpZXdwb3J0Jyk7XG4gICAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQodmlld3BvcnRNZXRhKTtcbiAgICAgIH1cbiAgICAgIHZpZXdwb3J0TWV0YS5zZXRBdHRyaWJ1dGUoXG4gICAgICAgICdjb250ZW50JyxcbiAgICAgICAgJ3dpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAsIG1heGltdW0tc2NhbGU9MS4wLCB1c2VyLXNjYWxhYmxlPW5vLCB2aWV3cG9ydC1maXQ9Y292ZXInXG4gICAgICApO1xuXG4gICAgICAvLyBBZGQgbW9iaWxlIHdlYiBhcHAgbWV0YSB0YWdzXG4gICAgICBjb25zdCBhZGRNZXRhVGFnID0gKG5hbWU6IHN0cmluZywgY29udGVudDogc3RyaW5nKSA9PiB7XG4gICAgICAgIGxldCBtZXRhID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihgbWV0YVtuYW1lPVwiJHtuYW1lfVwiXWApO1xuICAgICAgICBpZiAoIW1ldGEpIHtcbiAgICAgICAgICBtZXRhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbWV0YScpO1xuICAgICAgICAgIG1ldGEuc2V0QXR0cmlidXRlKCduYW1lJywgbmFtZSk7XG4gICAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChtZXRhKTtcbiAgICAgICAgfVxuICAgICAgICBtZXRhLnNldEF0dHJpYnV0ZSgnY29udGVudCcsIGNvbnRlbnQpO1xuICAgICAgfTtcblxuICAgICAgYWRkTWV0YVRhZygnbW9iaWxlLXdlYi1hcHAtY2FwYWJsZScsICd5ZXMnKTtcbiAgICAgIGFkZE1ldGFUYWcoJ2FwcGxlLW1vYmlsZS13ZWItYXBwLWNhcGFibGUnLCAneWVzJyk7XG4gICAgICBhZGRNZXRhVGFnKCdhcHBsZS1tb2JpbGUtd2ViLWFwcC1zdGF0dXMtYmFyLXN0eWxlJywgJ2RlZmF1bHQnKTtcbiAgICAgIGFkZE1ldGFUYWcoJ2FwcGxlLW1vYmlsZS13ZWItYXBwLXRpdGxlJywgJ0Jhcm9SaWRlJyk7XG4gICAgICBhZGRNZXRhVGFnKCd0aGVtZS1jb2xvcicsICcjMWUzYTVmJyk7XG4gICAgICBhZGRNZXRhVGFnKCdmb3JtYXQtZGV0ZWN0aW9uJywgJ3RlbGVwaG9uZT1ubycpO1xuICAgIH1cblxuICAgIC8vIEhhbmRsZSBvcmllbnRhdGlvbiBjaGFuZ2VzXG4gICAgY29uc3QgaGFuZGxlT3JpZW50YXRpb25DaGFuZ2UgPSAoKSA9PiB7XG4gICAgICAvLyBSZS1pbml0aWFsaXplIG9wdGltaXphdGlvbnMgYWZ0ZXIgb3JpZW50YXRpb24gY2hhbmdlXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaW5pdGlhbGl6ZU1vYmlsZU9wdGltaXphdGlvbnMoKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvcmllbnRhdGlvbmNoYW5nZScsIGhhbmRsZU9yaWVudGF0aW9uQ2hhbmdlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlT3JpZW50YXRpb25DaGFuZ2UpO1xuXG4gICAgLy8gQ2xlYW51cFxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignb3JpZW50YXRpb25jaGFuZ2UnLCBoYW5kbGVPcmllbnRhdGlvbkNoYW5nZSk7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlT3JpZW50YXRpb25DaGFuZ2UpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufVxuXG4vLyBDU1MtaW4tSlMgc3R5bGVzIGZvciBtb2JpbGUgb3B0aW1pemF0aW9uc1xuZXhwb3J0IGNvbnN0IE1vYmlsZVN0eWxlcyA9ICgpID0+IHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBPbmx5IHJ1biBpbiBicm93c2VyIGVudmlyb25tZW50XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBkb2N1bWVudCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBkZXZpY2VJbmZvID0gZ2V0RGV2aWNlSW5mbygpO1xuXG4gICAgaWYgKGRldmljZUluZm8uaXNNb2JpbGUpIHtcbiAgICAgIC8vIEFkZCBtb2JpbGUtc3BlY2lmaWMgc3R5bGVzXG4gICAgICBjb25zdCBzdHlsZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3N0eWxlJyk7XG4gICAgICBzdHlsZS50ZXh0Q29udGVudCA9IGBcbiAgICAgICAgLyogTW9iaWxlLXNwZWNpZmljIG92ZXJyaWRlcyAqL1xuICAgICAgICAuaXMtbW9iaWxlIGlucHV0LFxuICAgICAgICAuaXMtbW9iaWxlIHNlbGVjdCxcbiAgICAgICAgLmlzLW1vYmlsZSB0ZXh0YXJlYSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xuICAgICAgICAgIC1tb3otYXBwZWFyYW5jZTogbm9uZTtcbiAgICAgICAgICBhcHBlYXJhbmNlOiBub25lO1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSBidXR0b24ge1xuICAgICAgICAgIG1pbi1oZWlnaHQ6IDQ0cHg7XG4gICAgICAgICAgdG91Y2gtYWN0aW9uOiBtYW5pcHVsYXRpb247XG4gICAgICAgICAgLXdlYmtpdC10YXAtaGlnaGxpZ2h0LWNvbG9yOiB0cmFuc3BhcmVudDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5pcy1tb2JpbGUgLm1hcC1jb250YWluZXIge1xuICAgICAgICAgIHRvdWNoLWFjdGlvbjogbm9uZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIFByZXZlbnQgem9vbSBvbiBpbnB1dCBmb2N1cyAqL1xuICAgICAgICAuaXMtbW9iaWxlIGlucHV0OmZvY3VzLFxuICAgICAgICAuaXMtbW9iaWxlIHNlbGVjdDpmb2N1cyxcbiAgICAgICAgLmlzLW1vYmlsZSB0ZXh0YXJlYTpmb2N1cyB7XG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBCZXR0ZXIgc2Nyb2xsaW5nIG9uIG1vYmlsZSAqL1xuICAgICAgICAuaXMtbW9iaWxlIHtcbiAgICAgICAgICAtd2Via2l0LW92ZXJmbG93LXNjcm9sbGluZzogdG91Y2g7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBIaWRlIHNjcm9sbGJhcnMgb24gbW9iaWxlIGZvciBjbGVhbmVyIGxvb2sgKi9cbiAgICAgICAgLmlzLW1vYmlsZSA6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcbiAgICAgICAgICB3aWR0aDogMHB4O1xuICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLyogTW9iaWxlLXNwZWNpZmljIGZvcm0gaW1wcm92ZW1lbnRzICovXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS1mb3JtIHtcbiAgICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLWZvcm0gaW5wdXQsXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS1mb3JtIHNlbGVjdCxcbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLWZvcm0gdGV4dGFyZWEge1xuICAgICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZTVlN2ViO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweCAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLWZvcm0gYnV0dG9uIHtcbiAgICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBNb2JpbGUgbmF2aWdhdGlvbiBpbXByb3ZlbWVudHMgKi9cbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLW5hdiB7XG4gICAgICAgICAgcG9zaXRpb246IHN0aWNreTtcbiAgICAgICAgICB0b3A6IDA7XG4gICAgICAgICAgei1pbmRleDogNTA7XG4gICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XG4gICAgICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLyogTW9iaWxlIGNhcmQgaW1wcm92ZW1lbnRzICovXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS1jYXJkIHtcbiAgICAgICAgICBtYXJnaW46IDhweDtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBNb2JpbGUgbW9kYWwgaW1wcm92ZW1lbnRzICovXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS1tb2RhbCB7XG4gICAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgICAgfVxuXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS1tb2RhbC1jb250ZW50IHtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgICAgICAgIG1heC1oZWlnaHQ6IDg1dmg7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBNb2JpbGUgdGFibGUgaW1wcm92ZW1lbnRzICovXG4gICAgICAgIC5pcy1tb2JpbGUgLm1vYmlsZS10YWJsZSB7XG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLXRhYmxlIHRoLFxuICAgICAgICAuaXMtbW9iaWxlIC5tb2JpbGUtdGFibGUgdGQge1xuICAgICAgICAgIHBhZGRpbmc6IDEycHggOHB4O1xuICAgICAgICB9XG5cbiAgICAgICAgLyogTW9iaWxlIG5vdGlmaWNhdGlvbiBpbXByb3ZlbWVudHMgKi9cbiAgICAgICAgLmlzLW1vYmlsZSAubW9iaWxlLW5vdGlmaWNhdGlvbiB7XG4gICAgICAgICAgbWFyZ2luOiAxNnB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBLZXlib2FyZCBoYW5kbGluZyAqL1xuICAgICAgICAuaXMtbW9iaWxlLmtleWJvYXJkLW9wZW4ge1xuICAgICAgICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIFNhZmUgYXJlYSBoYW5kbGluZyBmb3IgZGV2aWNlcyB3aXRoIG5vdGNoZXMgKi9cbiAgICAgICAgLmlzLW1vYmlsZSAuc2FmZS1hcmVhLXRvcCB7XG4gICAgICAgICAgcGFkZGluZy10b3A6IG1heCgxNnB4LCBlbnYoc2FmZS1hcmVhLWluc2V0LXRvcCkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAuc2FmZS1hcmVhLWJvdHRvbSB7XG4gICAgICAgICAgcGFkZGluZy1ib3R0b206IG1heCgxNnB4LCBlbnYoc2FmZS1hcmVhLWluc2V0LWJvdHRvbSkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAuc2FmZS1hcmVhLWxlZnQge1xuICAgICAgICAgIHBhZGRpbmctbGVmdDogbWF4KDE2cHgsIGVudihzYWZlLWFyZWEtaW5zZXQtbGVmdCkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLmlzLW1vYmlsZSAuc2FmZS1hcmVhLXJpZ2h0IHtcbiAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiBtYXgoMTZweCwgZW52KHNhZmUtYXJlYS1pbnNldC1yaWdodCkpO1xuICAgICAgICB9XG4gICAgICBgO1xuXG4gICAgICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHN0eWxlKTtcblxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5yZW1vdmVDaGlsZChzdHlsZSk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIHJldHVybiBudWxsO1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJpbml0aWFsaXplTW9iaWxlT3B0aW1pemF0aW9ucyIsImdldERldmljZUluZm8iLCJVbml2ZXJzYWxEZXZpY2VPcHRpbWl6ZXIiLCJjaGlsZHJlbiIsImRvY3VtZW50IiwiZGV2aWNlSW5mbyIsImJvZHkiLCJjbGFzc0xpc3QiLCJyZW1vdmUiLCJjbGFzc2VzVG9BZGQiLCJpc01vYmlsZSIsImlzVGFibGV0IiwiaXNJT1MiLCJpc0FuZHJvaWQiLCJ0b3VjaFN1cHBvcnQiLCJmaWx0ZXIiLCJCb29sZWFuIiwiYWRkIiwidmlld3BvcnRNZXRhIiwicXVlcnlTZWxlY3RvciIsImNyZWF0ZUVsZW1lbnQiLCJzZXRBdHRyaWJ1dGUiLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJhZGRNZXRhVGFnIiwibmFtZSIsImNvbnRlbnQiLCJtZXRhIiwiaGFuZGxlT3JpZW50YXRpb25DaGFuZ2UiLCJzZXRUaW1lb3V0Iiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJNb2JpbGVTdHlsZXMiLCJzdHlsZSIsInRleHRDb250ZW50IiwicmVtb3ZlQ2hpbGQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/MobileOptimizer.tsx\n"));

/***/ })

});