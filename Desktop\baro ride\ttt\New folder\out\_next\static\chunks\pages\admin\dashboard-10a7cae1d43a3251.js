(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[189],{2599:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/dashboard",function(){return t(7740)}])},5695:function(e,s,t){"use strict";t.d(s,{Z:function(){return o}});var a=t(5893),r=t(7294),d=t(1163),i=t(837),n=t(7339),l=t(6492);function o(e){let{children:s,requiredRoles:t,redirectTo:o="/login"}=e,{user:c,loading:h}=(0,i.a)(),{checkAccess:m}=(0,n.s)(),g=(0,d.useRouter)(),{showNotification:x}=(0,l.l)();return((0,r.useEffect)(()=>{if(!h){if(!c){g.push(o),x("Please log in to access this page","warning");return}if(!m(t)){let e="/",s="Access denied. You do not have permission to view this page.";"driver"===c.role?(e="/driver/dashboard",s="Access denied. Redirected to driver dashboard."):"rider"===c.role&&(e="/",s="Access denied. Redirected to home page."),g.push(e),x(s,"warning")}}},[c,h,t,g,o,m]),h)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):c&&m(t)?(0,a.jsx)(a.Fragment,{children:s}):null}},7740:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return u}});var a=t(5893),r=t(7294),d=t(837),i=t(404),n=t(109),l=t(2151),o=t(7339),c=t(1163),h=t(6492),m=t(5695),g=t(1664),x=t.n(g);function u(){let{user:e}=(0,d.a)(),{isAdmin:s}=(0,o.s)();(0,c.useRouter)();let{showNotification:t}=(0,h.l)(),[g,u]=(0,r.useState)({totalUsers:0,totalDrivers:0,totalRiders:0,totalBookings:0,pendingBookings:0,completedBookings:0,cancelledBookings:0}),[f,p]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{if(e)try{p(!0);let e=(0,n.IO)((0,n.hJ)(i.db,"users")),s=await (0,n.PL)(e),t=s.size,a=0,r=0;s.forEach(e=>{let s=e.data();"driver"===s.role?a++:"rider"===s.role&&r++});let d=(0,n.IO)((0,n.hJ)(i.db,"bookings")),l=await (0,n.PL)(d),o=l.size,c=0,h=0,m=0;l.forEach(e=>{let s=e.data();"pending"===s.status?c++:"completed"===s.status?h++:"cancelled"===s.status&&m++}),u({totalUsers:t,totalDrivers:a,totalRiders:r,totalBookings:o,pendingBookings:c,completedBookings:h,cancelledBookings:m})}catch(e){console.error("Error fetching admin stats:",e),t("Failed to load admin statistics","error")}finally{p(!1)}})()},[e,t]),(0,a.jsx)(m.Z,{requiredRoles:["admin"],children:(0,a.jsx)(l.Z,{title:"BaroRide - Admin Dashboard",children:(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Admin Dashboard"}),f?(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-700",children:"Total Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:g.totalUsers}),(0,a.jsxs)("div",{className:"mt-2 text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-500",children:["Drivers: ",g.totalDrivers]}),(0,a.jsxs)("span",{className:"text-gray-500 ml-2",children:["Riders: ",g.totalRiders]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-700",children:"Total Bookings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600",children:g.totalBookings})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-700",children:"Pending Bookings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-600",children:g.pendingBookings})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-700",children:"Completed Bookings"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600",children:g.completedBookings})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)(x(),{href:"/admin/users",className:"bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)("h2",{className:"text-xl font-medium text-gray-800 mb-2",children:"Manage Users"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View, edit, and manage user accounts"})]}),(0,a.jsxs)(x(),{href:"/admin/drivers",className:"bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)("h2",{className:"text-xl font-medium text-gray-800 mb-2",children:"Manage Drivers"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Approve, suspend, or manage driver accounts"})]}),(0,a.jsxs)(x(),{href:"/admin/bookings",className:"bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow",children:[(0,a.jsx)("h2",{className:"text-xl font-medium text-gray-800 mb-2",children:"Manage Bookings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"View and manage all ride bookings"})]})]})]})})})}}},function(e){e.O(0,[996,151,888,774,179],function(){return e(e.s=2599)}),_N_E=e.O()}]);