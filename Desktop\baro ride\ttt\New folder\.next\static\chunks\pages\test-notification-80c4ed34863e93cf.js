(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[267],{9977:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/test-notification",function(){return n(1967)}])},1967:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return l}});var t=n(5893),i=n(7294),o=n(7994),r=n(6492);function l(){let{showNotification:e}=(0,r.l)(),[s,n]=(0,i.useState)(""),[l,a]=(0,i.useState)("info");return(0,t.jsx)(o.Z,{title:"Test Notifications",children:(0,t.jsxs)("div",{className:"container mx-auto p-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Notifications"}),(0,t.jsxs)("form",{onSubmit:n=>{n.preventDefault(),e(s,l)},className:"space-y-4 max-w-md",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,t.jsx)("input",{type:"text",value:s,onChange:e=>n(e.target.value),className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md",placeholder:"Enter notification message",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,t.jsxs)("select",{value:l,onChange:e=>a(e.target.value),className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"info",children:"Info"}),(0,t.jsx)("option",{value:"success",children:"Success"}),(0,t.jsx)("option",{value:"warning",children:"Warning"}),(0,t.jsx)("option",{value:"error",children:"Error"})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"w-full bg-blue-500 hover:bg-blue-600 text-white p-2 rounded font-medium transition-colors",children:"Show Notification"})})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Buttons"}),(0,t.jsxs)("div",{className:"space-x-2",children:[(0,t.jsx)("button",{onClick:()=>e("This is an info notification","info"),className:"bg-blue-500 text-white px-4 py-2 rounded",children:"Info"}),(0,t.jsx)("button",{onClick:()=>e("This is a success notification","success"),className:"bg-green-500 text-white px-4 py-2 rounded",children:"Success"}),(0,t.jsx)("button",{onClick:()=>e("This is a warning notification","warning"),className:"bg-yellow-500 text-white px-4 py-2 rounded",children:"Warning"}),(0,t.jsx)("button",{onClick:()=>e("This is an error notification","error"),className:"bg-red-500 text-white px-4 py-2 rounded",children:"Error"})]})]})]})})}}},function(e){e.O(0,[996,994,888,774,179],function(){return e(e.s=9977)}),_N_E=e.O()}]);