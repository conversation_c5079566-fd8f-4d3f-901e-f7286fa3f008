"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/book",{

/***/ "./src/pages/book/index.tsx":
/*!**********************************!*\
  !*** ./src/pages/book/index.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BookRidePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _components_BasicMap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BasicMap */ \"./src/components/BasicMap.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/firebase-helpers */ \"./src/utils/firebase-helpers.ts\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"./src/components/ProtectedRoute.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AIRPORTS = [\n    {\n        code: \"GMB\",\n        name: \"Gambela International Airport\"\n    }\n];\nfunction BookRide() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    const [pickupLocation, setPickupLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        address: \"\",\n        lat: 0,\n        lng: 0\n    });\n    const [selectedAirport, setSelectedAirport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(AIRPORTS[0]);\n    const [passengers, setPassengers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSelectingOnMap, setIsSelectingOnMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [estimatedFare, setEstimatedFare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previousBookings, setPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingBookings, setIsLoadingBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreviousBookings, setShowPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedBookingId, setSelectedBookingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingGps, setIsLoadingGps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch previous bookings when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Fetch previous bookings if user is logged in\n        if (user) {\n            fetchPreviousBookings();\n        }\n    }, [\n        user\n    ]);\n    // Fetch user's previous bookings\n    const fetchPreviousBookings = async ()=>{\n        if (!user) return;\n        setIsLoadingBookings(true);\n        try {\n            // Log for debugging in production\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                console.log(\"Fetching previous bookings in Firebase hosting environment\");\n            }\n            // Use our safe query function if we're in production, otherwise use regular query\n            let bookings = [];\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use the safe query function with retry logic\n                const constraints = [\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5)\n                ];\n                const results = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeQueryDocs)(\"bookings\", constraints);\n                bookings = results.map((data)=>({\n                        id: data.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    }));\n            } else {\n                // Regular query for development environment\n                const bookingsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5) // Limit to 5 most recent bookings\n                );\n                const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(bookingsQuery);\n                querySnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    // Convert Firestore timestamps to Date objects\n                    const booking = {\n                        id: doc.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    };\n                    bookings.push(booking);\n                });\n            }\n            setPreviousBookings(bookings);\n            console.log(\"Fetched \".concat(bookings.length, \" previous bookings\"));\n        } catch (error) {\n            console.error(\"Error fetching previous bookings:\", error);\n            // Silently fail - don't show error notification to user\n            // Just set empty array so the UI shows \"No previous bookings found\"\n            setPreviousBookings([]);\n        } finally{\n            setIsLoadingBookings(false);\n        }\n    };\n    // Clear selected booking and pickup location\n    const clearSelection = ()=>{\n        setPickupLocation({\n            address: \"\",\n            lat: 0,\n            lng: 0\n        });\n        setSelectedBookingId(null);\n    };\n    // Toggle between map selection and manual entry\n    const toggleMapSelection = ()=>{\n        setIsSelectingOnMap(!isSelectingOnMap);\n    };\n    // Handle location selection from map\n    const handleLocationSelected = (location)=>{\n        // Validate the location\n        if (!location || typeof location.lat !== \"number\" || typeof location.lng !== \"number\") {\n            showNotification(\"Invalid location selected. Please try again.\", \"error\");\n            return;\n        }\n        // Validate coordinates are reasonable (not 0,0 or extreme values)\n        if (location.lat === 0 && location.lng === 0) {\n            showNotification(\"Invalid coordinates selected. Please try again.\", \"error\");\n            return;\n        }\n        // Create a valid location object\n        const newLocation = {\n            lat: location.lat,\n            lng: location.lng,\n            address: location.address || \"Selected location\"\n        };\n        console.log(\"Location selected from map:\", newLocation);\n        // Update state\n        setPickupLocation(newLocation);\n        // Clear selected booking since we're selecting a new location\n        setSelectedBookingId(null);\n        setIsSelectingOnMap(false);\n        // Calculate fare for the new location\n        const fare = calculateFare(newLocation);\n        setEstimatedFare(fare);\n        // Show confirmation to the user\n        showNotification(\"Pickup location selected successfully!\", \"success\");\n    };\n    // Validate and geocode manual address entry with debouncing\n    const validateAndGeocodeAddress = async (address)=>{\n        if (!address || address.trim().length < 3) {\n            return;\n        }\n        try {\n            // Use Mapbox Geocoding API to get coordinates for the address\n            const response = await fetch(\"https://api.mapbox.com/geocoding/v5/mapbox.places/\".concat(encodeURIComponent(address), \".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A&limit=1\"));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.features && data.features.length > 0) {\n                    const feature = data.features[0];\n                    const [lng, lat] = feature.center;\n                    // Update location with geocoded coordinates\n                    setPickupLocation((prev)=>({\n                            ...prev,\n                            lat,\n                            lng,\n                            address: feature.place_name || address\n                        }));\n                    // Calculate fare for the new location\n                    const fare = calculateFare({\n                        lat,\n                        lng,\n                        address\n                    });\n                    setEstimatedFare(fare);\n                    console.log(\"Address geocoded successfully:\", {\n                        lat,\n                        lng,\n                        address: feature.place_name\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error geocoding address:\", error);\n        // Don't show error to user for geocoding failures during typing\n        }\n    };\n    // Debounced address validation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            if (pickupLocation.address && pickupLocation.address.length > 3 && (pickupLocation.lat === 0 || pickupLocation.lng === 0)) {\n                validateAndGeocodeAddress(pickupLocation.address);\n            }\n        }, 1000); // Wait 1 second after user stops typing\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        pickupLocation.address\n    ]);\n    // Toggle showing previous bookings\n    const togglePreviousBookings = ()=>{\n        setShowPreviousBookings(!showPreviousBookings);\n    };\n    // Get user's current location using GPS - manual action\n    const getUserLocation = async ()=>{\n        if (!navigator.geolocation) {\n            showNotification(\"Geolocation is not supported by your browser. Please enter your location manually.\", \"error\");\n            return;\n        }\n        setIsLoadingGps(true);\n        showNotification(\"Getting your current location...\", \"info\");\n        try {\n            // Get current position with better timeout and error handling\n            const position = await new Promise((resolve, reject)=>{\n                navigator.geolocation.getCurrentPosition((position)=>{\n                    console.log(\"GPS position obtained:\", position.coords);\n                    resolve(position);\n                }, (error)=>{\n                    console.error(\"GPS error:\", error.code, error.message);\n                    let errorMessage = \"Unable to retrieve your location.\";\n                    // Provide more specific error messages\n                    if (error.code === 1) {\n                        errorMessage = \"Location access denied. Please enable location services in your browser settings.\";\n                    } else if (error.code === 2) {\n                        errorMessage = \"Your current position is unavailable. Please try again later.\";\n                    } else if (error.code === 3) {\n                        errorMessage = \"Location request timed out. Please try again.\";\n                    }\n                    reject(new Error(errorMessage));\n                }, {\n                    enableHighAccuracy: true,\n                    timeout: 10000,\n                    maximumAge: 0\n                });\n            });\n            const { latitude, longitude } = position.coords;\n            console.log(\"GPS coordinates: \".concat(latitude, \", \").concat(longitude));\n            try {\n                // Get address from coordinates using Mapbox Geocoding API\n                const response = await fetch(\"https://api.mapbox.com/geocoding/v5/mapbox.places/\".concat(longitude, \",\").concat(latitude, \".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A\"));\n                if (!response.ok) {\n                    throw new Error(\"Geocoding API error: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Geocoding response:\", data);\n                let address = \"Your current location\";\n                if (data.features && data.features.length > 0) {\n                    address = data.features[0].place_name;\n                    console.log(\"Address found:\", address);\n                }\n                // Create location object\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address\n                };\n                // Update pickup location\n                setPickupLocation(location);\n                // Clear selected booking\n                setSelectedBookingId(null);\n                // Calculate fare for the new location\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                // Show success notification\n                showNotification(\"Your current location has been set as the pickup point.\", \"success\");\n            } catch (geocodingError) {\n                console.error(\"Error getting address:\", geocodingError);\n                // Still set the location even if geocoding fails\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address: \"Your current location\"\n                };\n                setPickupLocation(location);\n                setSelectedBookingId(null);\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                showNotification(\"Location set, but we couldn't get your exact address. You can edit it manually.\", \"warning\");\n            }\n        } catch (error) {\n            console.error(\"Error getting location:\", error);\n            showNotification(error instanceof Error ? error.message : \"Unable to retrieve your location. Please try again or select manually.\", \"error\");\n        } finally{\n            setIsLoadingGps(false);\n        }\n    };\n    // Select a previous booking\n    const selectPreviousBooking = (booking)=>{\n        // Update pickup location\n        setPickupLocation(booking.pickupLocation);\n        // Update airport if it exists in our list\n        const airport = AIRPORTS.find((a)=>a.code === booking.airport.code);\n        if (airport) {\n            setSelectedAirport(airport);\n        }\n        // Set the selected booking ID\n        setSelectedBookingId(booking.id);\n        // Hide the previous bookings dropdown\n        setShowPreviousBookings(false);\n    };\n    // Calculate fare based on distance and number of passengers\n    const calculateFare = function() {\n        let location = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : pickupLocation;\n        // In a real app, you would calculate based on distance between pickup and airport\n        // For this example, we'll use a base fare plus a random amount based on coordinates\n        if (location.lat === 0 || location.lng === 0) {\n            return 0;\n        }\n        const baseFare = 35;\n        // Use the coordinates to create a somewhat realistic variable fare\n        // This is just for demonstration - in a real app you'd use actual distance calculation\n        const seed = location.lat * location.lng % 100;\n        const distanceFare = Math.floor(10 + seed / 5);\n        // Add $5 per additional passenger\n        const passengerFare = (passengers - 1) * 5;\n        return baseFare + distanceFare + passengerFare;\n    };\n    // Update estimated fare when pickup location or passengers count changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pickupLocation.lat !== 0 && pickupLocation.lng !== 0) {\n            const fare = calculateFare(pickupLocation);\n            setEstimatedFare(fare);\n        }\n    }, [\n        pickupLocation,\n        passengers\n    ]);\n    // Create booking directly (no payment required)\n    const createBooking = async ()=>{\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        try {\n            // Show loading notification\n            showNotification(\"Creating your booking...\", \"info\");\n            // Create timestamp objects for Firestore\n            const now = new Date();\n            // Create the booking object with all required fields\n            const booking = {\n                riderId: user.id,\n                pickupLocation: {\n                    address: pickupLocation.address,\n                    lat: pickupLocation.lat,\n                    lng: pickupLocation.lng\n                },\n                airport: {\n                    name: selectedAirport.name,\n                    code: selectedAirport.code\n                },\n                status: \"pending\",\n                fare: estimatedFare > 0 ? estimatedFare : calculateFare(),\n                passengers: passengers,\n                createdAt: now,\n                updatedAt: now\n            };\n            console.log(\"Creating booking with data:\", booking);\n            // Add the booking to Firestore with our safe function if in production\n            let bookingId;\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use safe function with built-in retry logic\n                console.log(\"Using safe function to create booking in production\");\n                bookingId = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"bookings\", booking);\n                console.log(\"Booking created with ID:\", bookingId);\n            } else {\n                // Use standard function in development\n                try {\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created with ID:\", bookingId);\n                } catch (error) {\n                    console.error(\"Error creating booking:\", error);\n                    showNotification(\"Retrying booking creation...\", \"info\");\n                    // Second attempt after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created on second attempt with ID:\", bookingId);\n                }\n            }\n            // Update the user's booking history\n            try {\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeUpdateDoc)(\"users\", user.id, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                } else {\n                    // Use standard function in development\n                    const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id);\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userRef);\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(userRef, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                }\n            } catch (historyError) {\n                // Non-critical error, log but continue\n                console.error(\"Error updating booking history:\", historyError);\n            }\n            // Create a notification for the user\n            try {\n                const notificationData = {\n                    userId: user.id,\n                    message: \"Your ride has been booked successfully. Waiting for a driver to accept.\",\n                    type: \"info\",\n                    read: false,\n                    relatedBookingId: bookingId,\n                    createdAt: now\n                };\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"notifications\", notificationData);\n                } else {\n                    // Use standard function in development\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), notificationData);\n                }\n                console.log(\"Created notification for user\");\n            } catch (notificationError) {\n                // Non-critical error, log but continue\n                console.error(\"Error creating notification:\", notificationError);\n            }\n            // Show success notification\n            showNotification(\"Booking created successfully! Waiting for a driver to accept.\", \"success\");\n            // Reset form after successful booking\n            setPickupLocation({\n                address: \"\",\n                lat: 0,\n                lng: 0\n            });\n            setPassengers(1);\n            setEstimatedFare(0);\n            setSelectedBookingId(null);\n        } catch (error) {\n            console.error(\"Error creating booking:\", error);\n            // Provide more detailed error messages\n            let errorMessage = \"Failed to create booking. Please try again.\";\n            if (error instanceof Error) {\n                if (error.message.includes(\"network\")) {\n                    errorMessage = \"Network error. Please check your internet connection and try again.\";\n                } else if (error.message.includes(\"permission-denied\")) {\n                    errorMessage = \"Permission denied. Please log out and log back in.\";\n                } else if (error.message.includes(\"not-found\")) {\n                    errorMessage = \"Database connection error. Please refresh the page and try again.\";\n                }\n            }\n            showNotification(errorMessage, \"error\");\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        // Validate pickup location\n        if (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) {\n            showNotification(\"Please select a valid pickup location.\", \"error\");\n            return;\n        }\n        // Create booking directly (no payment required)\n        await createBooking();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        title: \"BaroRide - Book a Ride\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-2 sm:p-4 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900\",\n                            children: \"Book Your Ride\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm sm:text-base text-gray-600 mt-1\",\n                            children: \"Quick and easy airport transportation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 sm:mb-0\",\n                                            children: \"Pickup Location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleMapSelection,\n                                                    className: \"text-xs sm:text-sm px-3 py-1 rounded-full transition-colors touch-manipulation font-medium \".concat(isSelectingOnMap ? \"text-white bg-blue-600 hover:bg-blue-700 border border-blue-600\" : \"text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200\"),\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    children: isSelectingOnMap ? \"\\uD83D\\uDCDD Manual Entry\" : \"\\uD83D\\uDDFA️ Select on Map\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: getUserLocation,\n                                                    className: \"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center\",\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    disabled: isLoadingGps,\n                                                    children: isLoadingGps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Getting...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: \"\\uD83D\\uDCCD My Location\"\n                                                    }, void 0, false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                previousBookings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePreviousBookings,\n                                                    className: \"text-sm text-green-500 hover:text-green-700\",\n                                                    children: showPreviousBookings ? \"Hide Previous\" : \"Use Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this),\n                                !isSelectingOnMap ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Enter your pickup address manually\",\n                                                    className: \"w-full p-2 border rounded\",\n                                                    value: pickupLocation.address,\n                                                    onChange: (e)=>{\n                                                        setPickupLocation((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            }));\n                                                        // Clear selected booking when manually editing\n                                                        if (selectedBookingId) {\n                                                            setSelectedBookingId(null);\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pickupLocation.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearSelection,\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-4 w-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3 inline-block mr-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this),\n                                                'Enter your address manually, click \"Use My Location\", or select it on the map'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-blue-600 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-blue-800\",\n                                                        children: \"Map Selection Mode Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Tap anywhere on the map below to select your pickup location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this),\n                                showPreviousBookings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 border rounded shadow-sm overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 px-3 py-2 border-b\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Previous Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-60 overflow-y-auto\",\n                                            children: isLoadingBookings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Loading...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 21\n                                            }, this) : previousBookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-sm text-gray-600\",\n                                                children: \"No previous bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: previousBookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"p-3 hover:bg-gray-50 cursor-pointer transition-colors \".concat(selectedBookingId === booking.id ? \"bg-blue-50 border-l-4 border-blue-500\" : \"\"),\n                                                        onClick: ()=>selectPreviousBooking(booking),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: booking.pickupLocation.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"To: \",\n                                                                                booking.airport.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mr-2\",\n                                                                            children: new Date(booking.updatedAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        selectedBookingId === booking.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-500 font-medium\",\n                                                                            children: \"Selected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, booking.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Pickup Location Map\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BasicMap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    height: \"300px\",\n                                    selectable: isSelectingOnMap,\n                                    onLocationSelected: handleLocationSelected,\n                                    initialLocation: pickupLocation.lat !== 0 ? pickupLocation : undefined\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this),\n                                pickupLocation.lat !== 0 && pickupLocation.lng !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-blue-50 border border-blue-100 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            \"Selected Pickup: \",\n                                                            pickupLocation.address\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedBookingId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                        children: \"From Previous Booking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: clearSelection,\n                                                className: \"ml-2 text-xs text-red-500 hover:text-red-700\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Select Airport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedAirport.code,\n                                            onChange: (e)=>{\n                                                const airport = AIRPORTS.find((a)=>a.code === e.target.value);\n                                                if (airport) setSelectedAirport(airport);\n                                            },\n                                            className: \"w-full p-2 border rounded\",\n                                            children: AIRPORTS.map((airport)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: airport.code,\n                                                    children: airport.name\n                                                }, airport.code, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Number of Passengers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.max(1, prev - 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-l hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M20 12H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"8\",\n                                                    value: passengers,\n                                                    onChange: (e)=>setPassengers(Math.max(1, Math.min(8, parseInt(e.target.value) || 1))),\n                                                    className: \"w-full p-2 border-t border-b text-center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.min(8, prev + 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-r hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M12 6v12M6 12h12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Maximum 8 passengers per ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 743,\n                            columnNumber: 11\n                        }, this),\n                        estimatedFare > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-800 mb-2\",\n                                    children: \"Fare Estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Base fare\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Additional passengers (\",\n                                                        passengers - 1,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"$35.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        Math.floor(10 + pickupLocation.lat * pickupLocation.lng % 100 / 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((passengers - 1) * 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: \"Total estimated fare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: [\n                                                \"$\",\n                                                estimatedFare.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"* Actual fare may vary based on traffic, weather, and other factors.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"w-full \".concat(!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0 ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-500 hover:bg-blue-600\", \" text-white p-3 rounded font-medium transition-colors\"),\n                                    disabled: !pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0,\n                                    children: !user ? \"Please Log In to Book a Ride\" : \"Book Ride Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 13\n                                }, this),\n                                (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-red-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm\",\n                                            children: \"Please enter or select a pickup location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 15\n                                }, this),\n                                !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-yellow-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500 text-sm\",\n                                            children: \"You need to be logged in to book a ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 557,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 555,\n        columnNumber: 5\n    }, this);\n}\n_s(BookRide, \"XktRhB0uZAyb6WBU6XF7HfoHKZM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification\n    ];\n});\n_c = BookRide;\n// Wrap the component with ProtectedRoute\nfunction BookRidePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        requiredRoles: [\n            \"admin\",\n            \"rider\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BookRide, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 867,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 866,\n        columnNumber: 5\n    }, this);\n}\n_c1 = BookRidePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"BookRide\");\n$RefreshReg$(_c1, \"BookRidePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/book/index.tsx\n"));

/***/ })

});