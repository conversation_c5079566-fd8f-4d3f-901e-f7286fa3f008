import { useState } from 'react';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '@/firebase/config';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useNotification } from '@/contexts/NotificationContext';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const { showNotification } = useNotification();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // Send password reset email using Firebase
      await sendPasswordResetEmail(auth, email);

      // Show success message
      setSuccess(true);
      showNotification('Password reset email sent! Check your inbox.', 'success', 5000);
    } catch (err) {
      // Handle specific error codes
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reset email';

      if (errorMessage.includes('user-not-found')) {
        setError('No account found with this email address');
      } else if (errorMessage.includes('invalid-email')) {
        setError('Please enter a valid email address');
      } else {
        setError('Failed to send reset email. Please try again later.');
      }

      showNotification('Error: ' + (err instanceof Error ? err.message : 'Failed to send reset email'), 'error', 5000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 safe-area-top safe-area-bottom">
      <Head>
        <title>BaroRide - Forgot Password</title>
        <meta name="description" content="Reset your BaroRide account password" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      </Head>

      {/* Mobile-optimized container */}
      <div className="flex flex-col min-h-screen">
        {/* Header section */}
        <div className="flex-shrink-0 pt-8 pb-4 px-4 text-center">
          <div className="flex justify-center mb-4">
            <img
              src="/logo-icon.svg"
              alt="BaroRide Logo"
              className="h-16 w-16 sm:h-20 sm:w-20"
            />
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Reset Password</h1>
          <p className="text-sm sm:text-base text-gray-600">We'll help you get back into your account</p>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex items-center justify-center px-4 pb-8">
          <div className="w-full max-w-sm">
            {success ? (
          <div className="text-center">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              <p>Password reset email sent!</p>
              <p className="text-sm mt-2">Check your email inbox for instructions to reset your password.</p>
            </div>
            <div className="mt-6">
              <Link href="/login" className="text-blue-500 hover:text-blue-700">
                Return to Login
              </Link>
            </div>
          </div>
        ) : (
          <>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}

            <p className="mb-4 text-gray-600">
              Enter your email address below and we'll send you instructions to reset your password.
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting}
                />
              </div>

              <button
                type="submit"
                className={`w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                }`}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send Reset Link'}
              </button>

              <div className="text-center mt-4">
                <Link href="/login" className="text-sm text-blue-500 hover:text-blue-700">
                  Back to Login
                </Link>
              </div>
            </form>
          </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
