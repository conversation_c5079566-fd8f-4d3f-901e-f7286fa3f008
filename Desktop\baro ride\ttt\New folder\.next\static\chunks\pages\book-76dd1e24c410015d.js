(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{2255:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/book",function(){return s(7142)}])},5695:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var o=s(5893),r=s(7294),a=s(1163),n=s(837),i=s(7339),l=s(6492);function c(e){let{children:t,requiredRoles:s,redirectTo:c="/login"}=e,{user:d,loading:u}=(0,n.a)(),{checkAccess:m}=(0,i.s)(),x=(0,a.useRouter)(),{showNotification:h}=(0,l.l)();return((0,r.useEffect)(()=>{if(!u){if(!d){x.push(c),h("Please log in to access this page","warning");return}if(!m(s)){let e="/",t="Access denied. You do not have permission to view this page.";"driver"===d.role?(e="/driver/dashboard",t="Access denied. Redirected to driver dashboard."):"rider"===d.role&&(e="/",t="Access denied. Redirected to home page."),x.push(e),h(t,"warning")}}},[d,u,s,x,c,m]),u)?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):d&&m(s)?(0,o.jsx)(o.Fragment,{children:t}):null}},7142:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return y}});var o=s(5893),r=s(7294),a=s(837),n=s(404),i=s(109),l=s(3302),c=s(2151),d=s(6492);let u=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,o=0;for(;o<s;)try{let s=(0,i.IO)((0,i.hJ)(n.db,e),...t),o=await (0,i.PL)(s),r=[];return o.forEach(e=>{r.push({id:e.id,...e.data()})}),r}catch(e){if(o++,console.error("Error querying documents (attempt ".concat(o,"/").concat(s,"):"),e),o>=s)throw e;await new Promise(e=>setTimeout(e,1e3))}return[]},m=async function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,o=0;for(;o<s;)try{return(await (0,i.ET)((0,i.hJ)(n.db,e),t)).id}catch(e){if(o++,console.error("Error adding document (attempt ".concat(o,"/").concat(s,"):"),e),o>=s)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to add document after maximum retries")},x=async function(e,t,s){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2,r=0;for(;r<o;)try{let o=(0,i.doc)(n.db,e,t);await (0,i.r7)(o,s);return}catch(e){if(r++,console.error("Error updating document (attempt ".concat(r,"/").concat(o,"):"),e),r>=o)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to update document after maximum retries")},h=()=>!0,g=()=>{if(!h())return!1;let e=window.location.hostname;return e.includes("firebaseapp.com")||e.includes("web.app")||"baroride.web.app"===e};var p=s(5695),b=s(445);let f=[{code:"GMB",name:"Gambela International Airport"}];function w(){let{user:e}=(0,a.a)(),{showNotification:t}=(0,d.l)(),[s,h]=(0,r.useState)({address:"",lat:0,lng:0}),[p,w]=(0,r.useState)(f[0]),[y,v]=(0,r.useState)(1),[j,k]=(0,r.useState)(!1),[N,P]=(0,r.useState)(0),[M,E]=(0,r.useState)([]),[A,D]=(0,r.useState)(!1),[L,C]=(0,r.useState)(!1),[I,T]=(0,r.useState)(null),[B,S]=(0,r.useState)(!1),[F]=(0,r.useState)(()=>(0,b.dz)());(0,r.useEffect)(()=>{(0,b.dN)()},[]),(0,r.useEffect)(()=>{e&&_()},[e]);let _=async()=>{if(e){D(!0);try{g()&&console.log("Fetching previous bookings in Firebase hosting environment");let t=[];if(g()){let s=[(0,i.ar)("riderId","==",e.id),(0,i.ar)("status","==","completed"),(0,i.Xo)("updatedAt","desc"),(0,i.b9)(5)];t=(await u("bookings",s)).map(e=>({id:e.id,...e,scheduledTime:e.scheduledTime?new Date(e.scheduledTime):new Date,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,passengers:e.passengers||1}))}else{let s=(0,i.IO)((0,i.hJ)(n.db,"bookings"),(0,i.ar)("riderId","==",e.id),(0,i.ar)("status","==","completed"),(0,i.Xo)("updatedAt","desc"),(0,i.b9)(5));(await (0,i.PL)(s)).forEach(e=>{let s=e.data(),o={id:e.id,...s,scheduledTime:s.scheduledTime?new Date(s.scheduledTime):new Date,createdAt:s.createdAt?new Date(s.createdAt):new Date,updatedAt:s.updatedAt?new Date(s.updatedAt):new Date,passengers:s.passengers||1};t.push(o)})}E(t),console.log("Fetched ".concat(t.length," previous bookings"))}catch(e){console.error("Error fetching previous bookings:",e),E([])}finally{D(!1)}}},W=()=>{h({address:"",lat:0,lng:0}),T(null)},O=async e=>{if(e&&!(e.trim().length<3))try{let t=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(encodeURIComponent(e),".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A&limit=1"));if(t.ok){let s=await t.json();if(s.features&&s.features.length>0){let t=s.features[0],[o,r]=t.center;h(s=>({...s,lat:r,lng:o,address:t.place_name||e}));let a=Y({lat:r,lng:o,address:e});P(a),console.log("Address geocoded successfully:",{lat:r,lng:o,address:t.place_name})}}}catch(e){console.error("Error geocoding address:",e)}};(0,r.useEffect)(()=>{let e=setTimeout(()=>{s.address&&s.address.length>3&&(0===s.lat||0===s.lng)&&O(s.address)},1e3);return()=>clearTimeout(e)},[s.address]);let z=async()=>{if(!navigator.geolocation){t("Geolocation is not supported by your browser. Please enter your location manually.","error");return}S(!0),t("Getting your current location...","info");try{let{latitude:e,longitude:s}=(await new Promise((e,t)=>{navigator.geolocation.getCurrentPosition(t=>{console.log("GPS position obtained:",t.coords),e(t)},e=>{console.error("GPS error:",e.code,e.message);let s="Unable to retrieve your location.";1===e.code?s="Location access denied. Please enable location services in your browser settings.":2===e.code?s="Your current position is unavailable. Please try again later.":3===e.code&&(s="Location request timed out. Please try again."),t(Error(s))},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})).coords;console.log("GPS coordinates: ".concat(e,", ").concat(s));try{let o=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(s,",").concat(e,".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A"));if(!o.ok)throw Error("Geocoding API error: ".concat(o.status));let r=await o.json();console.log("Geocoding response:",r);let a="Your current location";r.features&&r.features.length>0&&(a=r.features[0].place_name,console.log("Address found:",a));let n={lat:e,lng:s,address:a};h(n),T(null);let i=Y(n);P(i),t("Your current location has been set as the pickup point.","success")}catch(a){console.error("Error getting address:",a);let o={lat:e,lng:s,address:"Your current location"};h(o),T(null);let r=Y(o);P(r),t("Location set, but we couldn't get your exact address. You can edit it manually.","warning")}}catch(e){console.error("Error getting location:",e),t(e instanceof Error?e.message:"Unable to retrieve your location. Please try again or select manually.","error")}finally{S(!1)}},R=e=>{h(e.pickupLocation);let t=f.find(t=>t.code===e.airport.code);t&&w(t),T(e.id),C(!1)},Y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s;return 0===e.lat||0===e.lng?0:35+Math.floor(10+e.lat*e.lng%100/5)+(y-1)*5};(0,r.useEffect)(()=>{0!==s.lat&&0!==s.lng&&P(Y(s))},[s,y]);let G=async()=>{if(!e){t("Please log in to book a ride.","error");return}try{let o;t("Creating your booking...","info");let r=new Date,a={riderId:e.id,pickupLocation:{address:s.address,lat:s.lat,lng:s.lng},airport:{name:p.name,code:p.code},status:"pending",fare:N>0?N:Y(),passengers:y,createdAt:r,updatedAt:r};if(console.log("Creating booking with data:",a),g())console.log("Using safe function to create booking in production"),o=await m("bookings",a),console.log("Booking created with ID:",o);else try{o=(await (0,i.ET)((0,i.hJ)(n.db,"bookings"),a)).id,console.log("Booking created with ID:",o)}catch(e){console.error("Error creating booking:",e),t("Retrying booking creation...","info"),await new Promise(e=>setTimeout(e,1e3)),o=(await (0,i.ET)((0,i.hJ)(n.db,"bookings"),a)).id,console.log("Booking created on second attempt with ID:",o)}try{if(g()){let t=await (0,i.QT)((0,i.doc)(n.db,"users",e.id));if(t.exists()){let s=t.data().bookingHistory||[];s.includes(o)||(await x("users",e.id,{bookingHistory:[...s,o]}),console.log("Updated user booking history"))}}else{let t=(0,i.doc)(n.db,"users",e.id),s=await (0,i.QT)(t);if(s.exists()){let e=s.data().bookingHistory||[];e.includes(o)||(await (0,i.r7)(t,{bookingHistory:[...e,o]}),console.log("Updated user booking history"))}}}catch(e){console.error("Error updating booking history:",e)}try{let t={userId:e.id,message:"Your ride has been booked successfully. Waiting for a driver to accept.",type:"info",read:!1,relatedBookingId:o,createdAt:r};g()?await m("notifications",t):await (0,i.ET)((0,i.hJ)(n.db,"notifications"),t),console.log("Created notification for user")}catch(e){console.error("Error creating notification:",e)}t("Booking created successfully! Waiting for a driver to accept.","success"),h({address:"",lat:0,lng:0}),v(1),P(0),T(null)}catch(s){console.error("Error creating booking:",s);let e="Failed to create booking. Please try again.";s instanceof Error&&(s.message.includes("network")?e="Network error. Please check your internet connection and try again.":s.message.includes("permission-denied")?e="Permission denied. Please log out and log back in.":s.message.includes("not-found")&&(e="Database connection error. Please refresh the page and try again.")),t(e,"error")}},H=async o=>{if(o.preventDefault(),!e){t("Please log in to book a ride.","error");return}if(!s.address||0===s.lat||0===s.lng){t("Please select a valid pickup location.","error");return}await G()};return(0,o.jsx)(c.Z,{title:"BaroRide - Book a Ride",children:(0,o.jsxs)("div",{className:"container mx-auto p-2 sm:p-4 max-w-4xl",children:[(0,o.jsxs)("div",{className:"text-center mb-6",children:[(0,o.jsx)("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900",children:"Book Your Ride"}),(0,o.jsx)("p",{className:"text-sm sm:text-base text-gray-600 mt-1",children:"Quick and easy airport transportation"})]}),(0,o.jsxs)("form",{onSubmit:H,className:"space-y-4 sm:space-y-6 ".concat(F.isMobile?"mobile-form":""),children:[(0,o.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2 sm:mb-0",children:"Pickup Location"}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,o.jsx)("button",{type:"button",onClick:()=>{k(!j)},className:"text-xs sm:text-sm px-3 py-1 rounded-full transition-colors touch-manipulation font-medium ".concat(j?"text-white bg-blue-600 hover:bg-blue-700 border border-blue-600":"text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200"," ").concat(F.isMobile?"touch-target":""),style:{touchAction:"manipulation"},children:j?"\uD83D\uDCDD Manual Entry":"\uD83D\uDDFA️ Select on Map"}),(0,o.jsx)("button",{type:"button",onClick:z,className:"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center",style:{touchAction:"manipulation"},disabled:B,children:B?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("svg",{className:"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,o.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,o.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Getting..."]}):(0,o.jsx)(o.Fragment,{children:"\uD83D\uDCCD My Location"})}),M.length>0&&(0,o.jsx)("button",{type:"button",onClick:()=>{C(!L)},className:"text-sm text-green-500 hover:text-green-700",children:L?"Hide Previous":"Use Previous"})]})]}),j?(0,o.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("svg",{className:"w-5 h-5 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Map Selection Mode Active"}),(0,o.jsx)("p",{className:"text-xs text-blue-600",children:"Tap anywhere on the map below to select your pickup location"})]})]})}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"text",placeholder:"Enter your pickup address manually",className:"w-full p-2 border rounded ".concat(F.isMobile?"mobile-input":""),value:s.address,onChange:e=>{h(t=>({...t,address:e.target.value})),I&&T(null)},autoComplete:"street-address",autoCorrect:"off",autoCapitalize:"words",inputMode:"text"}),s.address&&(0,o.jsx)("button",{type:"button",onClick:W,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,o.jsx)("svg",{className:"w-3 h-3 inline-block mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),'Enter your address manually, click "Use My Location", or select it on the map']})]}),L&&(0,o.jsxs)("div",{className:"mt-2 border rounded shadow-sm overflow-hidden",children:[(0,o.jsx)("div",{className:"bg-gray-50 px-3 py-2 border-b",children:(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Previous Bookings"})}),(0,o.jsx)("div",{className:"max-h-60 overflow-y-auto",children:A?(0,o.jsxs)("div",{className:"p-4 text-center",children:[(0,o.jsx)("div",{className:"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-600",children:"Loading..."})]}):0===M.length?(0,o.jsx)("div",{className:"p-4 text-center text-sm text-gray-600",children:"No previous bookings found"}):(0,o.jsx)("ul",{className:"divide-y divide-gray-200",children:M.map(e=>(0,o.jsx)("li",{className:"p-3 hover:bg-gray-50 cursor-pointer transition-colors ".concat(I===e.id?"bg-blue-50 border-l-4 border-blue-500":""),onClick:()=>R(e),children:(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e.pickupLocation.address}),(0,o.jsxs)("p",{className:"text-xs text-gray-500",children:["To: ",e.airport.name]})]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"text-xs text-gray-500 mr-2",children:new Date(e.updatedAt).toLocaleDateString()}),I===e.id&&(0,o.jsx)("span",{className:"text-xs text-blue-500 font-medium",children:"Selected"})]})]})},e.id))})})]})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Pickup Location Map"}),(0,o.jsx)("div",{className:F.isMobile?"map-container":"",children:(0,o.jsx)(l.Z,{height:F.isMobile?"40vh":"300px",selectable:j,onLocationSelected:e=>{if(!e||"number"!=typeof e.lat||"number"!=typeof e.lng){t("Invalid location selected. Please try again.","error");return}if(0===e.lat&&0===e.lng){t("Invalid coordinates selected. Please try again.","error");return}let s={lat:e.lat,lng:e.lng,address:e.address||"Selected location"};console.log("Location selected from map:",s),h(s),T(null),k(!1),P(Y(s)),t("Pickup location selected successfully!","success")},initialLocation:0!==s.lat?s:void 0})}),0!==s.lat&&0!==s.lng&&(0,o.jsx)("div",{className:"mt-2 p-2 bg-blue-50 border border-blue-100 rounded",children:(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("p",{className:"text-sm font-medium",children:["Selected Pickup: ",s.address]}),I&&(0,o.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:"From Previous Booking"})]}),(0,o.jsx)("button",{type:"button",onClick:W,className:"ml-2 text-xs text-red-500 hover:text-red-700",children:"Clear"})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Airport"}),(0,o.jsx)("select",{value:p.code,onChange:e=>{let t=f.find(t=>t.code===e.target.value);t&&w(t)},className:"w-full p-2 border rounded ".concat(F.isMobile?"mobile-input":""),children:f.map(e=>(0,o.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Passengers"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("button",{type:"button",onClick:()=>v(e=>Math.max(1,e-1)),className:"p-2 bg-gray-100 border rounded-l hover:bg-gray-200 ".concat(F.isMobile?"touch-target":""),children:(0,o.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M20 12H4"})})}),(0,o.jsx)("input",{type:"number",min:"1",max:"8",value:y,onChange:e=>v(Math.max(1,Math.min(8,parseInt(e.target.value)||1))),className:"w-full p-2 border-t border-b text-center ".concat(F.isMobile?"mobile-input":""),inputMode:"numeric"}),(0,o.jsx)("button",{type:"button",onClick:()=>v(e=>Math.min(8,e+1)),className:"p-2 bg-gray-100 border rounded-r hover:bg-gray-200 ".concat(F.isMobile?"touch-target":""),children:(0,o.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v12M6 12h12"})})})]}),(0,o.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum 8 passengers per ride"})]})]}),N>0&&(0,o.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-blue-800 mb-2",children:"Fare Estimate"}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm text-blue-700",children:"Base fare"}),(0,o.jsx)("p",{className:"text-sm text-blue-700",children:"Distance"}),y>1&&(0,o.jsxs)("p",{className:"text-sm text-blue-700",children:["Additional passengers (",y-1,")"]})]}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("p",{className:"text-sm text-blue-700",children:"$35.00"}),(0,o.jsxs)("p",{className:"text-sm text-blue-700",children:["$",Math.floor(10+s.lat*s.lng%100/5).toFixed(2)]}),y>1&&(0,o.jsxs)("p",{className:"text-sm text-blue-700",children:["$",((y-1)*5).toFixed(2)]})]})]}),(0,o.jsxs)("div",{className:"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center",children:[(0,o.jsx)("p",{className:"font-medium text-blue-800",children:"Total estimated fare"}),(0,o.jsxs)("p",{className:"font-medium text-blue-800",children:["$",N.toFixed(2)]})]}),(0,o.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"* Actual fare may vary based on traffic, weather, and other factors."})]}),(0,o.jsxs)("div",{className:"pt-4",children:[(0,o.jsx)("button",{type:"submit",className:"w-full ".concat(s.address&&0!==s.lat&&0!==s.lng?"bg-blue-500 hover:bg-blue-600":"bg-gray-400 cursor-not-allowed"," text-white p-3 rounded font-medium transition-colors ").concat(F.isMobile?"mobile-button touch-target":""),disabled:!s.address||0===s.lat||0===s.lng,style:{touchAction:"manipulation"},children:e?"Book Ride Now":"Please Log In to Book a Ride"}),(!s.address||0===s.lat||0===s.lng)&&(0,o.jsxs)("div",{className:"flex items-center mt-2",children:[(0,o.jsx)("svg",{className:"w-4 h-4 text-red-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,o.jsx)("p",{className:"text-red-500 text-sm",children:"Please enter or select a pickup location"})]}),!e&&(0,o.jsxs)("div",{className:"flex items-center mt-2",children:[(0,o.jsx)("svg",{className:"w-4 h-4 text-yellow-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,o.jsx)("p",{className:"text-yellow-500 text-sm",children:"You need to be logged in to book a ride"})]})]})]})]})})}function y(){return(0,o.jsx)(p.Z,{requiredRoles:["admin","rider"],children:(0,o.jsx)(w,{})})}}},function(e){e.O(0,[996,151,302,888,774,179],function(){return e(e.s=2255)}),_N_E=e.O()}]);