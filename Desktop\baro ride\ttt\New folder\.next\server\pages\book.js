/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/book";
exports.ids = ["pages/book"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fbook&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cbook%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fbook&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cbook%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\book\\index.tsx */ \"./src/pages/book/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/book\",\n        pathname: \"/book\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_book_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fbook&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cbook%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/BasicMap.tsx":
/*!*************************************!*\
  !*** ./src/components/BasicMap.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BasicMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction BasicMap({ height = \"300px\", selectable = false, onLocationSelected, initialLocation }) {\n    const mapContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Method to update the map with a new location\n    const updateMapLocation = (location)=>{\n        if (!mapRef.current || !location || location.lat === 0 || location.lng === 0) return;\n        // Remove existing markers\n        const markers = document.querySelectorAll(\".mapboxgl-marker\");\n        markers.forEach((marker)=>marker.remove());\n        // Add a new marker\n        new window.mapboxgl.Marker({\n            color: \"#3b82f6\"\n        }).setLngLat([\n            location.lng,\n            location.lat\n        ]).addTo(mapRef.current);\n        // Fly to the new location\n        mapRef.current.flyTo({\n            center: [\n                location.lng,\n                location.lat\n            ],\n            zoom: 14,\n            essential: true\n        });\n        // Add popup with address if available\n        if (location.address) {\n            new window.mapboxgl.Popup({\n                offset: 25,\n                closeButton: false\n            }).setLngLat([\n                location.lng,\n                location.lat\n            ]).setHTML(`<p style=\"margin: 0;\">${location.address}</p>`).addTo(mapRef.current);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load Mapbox script dynamically\n        const loadMapbox = ()=>{\n            // Check if Mapbox is already loaded\n            if (window.mapboxgl) {\n                initializeMap();\n                return;\n            }\n            // Create script element\n            const script = document.createElement(\"script\");\n            script.src = \"https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js\";\n            script.async = true;\n            script.onload = ()=>{\n                // Initialize map after script loads\n                initializeMap();\n            };\n            document.head.appendChild(script);\n            // Add CSS\n            const link = document.createElement(\"link\");\n            link.href = \"https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css\";\n            link.rel = \"stylesheet\";\n            document.head.appendChild(link);\n        };\n        // Initialize the map\n        const initializeMap = ()=>{\n            if (!mapContainerRef.current || !window.mapboxgl) return;\n            // Set access token\n            window.mapboxgl.accessToken = \"pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A\";\n            // Determine center coordinates\n            let centerLng = 34.5925; // Default to Gambela, Ethiopia\n            let centerLat = 8.2483;\n            // Use initialLocation if provided\n            if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n                centerLng = initialLocation.lng;\n                centerLat = initialLocation.lat;\n            }\n            // No automatic geolocation - we're making location selection fully manual\n            console.log(\"Using manual location selection only - automatic geolocation disabled\");\n            // Create map\n            const map = new window.mapboxgl.Map({\n                container: mapContainerRef.current,\n                style: \"mapbox://styles/mapbox/streets-v11\",\n                center: [\n                    centerLng,\n                    centerLat\n                ],\n                zoom: 13\n            });\n            // Store map reference\n            mapRef.current = map;\n            // Add navigation controls\n            map.addControl(new window.mapboxgl.NavigationControl());\n            // Add a marker for the initial location if provided\n            if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n                // Create a marker with a popup showing the address\n                new window.mapboxgl.Marker({\n                    color: \"#3b82f6\"\n                }).setLngLat([\n                    initialLocation.lng,\n                    initialLocation.lat\n                ]).addTo(map);\n                // Add popup with address if available\n                if (initialLocation.address) {\n                    new window.mapboxgl.Popup({\n                        offset: 25,\n                        closeButton: false\n                    }).setLngLat([\n                        initialLocation.lng,\n                        initialLocation.lat\n                    ]).setHTML(`<p style=\"margin: 0;\">${initialLocation.address}</p>`).addTo(map);\n                }\n            } else {\n                // Add a default marker if no initial location\n                new window.mapboxgl.Marker().setLngLat([\n                    centerLng,\n                    centerLat\n                ]).addTo(map);\n            }\n            // Add click handler for location selection if selectable is true\n            if (selectable && onLocationSelected) {\n                map.on(\"click\", async (e)=>{\n                    const { lng, lat } = e.lngLat;\n                    // Remove existing markers\n                    const markers = document.querySelectorAll(\".mapboxgl-marker\");\n                    markers.forEach((marker)=>marker.remove());\n                    // Create a new marker at the clicked location\n                    new window.mapboxgl.Marker({\n                        color: \"#3b82f6\"\n                    }).setLngLat([\n                        lng,\n                        lat\n                    ]).addTo(map);\n                    setIsLoading(true);\n                    try {\n                        // Get address from coordinates using Mapbox Geocoding API\n                        const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${window.mapboxgl.accessToken}`);\n                        const data = await response.json();\n                        let address = \"Unknown location\";\n                        if (data.features && data.features.length > 0) {\n                            address = data.features[0].place_name;\n                        }\n                        // Create popup with address and select button\n                        const popup = new window.mapboxgl.Popup({\n                            offset: 25\n                        }).setLngLat([\n                            lng,\n                            lat\n                        ]).setHTML(`\n                <div style=\"text-align: center;\">\n                  <p style=\"margin-bottom: 8px;\">${address}</p>\n                  <button id=\"select-location\" style=\"background-color: #3b82f6; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;\">\n                    Select this location\n                  </button>\n                </div>\n              `).addTo(map);\n                        // Add event listener to the select button\n                        setTimeout(()=>{\n                            const selectButton = document.getElementById(\"select-location\");\n                            if (selectButton) {\n                                selectButton.addEventListener(\"click\", ()=>{\n                                    onLocationSelected({\n                                        lat,\n                                        lng,\n                                        address\n                                    });\n                                    popup.remove();\n                                });\n                            }\n                        }, 100);\n                    } catch (error) {\n                        console.error(\"Error geocoding location:\", error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                });\n            }\n            // Clean up on unmount\n            return ()=>map.remove();\n        };\n        loadMapbox();\n    }, [\n        selectable,\n        onLocationSelected\n    ]);\n    // Update map when initialLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n            // Wait for map to be initialized\n            const checkMapInterval = setInterval(()=>{\n                if (mapRef.current) {\n                    updateMapLocation(initialLocation);\n                    clearInterval(checkMapInterval);\n                }\n            }, 100);\n            // Clear interval after 5 seconds to prevent memory leaks\n            setTimeout(()=>clearInterval(checkMapInterval), 5000);\n        }\n    }, [\n        initialLocation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapContainerRef,\n                style: {\n                    width: \"100%\",\n                    height,\n                    position: \"relative\",\n                    border: \"1px solid #ddd\",\n                    borderRadius: \"4px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: \"rgba(0, 0, 0, 0.2)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 10\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: \"white\",\n                        padding: \"12px\",\n                        borderRadius: \"50%\",\n                        boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                fill: \"none\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: \"4\",\n                                opacity: \"0.25\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                fill: \"none\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: \"4\",\n                                strokeDasharray: \"60 30\",\n                                style: {\n                                    animation: \"spin 1s linear infinite\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                                children: `\n                @keyframes spin {\n                  0% { transform: rotate(0deg); }\n                  100% { transform: rotate(360deg); }\n                }\n              `\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this),\n            selectable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    left: \"10px\",\n                    right: \"10px\",\n                    backgroundColor: \"white\",\n                    padding: \"8px 12px\",\n                    borderRadius: \"4px\",\n                    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\",\n                    zIndex: 5,\n                    fontSize: \"14px\",\n                    textAlign: \"center\",\n                    color: \"#4b5563\"\n                },\n                children: \"Click anywhere on the map to manually select your pickup location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9CYXNpY01hcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9EO0FBZXJDLFNBQVNHLFNBQVMsRUFDL0JDLFNBQVMsT0FBTyxFQUNoQkMsYUFBYSxLQUFLLEVBQ2xCQyxrQkFBa0IsRUFDbEJDLGVBQWUsRUFDRDtJQUNkLE1BQU1DLGtCQUFrQlAsNkNBQU1BLENBQWlCO0lBQy9DLE1BQU0sQ0FBQ1EsV0FBV0MsYUFBYSxHQUFHUiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNUyxTQUFTViw2Q0FBTUEsQ0FBTTtJQUUzQiwrQ0FBK0M7SUFDL0MsTUFBTVcsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUksQ0FBQ0YsT0FBT0csT0FBTyxJQUFJLENBQUNELFlBQVlBLFNBQVNFLEdBQUcsS0FBSyxLQUFLRixTQUFTRyxHQUFHLEtBQUssR0FBRztRQUU5RSwwQkFBMEI7UUFDMUIsTUFBTUMsVUFBVUMsU0FBU0MsZ0JBQWdCLENBQUM7UUFDMUNGLFFBQVFHLE9BQU8sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBT0MsTUFBTTtRQUV2QyxtQkFBbUI7UUFDbkIsSUFBSUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNLENBQUM7WUFBRUMsT0FBTztRQUFVLEdBQzNDQyxTQUFTLENBQUM7WUFBQ2QsU0FBU0csR0FBRztZQUFFSCxTQUFTRSxHQUFHO1NBQUMsRUFDdENhLEtBQUssQ0FBQ2pCLE9BQU9HLE9BQU87UUFFdkIsMEJBQTBCO1FBQzFCSCxPQUFPRyxPQUFPLENBQUNlLEtBQUssQ0FBQztZQUNuQkMsUUFBUTtnQkFBQ2pCLFNBQVNHLEdBQUc7Z0JBQUVILFNBQVNFLEdBQUc7YUFBQztZQUNwQ2dCLE1BQU07WUFDTkMsV0FBVztRQUNiO1FBRUEsc0NBQXNDO1FBQ3RDLElBQUluQixTQUFTb0IsT0FBTyxFQUFFO1lBQ3BCLElBQUlWLE9BQU9DLFFBQVEsQ0FBQ1UsS0FBSyxDQUFDO2dCQUFFQyxRQUFRO2dCQUFJQyxhQUFhO1lBQU0sR0FDeERULFNBQVMsQ0FBQztnQkFBQ2QsU0FBU0csR0FBRztnQkFBRUgsU0FBU0UsR0FBRzthQUFDLEVBQ3RDc0IsT0FBTyxDQUFDLENBQUMsc0JBQXNCLEVBQUV4QixTQUFTb0IsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUN2REwsS0FBSyxDQUFDakIsT0FBT0csT0FBTztRQUN6QjtJQUNGO0lBRUFkLGdEQUFTQSxDQUFDO1FBQ1IsaUNBQWlDO1FBQ2pDLE1BQU1zQyxhQUFhO1lBQ2pCLG9DQUFvQztZQUNwQyxJQUFJZixPQUFPQyxRQUFRLEVBQUU7Z0JBQ25CZTtnQkFDQTtZQUNGO1lBRUEsd0JBQXdCO1lBQ3hCLE1BQU1DLFNBQVN0QixTQUFTdUIsYUFBYSxDQUFDO1lBQ3RDRCxPQUFPRSxHQUFHLEdBQUc7WUFDYkYsT0FBT0csS0FBSyxHQUFHO1lBQ2ZILE9BQU9JLE1BQU0sR0FBRztnQkFDZCxvQ0FBb0M7Z0JBQ3BDTDtZQUNGO1lBQ0FyQixTQUFTMkIsSUFBSSxDQUFDQyxXQUFXLENBQUNOO1lBRTFCLFVBQVU7WUFDVixNQUFNTyxPQUFPN0IsU0FBU3VCLGFBQWEsQ0FBQztZQUNwQ00sS0FBS0MsSUFBSSxHQUFHO1lBQ1pELEtBQUtFLEdBQUcsR0FBRztZQUNYL0IsU0FBUzJCLElBQUksQ0FBQ0MsV0FBVyxDQUFDQztRQUM1QjtRQUVBLHFCQUFxQjtRQUNyQixNQUFNUixnQkFBZ0I7WUFDcEIsSUFBSSxDQUFDL0IsZ0JBQWdCTSxPQUFPLElBQUksQ0FBQ1MsT0FBT0MsUUFBUSxFQUFFO1lBRWxELG1CQUFtQjtZQUNuQkQsT0FBT0MsUUFBUSxDQUFDMEIsV0FBVyxHQUFHO1lBRTlCLCtCQUErQjtZQUMvQixJQUFJQyxZQUFZLFNBQVMsK0JBQStCO1lBQ3hELElBQUlDLFlBQVk7WUFFaEIsa0NBQWtDO1lBQ2xDLElBQUk3QyxtQkFBbUJBLGdCQUFnQlEsR0FBRyxLQUFLLEtBQUtSLGdCQUFnQlMsR0FBRyxLQUFLLEdBQUc7Z0JBQzdFbUMsWUFBWTVDLGdCQUFnQlMsR0FBRztnQkFDL0JvQyxZQUFZN0MsZ0JBQWdCUSxHQUFHO1lBQ2pDO1lBRUEsMEVBQTBFO1lBQzFFc0MsUUFBUUMsR0FBRyxDQUFDO1lBRVosYUFBYTtZQUNiLE1BQU1DLE1BQU0sSUFBSWhDLE9BQU9DLFFBQVEsQ0FBQ2dDLEdBQUcsQ0FBQztnQkFDbENDLFdBQVdqRCxnQkFBZ0JNLE9BQU87Z0JBQ2xDNEMsT0FBTztnQkFDUDVCLFFBQVE7b0JBQUNxQjtvQkFBV0M7aUJBQVU7Z0JBQzlCckIsTUFBTTtZQUNSO1lBRUEsc0JBQXNCO1lBQ3RCcEIsT0FBT0csT0FBTyxHQUFHeUM7WUFFakIsMEJBQTBCO1lBQzFCQSxJQUFJSSxVQUFVLENBQUMsSUFBSXBDLE9BQU9DLFFBQVEsQ0FBQ29DLGlCQUFpQjtZQUVwRCxvREFBb0Q7WUFDcEQsSUFBSXJELG1CQUFtQkEsZ0JBQWdCUSxHQUFHLEtBQUssS0FBS1IsZ0JBQWdCUyxHQUFHLEtBQUssR0FBRztnQkFDN0UsbURBQW1EO2dCQUNuRCxJQUFJTyxPQUFPQyxRQUFRLENBQUNDLE1BQU0sQ0FBQztvQkFBRUMsT0FBTztnQkFBVSxHQUMzQ0MsU0FBUyxDQUFDO29CQUFDcEIsZ0JBQWdCUyxHQUFHO29CQUFFVCxnQkFBZ0JRLEdBQUc7aUJBQUMsRUFDcERhLEtBQUssQ0FBQzJCO2dCQUVULHNDQUFzQztnQkFDdEMsSUFBSWhELGdCQUFnQjBCLE9BQU8sRUFBRTtvQkFDM0IsSUFBSVYsT0FBT0MsUUFBUSxDQUFDVSxLQUFLLENBQUM7d0JBQUVDLFFBQVE7d0JBQUlDLGFBQWE7b0JBQU0sR0FDeERULFNBQVMsQ0FBQzt3QkFBQ3BCLGdCQUFnQlMsR0FBRzt3QkFBRVQsZ0JBQWdCUSxHQUFHO3FCQUFDLEVBQ3BEc0IsT0FBTyxDQUFDLENBQUMsc0JBQXNCLEVBQUU5QixnQkFBZ0IwQixPQUFPLENBQUMsSUFBSSxDQUFDLEVBQzlETCxLQUFLLENBQUMyQjtnQkFDWDtZQUNGLE9BQU87Z0JBQ0wsOENBQThDO2dCQUM5QyxJQUFJaEMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNLEdBQ3ZCRSxTQUFTLENBQUM7b0JBQUN3QjtvQkFBV0M7aUJBQVUsRUFDaEN4QixLQUFLLENBQUMyQjtZQUNYO1lBRUEsaUVBQWlFO1lBQ2pFLElBQUlsRCxjQUFjQyxvQkFBb0I7Z0JBQ3BDaUQsSUFBSU0sRUFBRSxDQUFDLFNBQVMsT0FBT0M7b0JBQ3JCLE1BQU0sRUFBRTlDLEdBQUcsRUFBRUQsR0FBRyxFQUFFLEdBQUcrQyxFQUFFQyxNQUFNO29CQUU3QiwwQkFBMEI7b0JBQzFCLE1BQU05QyxVQUFVQyxTQUFTQyxnQkFBZ0IsQ0FBQztvQkFDMUNGLFFBQVFHLE9BQU8sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBT0MsTUFBTTtvQkFFdkMsOENBQThDO29CQUM5QyxJQUFJQyxPQUFPQyxRQUFRLENBQUNDLE1BQU0sQ0FBQzt3QkFBRUMsT0FBTztvQkFBVSxHQUMzQ0MsU0FBUyxDQUFDO3dCQUFDWDt3QkFBS0Q7cUJBQUksRUFDcEJhLEtBQUssQ0FBQzJCO29CQUVUN0MsYUFBYTtvQkFFYixJQUFJO3dCQUNGLDBEQUEwRDt3QkFDMUQsTUFBTXNELFdBQVcsTUFBTUMsTUFDckIsQ0FBQyxrREFBa0QsRUFBRWpELElBQUksQ0FBQyxFQUFFRCxJQUFJLG1CQUFtQixFQUFFUSxPQUFPQyxRQUFRLENBQUMwQixXQUFXLENBQUMsQ0FBQzt3QkFFcEgsTUFBTWdCLE9BQU8sTUFBTUYsU0FBU0csSUFBSTt3QkFFaEMsSUFBSWxDLFVBQVU7d0JBQ2QsSUFBSWlDLEtBQUtFLFFBQVEsSUFBSUYsS0FBS0UsUUFBUSxDQUFDQyxNQUFNLEdBQUcsR0FBRzs0QkFDN0NwQyxVQUFVaUMsS0FBS0UsUUFBUSxDQUFDLEVBQUUsQ0FBQ0UsVUFBVTt3QkFDdkM7d0JBRUEsOENBQThDO3dCQUM5QyxNQUFNQyxRQUFRLElBQUloRCxPQUFPQyxRQUFRLENBQUNVLEtBQUssQ0FBQzs0QkFBRUMsUUFBUTt3QkFBRyxHQUNsRFIsU0FBUyxDQUFDOzRCQUFDWDs0QkFBS0Q7eUJBQUksRUFDcEJzQixPQUFPLENBQUMsQ0FBQzs7aURBRXlCLEVBQUVKLFFBQVE7Ozs7O2NBSzdDLENBQUMsRUFDQUwsS0FBSyxDQUFDMkI7d0JBRVQsMENBQTBDO3dCQUMxQ2lCLFdBQVc7NEJBQ1QsTUFBTUMsZUFBZXZELFNBQVN3RCxjQUFjLENBQUM7NEJBQzdDLElBQUlELGNBQWM7Z0NBQ2hCQSxhQUFhRSxnQkFBZ0IsQ0FBQyxTQUFTO29DQUNyQ3JFLG1CQUFtQjt3Q0FDakJTO3dDQUNBQzt3Q0FDQWlCO29DQUNGO29DQUNBc0MsTUFBTWpELE1BQU07Z0NBQ2Q7NEJBQ0Y7d0JBQ0YsR0FBRztvQkFDTCxFQUFFLE9BQU9zRCxPQUFPO3dCQUNkdkIsUUFBUXVCLEtBQUssQ0FBQyw2QkFBNkJBO29CQUM3QyxTQUFVO3dCQUNSbEUsYUFBYTtvQkFDZjtnQkFDRjtZQUNGO1lBRUEsc0JBQXNCO1lBQ3RCLE9BQU8sSUFBTTZDLElBQUlqQyxNQUFNO1FBQ3pCO1FBRUFnQjtJQUNGLEdBQUc7UUFBQ2pDO1FBQVlDO0tBQW1CO0lBRW5DLDBDQUEwQztJQUMxQ04sZ0RBQVNBLENBQUM7UUFDUixJQUFJTyxtQkFBbUJBLGdCQUFnQlEsR0FBRyxLQUFLLEtBQUtSLGdCQUFnQlMsR0FBRyxLQUFLLEdBQUc7WUFDN0UsaUNBQWlDO1lBQ2pDLE1BQU02RCxtQkFBbUJDLFlBQVk7Z0JBQ25DLElBQUluRSxPQUFPRyxPQUFPLEVBQUU7b0JBQ2xCRixrQkFBa0JMO29CQUNsQndFLGNBQWNGO2dCQUNoQjtZQUNGLEdBQUc7WUFFSCx5REFBeUQ7WUFDekRMLFdBQVcsSUFBTU8sY0FBY0YsbUJBQW1CO1FBQ3BEO0lBQ0YsR0FBRztRQUFDdEU7S0FBZ0I7SUFFcEIscUJBQ0UsOERBQUN5RTtRQUFJdEIsT0FBTztZQUFFdUIsVUFBVTtRQUFXOzswQkFDakMsOERBQUNEO2dCQUNDRSxLQUFLMUU7Z0JBQ0xrRCxPQUFPO29CQUNMeUIsT0FBTztvQkFDUC9FO29CQUNBNkUsVUFBVTtvQkFDVkcsUUFBUTtvQkFDUkMsY0FBYztnQkFDaEI7Ozs7OztZQUlENUUsMkJBQ0MsOERBQUN1RTtnQkFDQ3RCLE9BQU87b0JBQ0x1QixVQUFVO29CQUNWSyxLQUFLO29CQUNMQyxNQUFNO29CQUNOQyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxpQkFBaUI7b0JBQ2pCQyxTQUFTO29CQUNUQyxZQUFZO29CQUNaQyxnQkFBZ0I7b0JBQ2hCQyxRQUFRO2dCQUNWOzBCQUVBLDRFQUFDZDtvQkFDQ3RCLE9BQU87d0JBQ0xnQyxpQkFBaUI7d0JBQ2pCSyxTQUFTO3dCQUNUVixjQUFjO3dCQUNkVyxXQUFXO29CQUNiOzhCQUVBLDRFQUFDQzt3QkFBSWQsT0FBTTt3QkFBSy9FLFFBQU87d0JBQUs4RixTQUFRO3dCQUFZQyxPQUFNOzswQ0FDcEQsOERBQUNDO2dDQUFPQyxJQUFHO2dDQUFLQyxJQUFHO2dDQUFLQyxHQUFFO2dDQUFLQyxNQUFLO2dDQUFPQyxRQUFPO2dDQUFVQyxhQUFZO2dDQUFJQyxTQUFROzs7Ozs7MENBQ3BGLDhEQUFDUDtnQ0FDQ0MsSUFBRztnQ0FDSEMsSUFBRztnQ0FDSEMsR0FBRTtnQ0FDRkMsTUFBSztnQ0FDTEMsUUFBTztnQ0FDUEMsYUFBWTtnQ0FDWkUsaUJBQWdCO2dDQUNoQmxELE9BQU87b0NBQUVtRCxXQUFXO2dDQUEwQjs7Ozs7OzBDQUVoRCw4REFBQ25EOzBDQUFPLENBQUM7Ozs7O2NBS1QsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9SckQsNEJBQ0MsOERBQUMyRTtnQkFDQ3RCLE9BQU87b0JBQ0x1QixVQUFVO29CQUNWSyxLQUFLO29CQUNMQyxNQUFNO29CQUNOQyxPQUFPO29CQUNQRSxpQkFBaUI7b0JBQ2pCSyxTQUFTO29CQUNUVixjQUFjO29CQUNkVyxXQUFXO29CQUNYRixRQUFRO29CQUNSZ0IsVUFBVTtvQkFDVkMsV0FBVztvQkFDWHJGLE9BQU87Z0JBQ1Q7MEJBQ0Q7Ozs7Ozs7Ozs7OztBQU1UIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWlycG9ydC1yaWRlLWJvb2tpbmcvLi9zcmMvY29tcG9uZW50cy9CYXNpY01hcC50c3g/MzllZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBMb2NhdGlvbiB7XG4gIGxhdDogbnVtYmVyO1xuICBsbmc6IG51bWJlcjtcbiAgYWRkcmVzcz86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIEJhc2ljTWFwUHJvcHMge1xuICBoZWlnaHQ/OiBzdHJpbmc7XG4gIHNlbGVjdGFibGU/OiBib29sZWFuO1xuICBvbkxvY2F0aW9uU2VsZWN0ZWQ/OiAobG9jYXRpb246IExvY2F0aW9uKSA9PiB2b2lkO1xuICBpbml0aWFsTG9jYXRpb24/OiBMb2NhdGlvbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQmFzaWNNYXAoe1xuICBoZWlnaHQgPSAnMzAwcHgnLFxuICBzZWxlY3RhYmxlID0gZmFsc2UsXG4gIG9uTG9jYXRpb25TZWxlY3RlZCxcbiAgaW5pdGlhbExvY2F0aW9uXG59OiBCYXNpY01hcFByb3BzKSB7XG4gIGNvbnN0IG1hcENvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IG1hcFJlZiA9IHVzZVJlZjxhbnk+KG51bGwpO1xuXG4gIC8vIE1ldGhvZCB0byB1cGRhdGUgdGhlIG1hcCB3aXRoIGEgbmV3IGxvY2F0aW9uXG4gIGNvbnN0IHVwZGF0ZU1hcExvY2F0aW9uID0gKGxvY2F0aW9uOiBMb2NhdGlvbikgPT4ge1xuICAgIGlmICghbWFwUmVmLmN1cnJlbnQgfHwgIWxvY2F0aW9uIHx8IGxvY2F0aW9uLmxhdCA9PT0gMCB8fCBsb2NhdGlvbi5sbmcgPT09IDApIHJldHVybjtcblxuICAgIC8vIFJlbW92ZSBleGlzdGluZyBtYXJrZXJzXG4gICAgY29uc3QgbWFya2VycyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5tYXBib3hnbC1tYXJrZXInKTtcbiAgICBtYXJrZXJzLmZvckVhY2gobWFya2VyID0+IG1hcmtlci5yZW1vdmUoKSk7XG5cbiAgICAvLyBBZGQgYSBuZXcgbWFya2VyXG4gICAgbmV3IHdpbmRvdy5tYXBib3hnbC5NYXJrZXIoeyBjb2xvcjogJyMzYjgyZjYnIH0pXG4gICAgICAuc2V0TG5nTGF0KFtsb2NhdGlvbi5sbmcsIGxvY2F0aW9uLmxhdF0pXG4gICAgICAuYWRkVG8obWFwUmVmLmN1cnJlbnQpO1xuXG4gICAgLy8gRmx5IHRvIHRoZSBuZXcgbG9jYXRpb25cbiAgICBtYXBSZWYuY3VycmVudC5mbHlUbyh7XG4gICAgICBjZW50ZXI6IFtsb2NhdGlvbi5sbmcsIGxvY2F0aW9uLmxhdF0sXG4gICAgICB6b29tOiAxNCxcbiAgICAgIGVzc2VudGlhbDogdHJ1ZVxuICAgIH0pO1xuXG4gICAgLy8gQWRkIHBvcHVwIHdpdGggYWRkcmVzcyBpZiBhdmFpbGFibGVcbiAgICBpZiAobG9jYXRpb24uYWRkcmVzcykge1xuICAgICAgbmV3IHdpbmRvdy5tYXBib3hnbC5Qb3B1cCh7IG9mZnNldDogMjUsIGNsb3NlQnV0dG9uOiBmYWxzZSB9KVxuICAgICAgICAuc2V0TG5nTGF0KFtsb2NhdGlvbi5sbmcsIGxvY2F0aW9uLmxhdF0pXG4gICAgICAgIC5zZXRIVE1MKGA8cCBzdHlsZT1cIm1hcmdpbjogMDtcIj4ke2xvY2F0aW9uLmFkZHJlc3N9PC9wPmApXG4gICAgICAgIC5hZGRUbyhtYXBSZWYuY3VycmVudCk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTG9hZCBNYXBib3ggc2NyaXB0IGR5bmFtaWNhbGx5XG4gICAgY29uc3QgbG9hZE1hcGJveCA9ICgpID0+IHtcbiAgICAgIC8vIENoZWNrIGlmIE1hcGJveCBpcyBhbHJlYWR5IGxvYWRlZFxuICAgICAgaWYgKHdpbmRvdy5tYXBib3hnbCkge1xuICAgICAgICBpbml0aWFsaXplTWFwKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHNjcmlwdCBlbGVtZW50XG4gICAgICBjb25zdCBzY3JpcHQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzY3JpcHQnKTtcbiAgICAgIHNjcmlwdC5zcmMgPSAnaHR0cHM6Ly9hcGkubWFwYm94LmNvbS9tYXBib3gtZ2wtanMvdjIuMTUuMC9tYXBib3gtZ2wuanMnO1xuICAgICAgc2NyaXB0LmFzeW5jID0gdHJ1ZTtcbiAgICAgIHNjcmlwdC5vbmxvYWQgPSAoKSA9PiB7XG4gICAgICAgIC8vIEluaXRpYWxpemUgbWFwIGFmdGVyIHNjcmlwdCBsb2Fkc1xuICAgICAgICBpbml0aWFsaXplTWFwKCk7XG4gICAgICB9O1xuICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChzY3JpcHQpO1xuXG4gICAgICAvLyBBZGQgQ1NTXG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGluaycpO1xuICAgICAgbGluay5ocmVmID0gJ2h0dHBzOi8vYXBpLm1hcGJveC5jb20vbWFwYm94LWdsLWpzL3YyLjE1LjAvbWFwYm94LWdsLmNzcyc7XG4gICAgICBsaW5rLnJlbCA9ICdzdHlsZXNoZWV0JztcbiAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQobGluayk7XG4gICAgfTtcblxuICAgIC8vIEluaXRpYWxpemUgdGhlIG1hcFxuICAgIGNvbnN0IGluaXRpYWxpemVNYXAgPSAoKSA9PiB7XG4gICAgICBpZiAoIW1hcENvbnRhaW5lclJlZi5jdXJyZW50IHx8ICF3aW5kb3cubWFwYm94Z2wpIHJldHVybjtcblxuICAgICAgLy8gU2V0IGFjY2VzcyB0b2tlblxuICAgICAgd2luZG93Lm1hcGJveGdsLmFjY2Vzc1Rva2VuID0gJ3BrLmV5SjFJam9pT1RJME5ERXpPREk1SWl3aVlTSTZJbU50T1haaU5ITjNlVEJtY21VeWFuSXllV0Z4YW5oaVpXOGlmUS5ROE5vY2tCWkY3STctc2I3VE4yTzhBJztcblxuICAgICAgLy8gRGV0ZXJtaW5lIGNlbnRlciBjb29yZGluYXRlc1xuICAgICAgbGV0IGNlbnRlckxuZyA9IDM0LjU5MjU7IC8vIERlZmF1bHQgdG8gR2FtYmVsYSwgRXRoaW9waWFcbiAgICAgIGxldCBjZW50ZXJMYXQgPSA4LjI0ODM7XG5cbiAgICAgIC8vIFVzZSBpbml0aWFsTG9jYXRpb24gaWYgcHJvdmlkZWRcbiAgICAgIGlmIChpbml0aWFsTG9jYXRpb24gJiYgaW5pdGlhbExvY2F0aW9uLmxhdCAhPT0gMCAmJiBpbml0aWFsTG9jYXRpb24ubG5nICE9PSAwKSB7XG4gICAgICAgIGNlbnRlckxuZyA9IGluaXRpYWxMb2NhdGlvbi5sbmc7XG4gICAgICAgIGNlbnRlckxhdCA9IGluaXRpYWxMb2NhdGlvbi5sYXQ7XG4gICAgICB9XG5cbiAgICAgIC8vIE5vIGF1dG9tYXRpYyBnZW9sb2NhdGlvbiAtIHdlJ3JlIG1ha2luZyBsb2NhdGlvbiBzZWxlY3Rpb24gZnVsbHkgbWFudWFsXG4gICAgICBjb25zb2xlLmxvZygnVXNpbmcgbWFudWFsIGxvY2F0aW9uIHNlbGVjdGlvbiBvbmx5IC0gYXV0b21hdGljIGdlb2xvY2F0aW9uIGRpc2FibGVkJyk7XG5cbiAgICAgIC8vIENyZWF0ZSBtYXBcbiAgICAgIGNvbnN0IG1hcCA9IG5ldyB3aW5kb3cubWFwYm94Z2wuTWFwKHtcbiAgICAgICAgY29udGFpbmVyOiBtYXBDb250YWluZXJSZWYuY3VycmVudCxcbiAgICAgICAgc3R5bGU6ICdtYXBib3g6Ly9zdHlsZXMvbWFwYm94L3N0cmVldHMtdjExJyxcbiAgICAgICAgY2VudGVyOiBbY2VudGVyTG5nLCBjZW50ZXJMYXRdLFxuICAgICAgICB6b29tOiAxMywgLy8gQ2xvc2VyIHpvb20gZm9yIGJldHRlciBsb2NhdGlvbiB2aXNpYmlsaXR5XG4gICAgICB9KTtcblxuICAgICAgLy8gU3RvcmUgbWFwIHJlZmVyZW5jZVxuICAgICAgbWFwUmVmLmN1cnJlbnQgPSBtYXA7XG5cbiAgICAgIC8vIEFkZCBuYXZpZ2F0aW9uIGNvbnRyb2xzXG4gICAgICBtYXAuYWRkQ29udHJvbChuZXcgd2luZG93Lm1hcGJveGdsLk5hdmlnYXRpb25Db250cm9sKCkpO1xuXG4gICAgICAvLyBBZGQgYSBtYXJrZXIgZm9yIHRoZSBpbml0aWFsIGxvY2F0aW9uIGlmIHByb3ZpZGVkXG4gICAgICBpZiAoaW5pdGlhbExvY2F0aW9uICYmIGluaXRpYWxMb2NhdGlvbi5sYXQgIT09IDAgJiYgaW5pdGlhbExvY2F0aW9uLmxuZyAhPT0gMCkge1xuICAgICAgICAvLyBDcmVhdGUgYSBtYXJrZXIgd2l0aCBhIHBvcHVwIHNob3dpbmcgdGhlIGFkZHJlc3NcbiAgICAgICAgbmV3IHdpbmRvdy5tYXBib3hnbC5NYXJrZXIoeyBjb2xvcjogJyMzYjgyZjYnIH0pXG4gICAgICAgICAgLnNldExuZ0xhdChbaW5pdGlhbExvY2F0aW9uLmxuZywgaW5pdGlhbExvY2F0aW9uLmxhdF0pXG4gICAgICAgICAgLmFkZFRvKG1hcCk7XG5cbiAgICAgICAgLy8gQWRkIHBvcHVwIHdpdGggYWRkcmVzcyBpZiBhdmFpbGFibGVcbiAgICAgICAgaWYgKGluaXRpYWxMb2NhdGlvbi5hZGRyZXNzKSB7XG4gICAgICAgICAgbmV3IHdpbmRvdy5tYXBib3hnbC5Qb3B1cCh7IG9mZnNldDogMjUsIGNsb3NlQnV0dG9uOiBmYWxzZSB9KVxuICAgICAgICAgICAgLnNldExuZ0xhdChbaW5pdGlhbExvY2F0aW9uLmxuZywgaW5pdGlhbExvY2F0aW9uLmxhdF0pXG4gICAgICAgICAgICAuc2V0SFRNTChgPHAgc3R5bGU9XCJtYXJnaW46IDA7XCI+JHtpbml0aWFsTG9jYXRpb24uYWRkcmVzc308L3A+YClcbiAgICAgICAgICAgIC5hZGRUbyhtYXApO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBBZGQgYSBkZWZhdWx0IG1hcmtlciBpZiBubyBpbml0aWFsIGxvY2F0aW9uXG4gICAgICAgIG5ldyB3aW5kb3cubWFwYm94Z2wuTWFya2VyKClcbiAgICAgICAgICAuc2V0TG5nTGF0KFtjZW50ZXJMbmcsIGNlbnRlckxhdF0pXG4gICAgICAgICAgLmFkZFRvKG1hcCk7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCBjbGljayBoYW5kbGVyIGZvciBsb2NhdGlvbiBzZWxlY3Rpb24gaWYgc2VsZWN0YWJsZSBpcyB0cnVlXG4gICAgICBpZiAoc2VsZWN0YWJsZSAmJiBvbkxvY2F0aW9uU2VsZWN0ZWQpIHtcbiAgICAgICAgbWFwLm9uKCdjbGljaycsIGFzeW5jIChlOiB7IGxuZ0xhdDogeyBsbmc6IG51bWJlcjsgbGF0OiBudW1iZXIgfSB9KSA9PiB7XG4gICAgICAgICAgY29uc3QgeyBsbmcsIGxhdCB9ID0gZS5sbmdMYXQ7XG5cbiAgICAgICAgICAvLyBSZW1vdmUgZXhpc3RpbmcgbWFya2Vyc1xuICAgICAgICAgIGNvbnN0IG1hcmtlcnMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubWFwYm94Z2wtbWFya2VyJyk7XG4gICAgICAgICAgbWFya2Vycy5mb3JFYWNoKG1hcmtlciA9PiBtYXJrZXIucmVtb3ZlKCkpO1xuXG4gICAgICAgICAgLy8gQ3JlYXRlIGEgbmV3IG1hcmtlciBhdCB0aGUgY2xpY2tlZCBsb2NhdGlvblxuICAgICAgICAgIG5ldyB3aW5kb3cubWFwYm94Z2wuTWFya2VyKHsgY29sb3I6ICcjM2I4MmY2JyB9KVxuICAgICAgICAgICAgLnNldExuZ0xhdChbbG5nLCBsYXRdKVxuICAgICAgICAgICAgLmFkZFRvKG1hcCk7XG5cbiAgICAgICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gR2V0IGFkZHJlc3MgZnJvbSBjb29yZGluYXRlcyB1c2luZyBNYXBib3ggR2VvY29kaW5nIEFQSVxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcbiAgICAgICAgICAgICAgYGh0dHBzOi8vYXBpLm1hcGJveC5jb20vZ2VvY29kaW5nL3Y1L21hcGJveC5wbGFjZXMvJHtsbmd9LCR7bGF0fS5qc29uP2FjY2Vzc190b2tlbj0ke3dpbmRvdy5tYXBib3hnbC5hY2Nlc3NUb2tlbn1gXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgICAgICAgbGV0IGFkZHJlc3MgPSAnVW5rbm93biBsb2NhdGlvbic7XG4gICAgICAgICAgICBpZiAoZGF0YS5mZWF0dXJlcyAmJiBkYXRhLmZlYXR1cmVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgYWRkcmVzcyA9IGRhdGEuZmVhdHVyZXNbMF0ucGxhY2VfbmFtZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gQ3JlYXRlIHBvcHVwIHdpdGggYWRkcmVzcyBhbmQgc2VsZWN0IGJ1dHRvblxuICAgICAgICAgICAgY29uc3QgcG9wdXAgPSBuZXcgd2luZG93Lm1hcGJveGdsLlBvcHVwKHsgb2Zmc2V0OiAyNSB9KVxuICAgICAgICAgICAgICAuc2V0TG5nTGF0KFtsbmcsIGxhdF0pXG4gICAgICAgICAgICAgIC5zZXRIVE1MKGBcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPVwidGV4dC1hbGlnbjogY2VudGVyO1wiPlxuICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW4tYm90dG9tOiA4cHg7XCI+JHthZGRyZXNzfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gaWQ9XCJzZWxlY3QtbG9jYXRpb25cIiBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICMzYjgyZjY7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyOiBub25lOyBwYWRkaW5nOiA0cHggOHB4OyBib3JkZXItcmFkaXVzOiA0cHg7IGN1cnNvcjogcG9pbnRlcjtcIj5cbiAgICAgICAgICAgICAgICAgICAgU2VsZWN0IHRoaXMgbG9jYXRpb25cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBgKVxuICAgICAgICAgICAgICAuYWRkVG8obWFwKTtcblxuICAgICAgICAgICAgLy8gQWRkIGV2ZW50IGxpc3RlbmVyIHRvIHRoZSBzZWxlY3QgYnV0dG9uXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0QnV0dG9uID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NlbGVjdC1sb2NhdGlvbicpO1xuICAgICAgICAgICAgICBpZiAoc2VsZWN0QnV0dG9uKSB7XG4gICAgICAgICAgICAgICAgc2VsZWN0QnV0dG9uLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgb25Mb2NhdGlvblNlbGVjdGVkKHtcbiAgICAgICAgICAgICAgICAgICAgbGF0LFxuICAgICAgICAgICAgICAgICAgICBsbmcsXG4gICAgICAgICAgICAgICAgICAgIGFkZHJlc3NcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgcG9wdXAucmVtb3ZlKCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIDEwMCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlb2NvZGluZyBsb2NhdGlvbjonLCBlcnJvcik7XG4gICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gQ2xlYW4gdXAgb24gdW5tb3VudFxuICAgICAgcmV0dXJuICgpID0+IG1hcC5yZW1vdmUoKTtcbiAgICB9O1xuXG4gICAgbG9hZE1hcGJveCgpO1xuICB9LCBbc2VsZWN0YWJsZSwgb25Mb2NhdGlvblNlbGVjdGVkXSk7XG5cbiAgLy8gVXBkYXRlIG1hcCB3aGVuIGluaXRpYWxMb2NhdGlvbiBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGluaXRpYWxMb2NhdGlvbiAmJiBpbml0aWFsTG9jYXRpb24ubGF0ICE9PSAwICYmIGluaXRpYWxMb2NhdGlvbi5sbmcgIT09IDApIHtcbiAgICAgIC8vIFdhaXQgZm9yIG1hcCB0byBiZSBpbml0aWFsaXplZFxuICAgICAgY29uc3QgY2hlY2tNYXBJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgaWYgKG1hcFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgdXBkYXRlTWFwTG9jYXRpb24oaW5pdGlhbExvY2F0aW9uKTtcbiAgICAgICAgICBjbGVhckludGVydmFsKGNoZWNrTWFwSW50ZXJ2YWwpO1xuICAgICAgICB9XG4gICAgICB9LCAxMDApO1xuXG4gICAgICAvLyBDbGVhciBpbnRlcnZhbCBhZnRlciA1IHNlY29uZHMgdG8gcHJldmVudCBtZW1vcnkgbGVha3NcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gY2xlYXJJbnRlcnZhbChjaGVja01hcEludGVydmFsKSwgNTAwMCk7XG4gICAgfVxuICB9LCBbaW5pdGlhbExvY2F0aW9uXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IHBvc2l0aW9uOiAncmVsYXRpdmUnIH19PlxuICAgICAgPGRpdlxuICAgICAgICByZWY9e21hcENvbnRhaW5lclJlZn1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgIGhlaWdodCxcbiAgICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2RkZCcsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNHB4JyxcbiAgICAgICAgfX1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBMb2FkaW5nIGluZGljYXRvciAqL31cbiAgICAgIHtpc0xvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgICAgbGVmdDogMCxcbiAgICAgICAgICAgIHJpZ2h0OiAwLFxuICAgICAgICAgICAgYm90dG9tOiAwLFxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjIpJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgekluZGV4OiAxMCxcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMTJweCcsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSknLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMjRcIiBoZWlnaHQ9XCIyNFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cIiMzYjgyZjZcIiBzdHJva2VXaWR0aD1cIjRcIiBvcGFjaXR5PVwiMC4yNVwiIC8+XG4gICAgICAgICAgICAgIDxjaXJjbGVcbiAgICAgICAgICAgICAgICBjeD1cIjEyXCJcbiAgICAgICAgICAgICAgICBjeT1cIjEyXCJcbiAgICAgICAgICAgICAgICByPVwiMTBcIlxuICAgICAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgICAgICBzdHJva2U9XCIjM2I4MmY2XCJcbiAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZURhc2hhcnJheT1cIjYwIDMwXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBhbmltYXRpb246ICdzcGluIDFzIGxpbmVhciBpbmZpbml0ZScgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHN0eWxlPntgXG4gICAgICAgICAgICAgICAgQGtleWZyYW1lcyBzcGluIHtcbiAgICAgICAgICAgICAgICAgIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cbiAgICAgICAgICAgICAgICAgIDEwMCUgeyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBgfTwvc3R5bGU+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogSW5zdHJ1Y3Rpb25zICovfVxuICAgICAge3NlbGVjdGFibGUgJiYgKFxuICAgICAgICA8ZGl2XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgdG9wOiAnMTBweCcsXG4gICAgICAgICAgICBsZWZ0OiAnMTBweCcsXG4gICAgICAgICAgICByaWdodDogJzEwcHgnLFxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgcGFkZGluZzogJzhweCAxMnB4JyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzRweCcsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcbiAgICAgICAgICAgIHpJbmRleDogNSxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgY29sb3I6ICcjNGI1NTYzJyxcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgQ2xpY2sgYW55d2hlcmUgb24gdGhlIG1hcCB0byBtYW51YWxseSBzZWxlY3QgeW91ciBwaWNrdXAgbG9jYXRpb25cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuXG4vLyBBZGQgdHlwZSBkZWZpbml0aW9uIGZvciB3aW5kb3cubWFwYm94Z2xcbmRlY2xhcmUgZ2xvYmFsIHtcbiAgaW50ZXJmYWNlIFdpbmRvdyB7XG4gICAgbWFwYm94Z2w6IGFueTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiQmFzaWNNYXAiLCJoZWlnaHQiLCJzZWxlY3RhYmxlIiwib25Mb2NhdGlvblNlbGVjdGVkIiwiaW5pdGlhbExvY2F0aW9uIiwibWFwQ29udGFpbmVyUmVmIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwibWFwUmVmIiwidXBkYXRlTWFwTG9jYXRpb24iLCJsb2NhdGlvbiIsImN1cnJlbnQiLCJsYXQiLCJsbmciLCJtYXJrZXJzIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yQWxsIiwiZm9yRWFjaCIsIm1hcmtlciIsInJlbW92ZSIsIndpbmRvdyIsIm1hcGJveGdsIiwiTWFya2VyIiwiY29sb3IiLCJzZXRMbmdMYXQiLCJhZGRUbyIsImZseVRvIiwiY2VudGVyIiwiem9vbSIsImVzc2VudGlhbCIsImFkZHJlc3MiLCJQb3B1cCIsIm9mZnNldCIsImNsb3NlQnV0dG9uIiwic2V0SFRNTCIsImxvYWRNYXBib3giLCJpbml0aWFsaXplTWFwIiwic2NyaXB0IiwiY3JlYXRlRWxlbWVudCIsInNyYyIsImFzeW5jIiwib25sb2FkIiwiaGVhZCIsImFwcGVuZENoaWxkIiwibGluayIsImhyZWYiLCJyZWwiLCJhY2Nlc3NUb2tlbiIsImNlbnRlckxuZyIsImNlbnRlckxhdCIsImNvbnNvbGUiLCJsb2ciLCJtYXAiLCJNYXAiLCJjb250YWluZXIiLCJzdHlsZSIsImFkZENvbnRyb2wiLCJOYXZpZ2F0aW9uQ29udHJvbCIsIm9uIiwiZSIsImxuZ0xhdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJkYXRhIiwianNvbiIsImZlYXR1cmVzIiwibGVuZ3RoIiwicGxhY2VfbmFtZSIsInBvcHVwIiwic2V0VGltZW91dCIsInNlbGVjdEJ1dHRvbiIsImdldEVsZW1lbnRCeUlkIiwiYWRkRXZlbnRMaXN0ZW5lciIsImVycm9yIiwiY2hlY2tNYXBJbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImRpdiIsInBvc2l0aW9uIiwicmVmIiwid2lkdGgiLCJib3JkZXIiLCJib3JkZXJSYWRpdXMiLCJ0b3AiLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiekluZGV4IiwicGFkZGluZyIsImJveFNoYWRvdyIsInN2ZyIsInZpZXdCb3giLCJ4bWxucyIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwib3BhY2l0eSIsInN0cm9rZURhc2hhcnJheSIsImFuaW1hdGlvbiIsImZvbnRTaXplIiwidGV4dEFsaWduIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/BasicMap.tsx\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Navbar__WEBPACK_IMPORTED_MODULE_2__]);\n_Navbar__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Layout({ children, title = \"BaroRide\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Book your ride with BaroRide - fixed fares and reliable service\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white text-gray-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-100 border-t border-gray-200 py-4 mt-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center text-gray-600 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" BaroRide. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NotificationBell */ \"./src/components/NotificationBell.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _NotificationBell__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _NotificationBell__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Navbar() {\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBackButton, setShowBackButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Determine if back button should be shown based on current route\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Show back button on all pages except home page\n        setShowBackButton(router.pathname !== \"/\");\n    }, [\n        router.pathname\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleBack = ()=>{\n        router.back();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-2 sm:px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors\",\n                                    \"aria-label\": \"Go back\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 sm:w-7 sm:h-7 text-gray-700\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo-icon.svg\",\n                                            alt: \"BaroRide Logo\",\n                                            className: \"h-8 w-8 sm:h-10 sm:w-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600 font-bold text-lg sm:text-xl\",\n                                            children: \"BaroRide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/book\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Book a Ride\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/driver/dashboard\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: user.fullName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-1\",\n                            children: [\n                                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 22\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 border-b border-gray-100 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 capitalize\",\n                                        children: user.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this),\n                            user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/book\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDCF1 Book a Ride\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 19\n                            }, this) : user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/driver/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDE97 Driver Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"⚙️ Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    signOut();\n                                    setIsMenuOpen(false);\n                                },\n                                className: \"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                children: \"\\uD83D\\uDEAA Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDD11 Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"✨ Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n");

/***/ }),

/***/ "./src/components/Notification.tsx":
/*!*****************************************!*\
  !*** ./src/components/Notification.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Notification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Notification({ message, type = \"info\", duration = 5000, onClose, driverDetails }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setIsVisible(false);\n            if (onClose) onClose();\n        }, duration);\n        return ()=>clearTimeout(timer);\n    }, [\n        duration,\n        onClose\n    ]);\n    if (!isVisible) return null;\n    const getTypeStyles = ()=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 border-green-500 text-green-700\";\n            case \"warning\":\n                return \"bg-yellow-100 border-yellow-500 text-yellow-700\";\n            case \"error\":\n                return \"bg-red-100 border-red-500 text-red-700\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 border-blue-500 text-blue-700\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this);\n            case \"info\":\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 max-w-md shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `p-4 mb-4 text-sm rounded-lg border ${getTypeStyles()}`,\n            role: \"alert\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mr-2\",\n                            children: getIcon()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium flex-grow whitespace-pre-line\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500\",\n                            onClick: ()=>{\n                                setIsVisible(false);\n                                if (onClose) onClose();\n                            },\n                            \"aria-label\": \"Close\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-gray-600\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold\",\n                                        children: driverDetails.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            driverDetails.vehicleColor,\n                                            \" \",\n                                            driverDetails.vehicleMake,\n                                            \" \",\n                                            driverDetails.vehicleModel\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs font-medium\",\n                                        children: [\n                                            \"License Plate: \",\n                                            driverDetails.licensePlate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Phone: \",\n                                            driverDetails.phoneNumber\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Notification.tsx\n");

/***/ }),

/***/ "./src/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction NotificationBell() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) return;\n        // Listen for user notifications\n        // Note: We're not using orderBy to avoid Firestore index issues\n        const notificationsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", user.id));\n        const unsubscribe = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.onSnapshot)(notificationsQuery, (snapshot)=>{\n            const userNotifications = [];\n            let count = 0;\n            snapshot.forEach((doc)=>{\n                const data = doc.data();\n                const notification = {\n                    id: doc.id,\n                    ...data,\n                    createdAt: data.createdAt?.toDate() || new Date()\n                };\n                userNotifications.push(notification);\n                if (!notification.read) {\n                    count++;\n                }\n            });\n            // Sort notifications by createdAt in descending order (newest first)\n            userNotifications.sort((a, b)=>{\n                return b.createdAt.getTime() - a.createdAt.getTime();\n            });\n            setNotifications(userNotifications);\n            setUnreadCount(count);\n        });\n        return ()=>unsubscribe();\n    }, [\n        user\n    ]);\n    const markAsRead = async (notificationId)=>{\n        if (!user) return;\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notificationId), {\n                read: true\n            });\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n        }\n    };\n    const markAllAsRead = async ()=>{\n        if (!user) return;\n        try {\n            const promises = notifications.filter((notification)=>!notification.read).map((notification)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notification.id), {\n                    read: true\n                }));\n            await Promise.all(promises);\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n        }\n    };\n    const toggleDropdown = ()=>{\n        setShowDropdown(!showDropdown);\n    };\n    const getNotificationTypeStyles = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 text-blue-800\";\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleDropdown,\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                \"aria-label\": \"Notifications\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\",\n                        children: unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-100 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: markAllAsRead,\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                children: \"No notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `px-4 py-3 border-b border-gray-100 ${!notification.read ? \"bg-blue-50\" : \"\"}`,\n                                    onClick: ()=>markAsRead(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 rounded-full p-1 ${getNotificationTypeStyles(notification.type)}`,\n                                                children: [\n                                                    notification.type === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"warning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-900 whitespace-pre-line\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-500\",\n                                                        children: new Date(notification.createdAt).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    notification.driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold\",\n                                                                            children: notification.driverDetails.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                notification.driverDetails.vehicleColor,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleMake,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleModel\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium\",\n                                                                            children: [\n                                                                                \"License: \",\n                                                                                notification.driverDetails.licensePlate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/NotificationBell.tsx\n");

/***/ }),

/***/ "./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/RBACContext */ \"./src/contexts/RBACContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction ProtectedRoute({ children, requiredRoles, redirectTo = \"/login\" }) {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { checkAccess } = (0,_contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.useRBAC)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__.useNotification)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait until auth is loaded\n        if (loading) return;\n        // If no user is logged in, redirect to login\n        if (!user) {\n            router.push(redirectTo);\n            showNotification(\"Please log in to access this page\", \"warning\");\n            return;\n        }\n        // Check if user has the required role\n        const hasAccess = checkAccess(requiredRoles);\n        if (!hasAccess) {\n            // Determine where to redirect based on user's role\n            let redirectPath = \"/\";\n            let message = \"Access denied. You do not have permission to view this page.\";\n            if (user.role === \"driver\") {\n                redirectPath = \"/driver/dashboard\";\n                message = \"Access denied. Redirected to driver dashboard.\";\n            } else if (user.role === \"rider\") {\n                redirectPath = \"/\";\n                message = \"Access denied. Redirected to home page.\";\n            }\n            router.push(redirectPath);\n            showNotification(message, \"warning\");\n        }\n    }, [\n        user,\n        loading,\n        requiredRoles,\n        router,\n        redirectTo,\n        checkAccess\n    ]);\n    // Show nothing while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated or doesn't have required role, don't render children\n    if (!user || !checkAccess(requiredRoles)) {\n        return null;\n    }\n    // If authenticated and has required role, render children\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__]);\n([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.onAuthStateChanged)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", firebaseUser.uid));\n                const userData = userDoc.data();\n                setUser(userData);\n                setLoading(false);\n                // Auto-redirect users based on role if they're on the homepage\n                if (router.pathname === \"/\") {\n                    if (userData.role === \"admin\") {\n                        console.log(\"Admin detected on homepage, redirecting to admin dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/admin/dashboard\");\n                        }, 100);\n                    } else if (userData.role === \"driver\") {\n                        console.log(\"Driver detected on homepage, redirecting to driver dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/driver/dashboard\");\n                        }, 100);\n                    }\n                }\n            } else {\n                setUser(null);\n                setLoading(false);\n            }\n        });\n        return unsubscribe;\n    }, [\n        router\n    ]);\n    const signOut = async ()=>{\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth);\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Notification */ \"./src/components/Notification.tsx\");\n\n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    showNotification: ()=>{}\n});\nfunction NotificationProvider({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showNotification = (message, type = \"info\", duration = 5000, driverDetails)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type,\n                    duration,\n                    driverDetails\n                }\n            ]);\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            showNotification\n        },\n        children: [\n            children,\n            notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Notification__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    message: notification.message,\n                    type: notification.type,\n                    duration: notification.duration,\n                    driverDetails: notification.driverDetails,\n                    onClose: ()=>removeNotification(notification.id)\n                }, notification.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nconst useNotification = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "./src/contexts/RBACContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/RBACContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RBACProvider: () => (/* binding */ RBACProvider),\n/* harmony export */   useRBAC: () => (/* binding */ useRBAC)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// Create the context with default values\nconst RBACContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    hasAccess: ()=>false,\n    checkAccess: ()=>false,\n    userRole: \"guest\",\n    isAdmin: false,\n    isDriver: false,\n    isRider: false\n});\n// Define route access permissions\nconst routeAccess = {\n    \"/\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/login\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/signup\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/forgot-password\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/book\": [\n        \"admin\",\n        \"rider\"\n    ],\n    \"/driver\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/driver/dashboard\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/admin\": [\n        \"admin\"\n    ],\n    \"/admin/dashboard\": [\n        \"admin\"\n    ],\n    \"/admin/users\": [\n        \"admin\"\n    ],\n    \"/admin/bookings\": [\n        \"admin\"\n    ]\n};\n// RBAC Provider component\nfunction RBACProvider({ children }) {\n    const { user, loading } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showNotification } = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification)();\n    const [authorized, setAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Determine user role\n    const userRole = user ? user.role === \"admin\" ? \"admin\" : user.role === \"driver\" ? \"driver\" : \"rider\" : \"guest\";\n    // Check if user has access to a specific route\n    const hasAccess = (route)=>{\n        // Check if the route exists in our access map\n        const allowedRoles = routeAccess[route];\n        // If route is not defined in our access map, default to admin-only\n        if (!allowedRoles) {\n            return userRole === \"admin\";\n        }\n        // Check if user's role is in the allowed roles for this route\n        return allowedRoles.includes(userRole);\n    };\n    // Check if user has one of the required roles\n    const checkAccess = (requiredRoles)=>{\n        return requiredRoles.includes(userRole);\n    };\n    // Role-specific boolean flags for easier checks\n    const isAdmin = userRole === \"admin\";\n    const isDriver = userRole === \"driver\";\n    const isRider = userRole === \"rider\";\n    // Route protection effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Skip during initial loading\n        if (loading) return;\n        // Get the current path\n        const path = router.pathname;\n        // Check if user has access to the current route\n        const hasRouteAccess = hasAccess(path);\n        if (!hasRouteAccess) {\n            // Redirect to appropriate page based on role\n            if (userRole === \"guest\") {\n                router.push(\"/login\");\n                showNotification(\"Please log in to access this page\", \"warning\");\n            } else if (userRole === \"driver\") {\n                router.push(\"/driver/dashboard\");\n                showNotification(\"Access denied. Redirected to driver dashboard.\", \"warning\");\n            } else if (userRole === \"rider\") {\n                router.push(\"/\");\n                showNotification(\"Access denied. Redirected to home page.\", \"warning\");\n            } else {\n                router.push(\"/\");\n                showNotification(\"Access denied. Please contact support if you believe this is an error.\", \"error\");\n            }\n        } else {\n            setAuthorized(true);\n        }\n    }, [\n        router.pathname,\n        userRole,\n        loading\n    ]);\n    // Provide the context value\n    const contextValue = {\n        hasAccess,\n        checkAccess,\n        userRole,\n        isAdmin,\n        isDriver,\n        isRider\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RBACContext.Provider, {\n        value: contextValue,\n        children: authorized ? children : null\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\RBACContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the RBAC context\nconst useRBAC = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RBACContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/RBACContext.tsx\n");

/***/ }),

/***/ "./src/firebase/config.ts":
/*!********************************!*\
  !*** ./src/firebase/config.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"firebase/storage\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po\",\n    authDomain: \"baroride.firebaseapp.com\",\n    projectId: \"baroride\",\n    storageBucket: \"baroride.firebasestorage.app\",\n    messagingSenderId: \"191771619835\",\n    appId: \"1:191771619835:web:2fc57d131cf64a35e2db5e\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Configure Firestore for better performance in production\nif (false) {}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/firebase/config.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/RBACContext */ \"./src/contexts/RBACContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.RBACProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBRXdCO0FBQ2dCO0FBQ2hCO0FBRXRELFNBQVNHLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDL0MscUJBQ0UsOERBQUNMLCtEQUFZQTtrQkFDWCw0RUFBQ0MsK0VBQW9CQTtzQkFDbkIsNEVBQUNDLCtEQUFZQTswQkFDWCw0RUFBQ0U7b0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2xDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJwb3J0LXJpZGUtYm9va2luZy8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgTm90aWZpY2F0aW9uUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL05vdGlmaWNhdGlvbkNvbnRleHQnO1xuaW1wb3J0IHsgUkJBQ1Byb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9SQkFDQ29udGV4dCc7XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPE5vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgICAgICA8UkJBQ1Byb3ZpZGVyPlxuICAgICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgICAgPC9SQkFDUHJvdmlkZXI+XG4gICAgICA8L05vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDtcbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJOb3RpZmljYXRpb25Qcm92aWRlciIsIlJCQUNQcm92aWRlciIsIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-navbutton-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FFSCw4REFBQ0s7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FHOUIsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUF5QkMsU0FBUTs7Ozs7O2tDQUM1Qyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQStCQyxTQUFROzs7Ozs7a0NBQ2xELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBd0NDLFNBQVE7Ozs7OztrQ0FDM0QsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUE2QkMsU0FBUTs7Ozs7O2tDQUdoRCw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQW1CQyxTQUFROzs7Ozs7a0NBR3RDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQWdDQyxTQUFROzs7Ozs7a0NBQ25ELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBd0NDLFNBQVE7Ozs7OztrQ0FHM0QsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFhQyxNQUFLOzs7Ozs7a0NBQzVCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzt3QkFBNEJDLGFBQVk7Ozs7Ozs7Ozs7OzswQkFFdEUsOERBQUNDO2dCQUFLQyxXQUFVOztrQ0FDZCw4REFBQ1osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpcnBvcnQtcmlkZS1ib29raW5nLy4vc3JjL3BhZ2VzL19kb2N1bWVudC50c3g/MTg4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxuICAgICAgPEhlYWQ+XG4gICAgICAgIHsvKiBFc3NlbnRpYWwgbW9iaWxlIHZpZXdwb3J0IG1ldGEgdGFnICovfVxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgbWF4aW11bS1zY2FsZT0xLjAsIHVzZXItc2NhbGFibGU9bm8sIHZpZXdwb3J0LWZpdD1jb3ZlclwiIC8+XG5cbiAgICAgICAgey8qIE1vYmlsZSB3ZWIgYXBwIGNhcGFiaWxpdGllcyAqL31cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXN0YXR1cy1iYXItc3R5bGVcIiBjb250ZW50PVwiZGVmYXVsdFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsZS1tb2JpbGUtd2ViLWFwcC10aXRsZVwiIGNvbnRlbnQ9XCJCYXJvUmlkZVwiIC8+XG5cbiAgICAgICAgey8qIFByZXZlbnQgYXV0b21hdGljIHBob25lIG51bWJlciBkZXRlY3Rpb24gKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJmb3JtYXQtZGV0ZWN0aW9uXCIgY29udGVudD1cInRlbGVwaG9uZT1ub1wiIC8+XG5cbiAgICAgICAgey8qIFRoZW1lIGNvbG9yIGZvciBtb2JpbGUgYnJvd3NlcnMgKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjMWUzYTVmXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tbmF2YnV0dG9uLWNvbG9yXCIgY29udGVudD1cIiMxZTNhNWZcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJibGFjay10cmFuc2x1Y2VudFwiIC8+XG5cbiAgICAgICAgey8qIFByZWNvbm5lY3QgdG8gaW1wcm92ZSBwZXJmb3JtYW5jZSAqL31cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdzdGF0aWMuY29tXCIgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiY3Jvc3NPcmlnaW4iLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/book/index.tsx":
/*!**********************************!*\
  !*** ./src/pages/book/index.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookRidePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _components_BasicMap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BasicMap */ \"./src/components/BasicMap.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/firebase-helpers */ \"./src/utils/firebase-helpers.ts\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"./src/components/ProtectedRoute.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__, _components_Layout__WEBPACK_IMPORTED_MODULE_6__, _utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__, _components_Layout__WEBPACK_IMPORTED_MODULE_6__, _utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__, _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst AIRPORTS = [\n    {\n        code: \"GMB\",\n        name: \"Gambela International Airport\"\n    }\n];\nfunction BookRide() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    const [pickupLocation, setPickupLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        address: \"\",\n        lat: 0,\n        lng: 0\n    });\n    const [selectedAirport, setSelectedAirport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(AIRPORTS[0]);\n    const [passengers, setPassengers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSelectingOnMap, setIsSelectingOnMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [estimatedFare, setEstimatedFare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previousBookings, setPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingBookings, setIsLoadingBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreviousBookings, setShowPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedBookingId, setSelectedBookingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingGps, setIsLoadingGps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch previous bookings when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Fetch previous bookings if user is logged in\n        if (user) {\n            fetchPreviousBookings();\n        }\n    }, [\n        user\n    ]);\n    // Fetch user's previous bookings\n    const fetchPreviousBookings = async ()=>{\n        if (!user) return;\n        setIsLoadingBookings(true);\n        try {\n            // Log for debugging in production\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                console.log(\"Fetching previous bookings in Firebase hosting environment\");\n            }\n            // Use our safe query function if we're in production, otherwise use regular query\n            let bookings = [];\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use the safe query function with retry logic\n                const constraints = [\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5)\n                ];\n                const results = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeQueryDocs)(\"bookings\", constraints);\n                bookings = results.map((data)=>({\n                        id: data.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    }));\n            } else {\n                // Regular query for development environment\n                const bookingsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5) // Limit to 5 most recent bookings\n                );\n                const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(bookingsQuery);\n                querySnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    // Convert Firestore timestamps to Date objects\n                    const booking = {\n                        id: doc.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    };\n                    bookings.push(booking);\n                });\n            }\n            setPreviousBookings(bookings);\n            console.log(`Fetched ${bookings.length} previous bookings`);\n        } catch (error) {\n            console.error(\"Error fetching previous bookings:\", error);\n            showNotification(\"Could not load your previous bookings. Please try again later.\", \"warning\");\n        } finally{\n            setIsLoadingBookings(false);\n        }\n    };\n    // Clear selected booking and pickup location\n    const clearSelection = ()=>{\n        setPickupLocation({\n            address: \"\",\n            lat: 0,\n            lng: 0\n        });\n        setSelectedBookingId(null);\n    };\n    // Toggle between map selection and manual entry\n    const toggleMapSelection = ()=>{\n        setIsSelectingOnMap(!isSelectingOnMap);\n    };\n    // Handle location selection from map\n    const handleLocationSelected = (location)=>{\n        // Validate the location\n        if (!location || typeof location.lat !== \"number\" || typeof location.lng !== \"number\") {\n            showNotification(\"Invalid location selected. Please try again.\", \"error\");\n            return;\n        }\n        // Create a valid location object\n        const newLocation = {\n            lat: location.lat,\n            lng: location.lng,\n            address: location.address || \"Selected location\"\n        };\n        console.log(\"Location selected from map:\", newLocation);\n        // Update state\n        setPickupLocation(newLocation);\n        // Clear selected booking since we're selecting a new location\n        setSelectedBookingId(null);\n        setIsSelectingOnMap(false);\n        // Calculate fare for the new location\n        const fare = calculateFare(newLocation);\n        setEstimatedFare(fare);\n        // Show confirmation to the user\n        showNotification(\"Pickup location selected successfully!\", \"success\");\n    };\n    // Toggle showing previous bookings\n    const togglePreviousBookings = ()=>{\n        setShowPreviousBookings(!showPreviousBookings);\n    };\n    // Get user's current location using GPS - manual action\n    const getUserLocation = async ()=>{\n        if (!navigator.geolocation) {\n            showNotification(\"Geolocation is not supported by your browser. Please enter your location manually.\", \"error\");\n            return;\n        }\n        setIsLoadingGps(true);\n        showNotification(\"Getting your current location...\", \"info\");\n        try {\n            // Get current position with better timeout and error handling\n            const position = await new Promise((resolve, reject)=>{\n                navigator.geolocation.getCurrentPosition((position)=>{\n                    console.log(\"GPS position obtained:\", position.coords);\n                    resolve(position);\n                }, (error)=>{\n                    console.error(\"GPS error:\", error.code, error.message);\n                    let errorMessage = \"Unable to retrieve your location.\";\n                    // Provide more specific error messages\n                    if (error.code === 1) {\n                        errorMessage = \"Location access denied. Please enable location services in your browser settings.\";\n                    } else if (error.code === 2) {\n                        errorMessage = \"Your current position is unavailable. Please try again later.\";\n                    } else if (error.code === 3) {\n                        errorMessage = \"Location request timed out. Please try again.\";\n                    }\n                    reject(new Error(errorMessage));\n                }, {\n                    enableHighAccuracy: true,\n                    timeout: 10000,\n                    maximumAge: 0\n                });\n            });\n            const { latitude, longitude } = position.coords;\n            console.log(`GPS coordinates: ${latitude}, ${longitude}`);\n            try {\n                // Get address from coordinates using Mapbox Geocoding API\n                const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A`);\n                if (!response.ok) {\n                    throw new Error(`Geocoding API error: ${response.status}`);\n                }\n                const data = await response.json();\n                console.log(\"Geocoding response:\", data);\n                let address = \"Your current location\";\n                if (data.features && data.features.length > 0) {\n                    address = data.features[0].place_name;\n                    console.log(\"Address found:\", address);\n                }\n                // Create location object\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address\n                };\n                // Update pickup location\n                setPickupLocation(location);\n                // Clear selected booking\n                setSelectedBookingId(null);\n                // Calculate fare for the new location\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                // Show success notification\n                showNotification(\"Your current location has been set as the pickup point.\", \"success\");\n            } catch (geocodingError) {\n                console.error(\"Error getting address:\", geocodingError);\n                // Still set the location even if geocoding fails\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address: \"Your current location\"\n                };\n                setPickupLocation(location);\n                setSelectedBookingId(null);\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                showNotification(\"Location set, but we couldn't get your exact address. You can edit it manually.\", \"warning\");\n            }\n        } catch (error) {\n            console.error(\"Error getting location:\", error);\n            showNotification(error instanceof Error ? error.message : \"Unable to retrieve your location. Please try again or select manually.\", \"error\");\n        } finally{\n            setIsLoadingGps(false);\n        }\n    };\n    // Select a previous booking\n    const selectPreviousBooking = (booking)=>{\n        // Update pickup location\n        setPickupLocation(booking.pickupLocation);\n        // Update airport if it exists in our list\n        const airport = AIRPORTS.find((a)=>a.code === booking.airport.code);\n        if (airport) {\n            setSelectedAirport(airport);\n        }\n        // Set the selected booking ID\n        setSelectedBookingId(booking.id);\n        // Hide the previous bookings dropdown\n        setShowPreviousBookings(false);\n    };\n    // Calculate fare based on distance and number of passengers\n    const calculateFare = (location = pickupLocation)=>{\n        // In a real app, you would calculate based on distance between pickup and airport\n        // For this example, we'll use a base fare plus a random amount based on coordinates\n        if (location.lat === 0 || location.lng === 0) {\n            return 0;\n        }\n        const baseFare = 35;\n        // Use the coordinates to create a somewhat realistic variable fare\n        // This is just for demonstration - in a real app you'd use actual distance calculation\n        const seed = location.lat * location.lng % 100;\n        const distanceFare = Math.floor(10 + seed / 5);\n        // Add $5 per additional passenger\n        const passengerFare = (passengers - 1) * 5;\n        return baseFare + distanceFare + passengerFare;\n    };\n    // Update estimated fare when pickup location or passengers count changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pickupLocation.lat !== 0 && pickupLocation.lng !== 0) {\n            const fare = calculateFare(pickupLocation);\n            setEstimatedFare(fare);\n        }\n    }, [\n        pickupLocation,\n        passengers\n    ]);\n    // Create booking directly (no payment required)\n    const createBooking = async ()=>{\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        try {\n            // Show loading notification\n            showNotification(\"Creating your booking...\", \"info\");\n            // Create timestamp objects for Firestore\n            const now = new Date();\n            // Create the booking object with all required fields\n            const booking = {\n                riderId: user.id,\n                pickupLocation: {\n                    address: pickupLocation.address,\n                    lat: pickupLocation.lat,\n                    lng: pickupLocation.lng\n                },\n                airport: {\n                    name: selectedAirport.name,\n                    code: selectedAirport.code\n                },\n                status: \"pending\",\n                fare: estimatedFare > 0 ? estimatedFare : calculateFare(),\n                passengers: passengers,\n                createdAt: now,\n                updatedAt: now\n            };\n            console.log(\"Creating booking with data:\", booking);\n            // Add the booking to Firestore with our safe function if in production\n            let bookingId;\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use safe function with built-in retry logic\n                console.log(\"Using safe function to create booking in production\");\n                bookingId = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"bookings\", booking);\n                console.log(\"Booking created with ID:\", bookingId);\n            } else {\n                // Use standard function in development\n                try {\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created with ID:\", bookingId);\n                } catch (error) {\n                    console.error(\"Error creating booking:\", error);\n                    showNotification(\"Retrying booking creation...\", \"info\");\n                    // Second attempt after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created on second attempt with ID:\", bookingId);\n                }\n            }\n            // Update the user's booking history\n            try {\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeUpdateDoc)(\"users\", user.id, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                } else {\n                    // Use standard function in development\n                    const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id);\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userRef);\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(userRef, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                }\n            } catch (historyError) {\n                // Non-critical error, log but continue\n                console.error(\"Error updating booking history:\", historyError);\n            }\n            // Create a notification for the user\n            try {\n                const notificationData = {\n                    userId: user.id,\n                    message: \"Your ride has been booked successfully. Waiting for a driver to accept.\",\n                    type: \"info\",\n                    read: false,\n                    relatedBookingId: bookingId,\n                    createdAt: now\n                };\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"notifications\", notificationData);\n                } else {\n                    // Use standard function in development\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), notificationData);\n                }\n                console.log(\"Created notification for user\");\n            } catch (notificationError) {\n                // Non-critical error, log but continue\n                console.error(\"Error creating notification:\", notificationError);\n            }\n            // Show success notification\n            showNotification(\"Booking created successfully! Waiting for a driver to accept.\", \"success\");\n            // Reset form after successful booking\n            setPickupLocation({\n                address: \"\",\n                lat: 0,\n                lng: 0\n            });\n            setPassengers(1);\n            setEstimatedFare(0);\n            setSelectedBookingId(null);\n        } catch (error) {\n            console.error(\"Error creating booking:\", error);\n            // Provide more detailed error messages\n            let errorMessage = \"Failed to create booking. Please try again.\";\n            if (error instanceof Error) {\n                if (error.message.includes(\"network\")) {\n                    errorMessage = \"Network error. Please check your internet connection and try again.\";\n                } else if (error.message.includes(\"permission-denied\")) {\n                    errorMessage = \"Permission denied. Please log out and log back in.\";\n                } else if (error.message.includes(\"not-found\")) {\n                    errorMessage = \"Database connection error. Please refresh the page and try again.\";\n                }\n            }\n            showNotification(errorMessage, \"error\");\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        // Validate pickup location\n        if (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) {\n            showNotification(\"Please select a valid pickup location.\", \"error\");\n            return;\n        }\n        // Create booking directly (no payment required)\n        await createBooking();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        title: \"BaroRide - Book a Ride\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-2 sm:p-4 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900\",\n                            children: \"Book Your Ride\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm sm:text-base text-gray-600 mt-1\",\n                            children: \"Quick and easy airport transportation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 sm:mb-0\",\n                                            children: \"Pickup Location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleMapSelection,\n                                                    className: \"text-xs sm:text-sm text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-colors touch-manipulation\",\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    children: isSelectingOnMap ? \"\\uD83D\\uDCDD Manual Entry\" : \"\\uD83D\\uDDFA️ Select on Map\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: getUserLocation,\n                                                    className: \"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center\",\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    disabled: isLoadingGps,\n                                                    children: isLoadingGps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Getting...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: \"\\uD83D\\uDCCD My Location\"\n                                                    }, void 0, false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this),\n                                                previousBookings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePreviousBookings,\n                                                    className: \"text-sm text-green-500 hover:text-green-700\",\n                                                    children: showPreviousBookings ? \"Hide Previous\" : \"Use Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this),\n                                !isSelectingOnMap ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Enter your pickup address manually\",\n                                                    className: \"w-full p-2 border rounded\",\n                                                    value: pickupLocation.address,\n                                                    onChange: (e)=>{\n                                                        setPickupLocation((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            }));\n                                                        // Clear selected booking when manually editing\n                                                        if (selectedBookingId) {\n                                                            setSelectedBookingId(null);\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pickupLocation.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearSelection,\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-4 w-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3 inline-block mr-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                'Enter your address manually, click \"Use My Location\", or select it on the map'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: \"Click on the map to manually select your pickup location\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, this),\n                                showPreviousBookings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 border rounded shadow-sm overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 px-3 py-2 border-b\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Previous Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-60 overflow-y-auto\",\n                                            children: isLoadingBookings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Loading...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 21\n                                            }, this) : previousBookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-sm text-gray-600\",\n                                                children: \"No previous bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: previousBookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: `p-3 hover:bg-gray-50 cursor-pointer transition-colors ${selectedBookingId === booking.id ? \"bg-blue-50 border-l-4 border-blue-500\" : \"\"}`,\n                                                        onClick: ()=>selectPreviousBooking(booking),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: booking.pickupLocation.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"To: \",\n                                                                                booking.airport.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mr-2\",\n                                                                            children: new Date(booking.updatedAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        selectedBookingId === booking.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-500 font-medium\",\n                                                                            children: \"Selected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, booking.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Pickup Location Map\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BasicMap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    height: \"300px\",\n                                    selectable: isSelectingOnMap,\n                                    onLocationSelected: handleLocationSelected,\n                                    initialLocation: pickupLocation.lat !== 0 ? pickupLocation : undefined\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this),\n                                pickupLocation.lat !== 0 && pickupLocation.lng !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-blue-50 border border-blue-100 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            \"Selected Pickup: \",\n                                                            pickupLocation.address\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedBookingId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                        children: \"From Previous Booking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: clearSelection,\n                                                className: \"ml-2 text-xs text-red-500 hover:text-red-700\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Select Airport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedAirport.code,\n                                            onChange: (e)=>{\n                                                const airport = AIRPORTS.find((a)=>a.code === e.target.value);\n                                                if (airport) setSelectedAirport(airport);\n                                            },\n                                            className: \"w-full p-2 border rounded\",\n                                            children: AIRPORTS.map((airport)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: airport.code,\n                                                    children: airport.name\n                                                }, airport.code, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Number of Passengers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.max(1, prev - 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-l hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M20 12H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"8\",\n                                                    value: passengers,\n                                                    onChange: (e)=>setPassengers(Math.max(1, Math.min(8, parseInt(e.target.value) || 1))),\n                                                    className: \"w-full p-2 border-t border-b text-center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.min(8, prev + 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-r hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M12 6v12M6 12h12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Maximum 8 passengers per ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 11\n                        }, this),\n                        estimatedFare > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-800 mb-2\",\n                                    children: \"Fare Estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Base fare\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Additional passengers (\",\n                                                        passengers - 1,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"$35.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        Math.floor(10 + pickupLocation.lat * pickupLocation.lng % 100 / 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((passengers - 1) * 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: \"Total estimated fare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: [\n                                                \"$\",\n                                                estimatedFare.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"* Actual fare may vary based on traffic, weather, and other factors.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: `w-full ${!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0 ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-500 hover:bg-blue-600\"} text-white p-3 rounded font-medium transition-colors`,\n                                    disabled: !pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0,\n                                    children: !user ? \"Please Log In to Book a Ride\" : \"Book Ride Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, this),\n                                (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-red-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm\",\n                                            children: \"Please enter or select a pickup location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, this),\n                                !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-yellow-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500 text-sm\",\n                                            children: \"You need to be logged in to book a ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 754,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n// Wrap the component with ProtectedRoute\nfunction BookRidePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        requiredRoles: [\n            \"admin\",\n            \"rider\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BookRide, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 795,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 794,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/book/index.tsx\n");

/***/ }),

/***/ "./src/utils/firebase-helpers.ts":
/*!***************************************!*\
  !*** ./src/utils/firebase-helpers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isFirebaseHosting: () => (/* binding */ isFirebaseHosting),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   safeAddDoc: () => (/* binding */ safeAddDoc),\n/* harmony export */   safeGetDoc: () => (/* binding */ safeGetDoc),\n/* harmony export */   safeQueryDocs: () => (/* binding */ safeQueryDocs),\n/* harmony export */   safeUpdateDoc: () => (/* binding */ safeUpdateDoc)\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_firestore__WEBPACK_IMPORTED_MODULE_0__, _firebase_config__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * Helper function to safely get a document from Firestore with retry logic\n */ const safeGetDoc = async (path, id, maxRetries = 2)=>{\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path, id);\n            const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n            if (docSnap.exists()) {\n                return {\n                    id: docSnap.id,\n                    ...docSnap.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            retries++;\n            console.error(`Error getting document (attempt ${retries}/${maxRetries}):`, error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    return null;\n};\n/**\n * Helper function to safely query documents from Firestore with retry logic\n */ const safeQueryDocs = async (path, constraints, maxRetries = 2)=>{\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path), ...constraints);\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            const results = [];\n            querySnapshot.forEach((doc)=>{\n                results.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            return results;\n        } catch (error) {\n            retries++;\n            console.error(`Error querying documents (attempt ${retries}/${maxRetries}):`, error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    return [];\n};\n/**\n * Helper function to safely add a document to Firestore with retry logic\n */ const safeAddDoc = async (path, data, maxRetries = 2)=>{\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path), data);\n            return docRef.id;\n        } catch (error) {\n            retries++;\n            console.error(`Error adding document (attempt ${retries}/${maxRetries}):`, error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    throw new Error(\"Failed to add document after maximum retries\");\n};\n/**\n * Helper function to safely update a document in Firestore with retry logic\n */ const safeUpdateDoc = async (path, id, data, maxRetries = 2)=>{\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path, id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, data);\n            return;\n        } catch (error) {\n            retries++;\n            console.error(`Error updating document (attempt ${retries}/${maxRetries}):`, error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    throw new Error(\"Failed to update document after maximum retries\");\n};\n/**\n * Check if the app is running in a production environment\n */ const isProduction = ()=>{\n    return \"development\" === \"production\";\n};\n/**\n * Check if the app is running in a browser environment\n */ const isBrowser = ()=>{\n    return \"undefined\" !== \"undefined\";\n};\n/**\n * Check if the app is running on Firebase hosting\n */ const isFirebaseHosting = ()=>{\n    if (!isBrowser()) return false;\n    // Check if the URL contains firebase hosting domains\n    const hostname = window.location.hostname;\n    return hostname.includes(\"firebaseapp.com\") || hostname.includes(\"web.app\") || hostname === \"baroride.web.app\";\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/firebase-helpers.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "firebase/storage":
/*!***********************************!*\
  !*** external "firebase/storage" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/storage");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fbook&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cbook%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();