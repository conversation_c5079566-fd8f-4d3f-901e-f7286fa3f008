(()=>{var e={};e.id=888,e.ids=[888],e.modules={6317:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{H:()=>u,a:()=>x});var i=s(997),a=s(6689),l=s(3462),d=s(401),n=s(1492),o=s(1163),c=e([l,d,n]);[l,d,n]=c.then?(await c)():c;let m=(0,a.createContext)({user:null,loading:!0,signOut:async()=>{}});function u({children:e}){let[r,s]=(0,a.useState)(null),[t,n]=(0,a.useState)(!0);(0,o.useRouter)();let c=async()=>{await (0,d.signOut)(l.auth),s(null)};return i.jsx(m.Provider,{value:{user:r,loading:t,signOut:c},children:e})}let x=()=>(0,a.useContext)(m);t()}catch(e){t(e)}})},1530:(e,r,s)=>{"use strict";s.d(r,{J:()=>d,l:()=>n});var t=s(997),i=s(6689);function a({message:e,type:r="info",duration:s=5e3,onClose:a,driverDetails:l}){let[d,n]=(0,i.useState)(!0);return d?t.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-md shadow-lg",children:(0,t.jsxs)("div",{className:`p-4 mb-4 text-sm rounded-lg border ${(()=>{switch(r){case"success":return"bg-green-100 border-green-500 text-green-700";case"warning":return"bg-yellow-100 border-yellow-500 text-yellow-700";case"error":return"bg-red-100 border-red-500 text-red-700";default:return"bg-blue-100 border-blue-500 text-blue-700"}})()}`,role:"alert",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"mr-2",children:(()=>{switch(r){case"success":return t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"warning":return t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"error":return t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}})()}),t.jsx("div",{className:"font-medium flex-grow whitespace-pre-line",children:e}),(0,t.jsxs)("button",{type:"button",className:"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500",onClick:()=>{n(!1),a&&a()},"aria-label":"Close",children:[t.jsx("span",{className:"sr-only",children:"Close"}),t.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),l&&t.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3",children:t.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-semibold",children:l.fullName}),(0,t.jsxs)("p",{className:"text-xs",children:[l.vehicleColor," ",l.vehicleMake," ",l.vehicleModel]}),(0,t.jsxs)("p",{className:"text-xs font-medium",children:["License Plate: ",l.licensePlate]}),(0,t.jsxs)("p",{className:"text-xs",children:["Phone: ",l.phoneNumber]})]})]})})]})}):null}let l=(0,i.createContext)({showNotification:()=>{}});function d({children:e}){let[r,s]=(0,i.useState)([]),d=e=>{s(r=>r.filter(r=>r.id!==e))};return(0,t.jsxs)(l.Provider,{value:{showNotification:(e,r="info",t=5e3,i)=>{let a=Date.now().toString();s(s=>[...s,{id:a,message:e,type:r,duration:t,driverDetails:i}])}},children:[e,r.map(e=>t.jsx(a,{message:e.message,type:e.type,duration:e.duration,driverDetails:e.driverDetails,onClose:()=>d(e.id)},e.id))]})}let n=()=>(0,i.useContext)(l)},2942:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{r:()=>c,s:()=>x});var i=s(997),a=s(6689),l=s(6317),d=s(1163),n=s(1530),o=e([l]);l=(o.then?(await o)():o)[0];let u=(0,a.createContext)({hasAccess:()=>!1,checkAccess:()=>!1,userRole:"guest",isAdmin:!1,isDriver:!1,isRider:!1}),m={"/":["admin","driver","rider","guest"],"/login":["admin","driver","rider","guest"],"/signup":["admin","driver","rider","guest"],"/forgot-password":["admin","driver","rider","guest"],"/book":["admin","rider"],"/driver":["admin","driver"],"/driver/dashboard":["admin","driver"],"/admin":["admin"],"/admin/dashboard":["admin"],"/admin/users":["admin"],"/admin/bookings":["admin"]};function c({children:e}){let{user:r,loading:s}=(0,l.a)();(0,d.useRouter)();let{showNotification:t}=(0,n.l)(),[o,c]=(0,a.useState)(!1),x=r?"admin"===r.role?"admin":"driver"===r.role?"driver":"rider":"guest",h="admin"===x,v="driver"===x,p="rider"===x;return i.jsx(u.Provider,{value:{hasAccess:e=>{let r=m[e];return r?r.includes(x):"admin"===x},checkAccess:e=>e.includes(x),userRole:x,isAdmin:h,isDriver:v,isRider:p},children:o?e:null})}let x=()=>(0,a.useContext)(u);t()}catch(e){t(e)}})},3462:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{auth:()=>c,db:()=>u});var i=s(3745),a=s(401),l=s(1492),d=s(3392),n=e([i,a,l,d]);[i,a,l,d]=n.then?(await n)():n;let o=(0,i.initializeApp)({apiKey:"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po",authDomain:"baroride.firebaseapp.com",projectId:"baroride",storageBucket:"baroride.firebasestorage.app",messagingSenderId:"191771619835",appId:"1:191771619835:web:2fc57d131cf64a35e2db5e"}),c=(0,a.getAuth)(o),u=(0,l.getFirestore)(o);(0,d.getStorage)(o),t()}catch(e){t(e)}})},3893:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>o});var i=s(997);s(108);var a=s(6317),l=s(1530),d=s(2942),n=e([a,d]);[a,d]=n.then?(await n)():n;let o=function({Component:e,pageProps:r}){return i.jsx(a.H,{children:i.jsx(l.J,{children:i.jsx(d.r,{children:i.jsx(e,{...r})})})})};t()}catch(e){t(e)}})},108:()=>{},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3745:e=>{"use strict";e.exports=import("firebase/app")},401:e=>{"use strict";e.exports=import("firebase/auth")},1492:e=>{"use strict";e.exports=import("firebase/firestore")},3392:e=>{"use strict";e.exports=import("firebase/storage")}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[567,163],()=>s(3893));module.exports=t})();