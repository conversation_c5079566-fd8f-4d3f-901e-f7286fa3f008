exports.id=754,exports.ids=[754],exports.modules={9532:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>c});var i=a(997),r=a(968),l=a.n(r),o=a(7792),n=a(4699),d=e([o]);function c({children:e,title:t="BaroRide"}){return(0,i.jsxs)(n.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:t}),i.jsx("meta",{name:"description",content:"Book your ride with BaroRide - fixed fares and reliable service"}),i.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"}),i.jsx("meta",{name:"theme-color",content:"#1e3a5f"}),i.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),i.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),i.jsx("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),i.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),i.jsx("meta",{name:"format-detection",content:"telephone=no"}),i.jsx("link",{rel:"icon",href:"/logo-icon.svg"}),i.jsx("link",{rel:"apple-touch-icon",href:"/logo-icon.svg"}),i.jsx("link",{rel:"shortcut icon",href:"/logo-icon.svg"})]}),i.jsx(n.x,{}),(0,i.jsxs)("div",{className:"min-h-screen flex flex-col bg-white text-gray-900 safe-area-top safe-area-bottom",children:[i.jsx(o.Z,{}),i.jsx("main",{className:"flex-grow w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-6 safe-area-left safe-area-right",children:e}),i.jsx("footer",{className:"bg-gray-100 border-t border-gray-200 py-4 mt-auto safe-area-bottom",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center text-gray-600 text-sm",children:["\xa9 ",new Date().getFullYear()," BaroRide. All rights reserved."]})})]})]})}o=(d.then?(await d)():d)[0],s()}catch(e){s(e)}})},4699:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l,x:()=>o});var s=a(997),i=a(6689),r=a(9335);function l({children:e}){return s.jsx(s.Fragment,{children:e})}let o=()=>((0,i.useEffect)(()=>{if((0,r.dz)().isMobile){let e=document.createElement("style");return e.textContent=`
        /* Mobile-specific overrides */
        .is-mobile input,
        .is-mobile select,
        .is-mobile textarea {
          font-size: 16px !important;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }

        .is-mobile button {
          min-height: 44px;
          touch-action: manipulation;
          -webkit-tap-highlight-color: transparent;
        }

        .is-mobile .map-container {
          touch-action: none;
        }

        /* Prevent zoom on input focus */
        .is-mobile input:focus,
        .is-mobile select:focus,
        .is-mobile textarea:focus {
          font-size: 16px !important;
        }

        /* Better scrolling on mobile */
        .is-mobile {
          -webkit-overflow-scrolling: touch;
        }

        /* Hide scrollbars on mobile for cleaner look */
        .is-mobile ::-webkit-scrollbar {
          width: 0px;
          background: transparent;
        }

        /* Mobile-specific form improvements */
        .is-mobile .mobile-form {
          padding: 16px;
        }

        .is-mobile .mobile-form input,
        .is-mobile .mobile-form select,
        .is-mobile .mobile-form textarea {
          padding: 16px;
          border-radius: 12px;
          border: 2px solid #e5e7eb;
          font-size: 16px !important;
        }

        .is-mobile .mobile-form button {
          padding: 16px;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
        }

        /* Mobile navigation improvements */
        .is-mobile .mobile-nav {
          position: sticky;
          top: 0;
          z-index: 50;
          background: white;
          border-bottom: 1px solid #e5e7eb;
          padding: 12px 16px;
        }

        /* Mobile card improvements */
        .is-mobile .mobile-card {
          margin: 8px;
          border-radius: 16px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Mobile modal improvements */
        .is-mobile .mobile-modal {
          padding: 16px;
        }

        .is-mobile .mobile-modal-content {
          border-radius: 16px;
          max-height: 85vh;
        }

        /* Mobile table improvements */
        .is-mobile .mobile-table {
          font-size: 14px;
        }

        .is-mobile .mobile-table th,
        .is-mobile .mobile-table td {
          padding: 12px 8px;
        }

        /* Mobile notification improvements */
        .is-mobile .mobile-notification {
          margin: 16px;
          border-radius: 12px;
          padding: 16px;
          font-size: 16px;
        }

        /* Keyboard handling */
        .is-mobile.keyboard-open {
          position: fixed;
          width: 100%;
        }

        /* Safe area handling for devices with notches */
        .is-mobile .safe-area-top {
          padding-top: max(16px, env(safe-area-inset-top));
        }

        .is-mobile .safe-area-bottom {
          padding-bottom: max(16px, env(safe-area-inset-bottom));
        }

        .is-mobile .safe-area-left {
          padding-left: max(16px, env(safe-area-inset-left));
        }

        .is-mobile .safe-area-right {
          padding-right: max(16px, env(safe-area-inset-right));
        }
      `,document.head.appendChild(e),()=>{document.head.removeChild(e)}}},[]),null)},7792:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>x});var i=a(997),r=a(6689),l=a(1664),o=a.n(l),n=a(1163),d=a(6317),c=a(2794),m=e([d,c]);function x(){let{user:e,signOut:t}=(0,d.a)(),[a,s]=(0,r.useState)(!1),[l,m]=(0,r.useState)(!1),x=(0,n.useRouter)(),h=()=>{s(!a)};return(0,i.jsxs)("nav",{className:"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50",children:[i.jsx("div",{className:"container mx-auto px-2 sm:px-4",children:(0,i.jsxs)("div",{className:"flex justify-between h-14 sm:h-16",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[l&&i.jsx("button",{onClick:()=>{x.back()},className:"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors","aria-label":"Go back",style:{touchAction:"manipulation"},children:i.jsx("svg",{className:"w-5 h-5 sm:w-7 sm:h-7 text-gray-700",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,i.jsxs)(o(),{href:"/",className:"flex-shrink-0 flex items-center space-x-2",children:[i.jsx("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-8 w-8 sm:h-10 sm:w-10"}),i.jsx("span",{className:"text-blue-600 font-bold text-lg sm:text-xl",children:"BaroRide"})]})]}),i.jsx("div",{className:"hidden md:flex items-center space-x-4",children:e?(0,i.jsxs)(i.Fragment,{children:["rider"===e.role?i.jsx(o(),{href:"/book",className:"text-gray-700 hover:text-blue-600",children:"Book a Ride"}):i.jsx(o(),{href:"/driver/dashboard",className:"text-gray-700 hover:text-blue-600",children:"Dashboard"}),i.jsx(c.Z,{}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsxs)("button",{onClick:h,className:"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none",children:[i.jsx("span",{className:"mr-1",children:e.fullName}),i.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),a&&i.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:i.jsx("button",{onClick:t,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign Out"})})]})]}):(0,i.jsxs)(i.Fragment,{children:[i.jsx(o(),{href:"/login",className:"text-gray-700 hover:text-blue-600",children:"Login"}),i.jsx(o(),{href:"/signup",className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Sign Up"})]})}),(0,i.jsxs)("div",{className:"md:hidden flex items-center space-x-1",children:[e&&i.jsx(c.Z,{}),i.jsx("button",{onClick:h,className:"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors",style:{touchAction:"manipulation"},"aria-label":"Toggle menu",children:i.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a?i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"}):i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})})]})]})}),a&&i.jsx("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:i.jsx("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100 mb-2",children:[i.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.fullName}),i.jsx("p",{className:"text-xs text-gray-500 capitalize",children:e.role})]}),"rider"===e.role?i.jsx(o(),{href:"/book",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>s(!1),children:"\uD83D\uDCF1 Book a Ride"}):"driver"===e.role?i.jsx(o(),{href:"/driver/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>s(!1),children:"\uD83D\uDE97 Driver Dashboard"}):i.jsx(o(),{href:"/admin/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>s(!1),children:"⚙️ Admin Dashboard"}),i.jsx("button",{onClick:()=>{t(),s(!1)},className:"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},children:"\uD83D\uDEAA Sign Out"})]}):(0,i.jsxs)(i.Fragment,{children:[i.jsx(o(),{href:"/login",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>s(!1),children:"\uD83D\uDD11 Login"}),i.jsx(o(),{href:"/signup",className:"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>s(!1),children:"✨ Sign Up"})]})})})]})}[d,c]=m.then?(await m)():m,s()}catch(e){s(e)}})},2794:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>c});var i=a(997),r=a(6689),l=a(6317),o=a(3462),n=a(1492),d=e([l,o,n]);function c(){let{user:e}=(0,l.a)(),[t,a]=(0,r.useState)([]),[s,d]=(0,r.useState)(!1),[c,m]=(0,r.useState)(0),x=async t=>{if(e)try{await (0,n.updateDoc)((0,n.doc)(o.db,"notifications",t),{read:!0})}catch(e){console.error("Error marking notification as read:",e)}},h=async()=>{if(e)try{let e=t.filter(e=>!e.read).map(e=>(0,n.updateDoc)((0,n.doc)(o.db,"notifications",e.id),{read:!0}));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}},u=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"warning":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-blue-100 text-blue-800"}};return e?(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsxs)("button",{onClick:()=>{d(!s)},className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[i.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),c>0&&i.jsx("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:c})]}),s&&i.jsx("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20",children:(0,i.jsxs)("div",{className:"py-2",children:[(0,i.jsxs)("div",{className:"px-4 py-2 bg-gray-100 flex justify-between items-center",children:[i.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Notifications"}),c>0&&i.jsx("button",{onClick:h,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),i.jsx("div",{className:"max-h-96 overflow-y-auto",children:0===t.length?i.jsx("div",{className:"px-4 py-3 text-sm text-gray-500",children:"No notifications"}):t.map(e=>i.jsx("div",{className:`px-4 py-3 border-b border-gray-100 ${e.read?"":"bg-blue-50"}`,onClick:()=>x(e.id),children:(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsxs)("div",{className:`flex-shrink-0 rounded-full p-1 ${u(e.type)}`,children:["success"===e.type&&i.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"warning"===e.type&&i.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),"error"===e.type&&i.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),"info"===e.type&&i.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})]}),(0,i.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[i.jsx("p",{className:"text-sm text-gray-900 whitespace-pre-line",children:e.message}),i.jsx("p",{className:"mt-1 text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()}),e.driverDetails&&i.jsx("div",{className:"mt-2 pt-2 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2",children:i.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,i.jsxs)("div",{children:[i.jsx("p",{className:"text-xs font-semibold",children:e.driverDetails.fullName}),(0,i.jsxs)("p",{className:"text-xs",children:[e.driverDetails.vehicleColor," ",e.driverDetails.vehicleMake," ",e.driverDetails.vehicleModel]}),(0,i.jsxs)("p",{className:"text-xs font-medium",children:["License: ",e.driverDetails.licensePlate]})]})]})})]})]})},e.id))})]})})]}):null}[l,o,n]=d.then?(await d)():d,s()}catch(e){s(e)}})},6317:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{H:()=>m,a:()=>h});var i=a(997),r=a(6689),l=a(3462),o=a(401),n=a(1492),d=a(1163),c=e([l,o,n]);[l,o,n]=c.then?(await c)():c;let x=(0,r.createContext)({user:null,loading:!0,signOut:async()=>{}});function m({children:e}){let[t,a]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0);(0,d.useRouter)();let c=async()=>{await (0,o.signOut)(l.auth),a(null)};return i.jsx(x.Provider,{value:{user:t,loading:s,signOut:c},children:e})}let h=()=>(0,r.useContext)(x);s()}catch(e){s(e)}})},1530:(e,t,a)=>{"use strict";a.d(t,{J:()=>o,l:()=>n});var s=a(997),i=a(6689);function r({message:e,type:t="info",duration:a=5e3,onClose:r,driverDetails:l}){let[o,n]=(0,i.useState)(!0);return o?s.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-md shadow-lg",children:(0,s.jsxs)("div",{className:`p-4 mb-4 text-sm rounded-lg border ${(()=>{switch(t){case"success":return"bg-green-100 border-green-500 text-green-700";case"warning":return"bg-yellow-100 border-yellow-500 text-yellow-700";case"error":return"bg-red-100 border-red-500 text-red-700";default:return"bg-blue-100 border-blue-500 text-blue-700"}})()}`,role:"alert",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"mr-2",children:(()=>{switch(t){case"success":return s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"warning":return s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"error":return s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}})()}),s.jsx("div",{className:"font-medium flex-grow whitespace-pre-line",children:e}),(0,s.jsxs)("button",{type:"button",className:"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500",onClick:()=>{n(!1),r&&r()},"aria-label":"Close",children:[s.jsx("span",{className:"sr-only",children:"Close"}),s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),l&&s.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3",children:s.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-semibold",children:l.fullName}),(0,s.jsxs)("p",{className:"text-xs",children:[l.vehicleColor," ",l.vehicleMake," ",l.vehicleModel]}),(0,s.jsxs)("p",{className:"text-xs font-medium",children:["License Plate: ",l.licensePlate]}),(0,s.jsxs)("p",{className:"text-xs",children:["Phone: ",l.phoneNumber]})]})]})})]})}):null}let l=(0,i.createContext)({showNotification:()=>{}});function o({children:e}){let[t,a]=(0,i.useState)([]),o=e=>{a(t=>t.filter(t=>t.id!==e))};return(0,s.jsxs)(l.Provider,{value:{showNotification:(e,t="info",s=5e3,i)=>{let r=Date.now().toString();a(a=>[...a,{id:r,message:e,type:t,duration:s,driverDetails:i}])}},children:[e,t.map(e=>s.jsx(r,{message:e.message,type:e.type,duration:e.duration,driverDetails:e.driverDetails,onClose:()=>o(e.id)},e.id))]})}let n=()=>(0,i.useContext)(l)},2942:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{r:()=>c,s:()=>h});var i=a(997),r=a(6689),l=a(6317),o=a(1163),n=a(1530),d=e([l]);l=(d.then?(await d)():d)[0];let m=(0,r.createContext)({hasAccess:()=>!1,checkAccess:()=>!1,userRole:"guest",isAdmin:!1,isDriver:!1,isRider:!1}),x={"/":["admin","driver","rider","guest"],"/login":["admin","driver","rider","guest"],"/signup":["admin","driver","rider","guest"],"/forgot-password":["admin","driver","rider","guest"],"/book":["admin","rider"],"/driver":["admin","driver"],"/driver/dashboard":["admin","driver"],"/admin":["admin"],"/admin/dashboard":["admin"],"/admin/users":["admin"],"/admin/bookings":["admin"]};function c({children:e}){let{user:t,loading:a}=(0,l.a)();(0,o.useRouter)();let{showNotification:s}=(0,n.l)(),[d,c]=(0,r.useState)(!1),h=t?"admin"===t.role?"admin":"driver"===t.role?"driver":"rider":"guest",u="admin"===h,p="driver"===h,b="rider"===h;return i.jsx(m.Provider,{value:{hasAccess:e=>{let t=x[e];return t?t.includes(h):"admin"===h},checkAccess:e=>e.includes(h),userRole:h,isAdmin:u,isDriver:p,isRider:b},children:d?e:null})}let h=()=>(0,r.useContext)(m);s()}catch(e){s(e)}})},3462:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{auth:()=>c,db:()=>m});var i=a(3745),r=a(401),l=a(1492),o=a(3392),n=e([i,r,l,o]);[i,r,l,o]=n.then?(await n)():n;let d=(0,i.initializeApp)({apiKey:"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po",authDomain:"baroride.firebaseapp.com",projectId:"baroride",storageBucket:"baroride.firebasestorage.app",messagingSenderId:"191771619835",appId:"1:191771619835:web:2fc57d131cf64a35e2db5e"}),c=(0,r.getAuth)(d),m=(0,l.getFirestore)(d);(0,o.getStorage)(d),s()}catch(e){s(e)}})},3893:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{default:()=>d});var i=a(997);a(108);var r=a(6317),l=a(1530),o=a(2942),n=e([r,o]);[r,o]=n.then?(await n)():n;let d=function({Component:e,pageProps:t}){return i.jsx(r.H,{children:i.jsx(l.J,{children:i.jsx(o.r,{children:i.jsx(e,{...t})})})})};s()}catch(e){s(e)}})},1070:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(997),i=a(6859);function r(){return(0,s.jsxs)(i.Html,{lang:"en",children:[(0,s.jsxs)(i.Head,{children:[s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"}),s.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),s.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),s.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),s.jsx("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),s.jsx("meta",{name:"format-detection",content:"telephone=no"}),s.jsx("meta",{name:"theme-color",content:"#1e3a5f"}),s.jsx("meta",{name:"msapplication-navbutton-color",content:"#1e3a5f"}),s.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,s.jsxs)("body",{className:"antialiased",children:[s.jsx(i.Main,{}),s.jsx(i.NextScript,{})]})]})}},9335:(e,t,a)=>{"use strict";a.d(t,{dz:()=>s});let s=()=>({isMobile:!1,isTablet:!1,isDesktop:!0,isIOS:!1,isAndroid:!1,isSafari:!1,isChrome:!1,screenWidth:1920,screenHeight:1080,pixelRatio:1,touchSupport:!1,orientation:"landscape"})},108:()=>{}};