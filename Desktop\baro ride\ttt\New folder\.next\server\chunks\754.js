exports.id=754,exports.ids=[754],exports.modules={9532:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{Z:()=>d});var a=s(997),l=s(968),i=s.n(l),n=s(7792),o=e([n]);function d({children:e,title:t="BaroRide"}){return(0,a.jsxs)(a.<PERSON>,{children:[(0,a.jsxs)(i(),{children:[a.jsx("title",{children:t}),a.jsx("meta",{name:"description",content:"Book your ride with BaroRide - fixed fares and reliable service"}),a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"}),a.jsx("meta",{name:"theme-color",content:"#2563eb"}),a.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),a.jsx("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),a.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),a.jsx("link",{rel:"icon",href:"/logo-icon.svg"}),a.jsx("link",{rel:"apple-touch-icon",href:"/logo-icon.svg"}),a.jsx("link",{rel:"shortcut icon",href:"/logo-icon.svg"})]}),(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-white text-gray-900 overflow-x-hidden",children:[a.jsx(n.Z,{}),a.jsx("main",{className:"flex-grow w-full",children:e}),a.jsx("footer",{className:"bg-gray-100 border-t border-gray-200 py-4 mt-auto",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center text-gray-600 text-sm",children:["\xa9 ",new Date().getFullYear()," BaroRide. All rights reserved."]})})]})]})}n=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},7792:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{Z:()=>h});var a=s(997),l=s(6689),i=s(1664),n=s.n(i),o=s(1163),d=s(6317),c=s(2794),x=e([d,c]);function h(){let{user:e,signOut:t}=(0,d.a)(),[s,r]=(0,l.useState)(!1),[i,x]=(0,l.useState)(!1),h=(0,o.useRouter)(),u=()=>{r(!s)};return(0,a.jsxs)("nav",{className:"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50",children:[a.jsx("div",{className:"container mx-auto px-2 sm:px-4",children:(0,a.jsxs)("div",{className:"flex justify-between h-14 sm:h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[i&&a.jsx("button",{onClick:()=>{h.back()},className:"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors","aria-label":"Go back",style:{touchAction:"manipulation"},children:a.jsx("svg",{className:"w-5 h-5 sm:w-7 sm:h-7 text-gray-700",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,a.jsxs)(n(),{href:"/",className:"flex-shrink-0 flex items-center space-x-2",children:[a.jsx("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-8 w-8 sm:h-10 sm:w-10"}),a.jsx("span",{className:"text-blue-600 font-bold text-lg sm:text-xl",children:"BaroRide"})]})]}),a.jsx("div",{className:"hidden md:flex items-center space-x-4",children:e?(0,a.jsxs)(a.Fragment,{children:["rider"===e.role?a.jsx(n(),{href:"/book",className:"text-gray-700 hover:text-blue-600",children:"Book a Ride"}):a.jsx(n(),{href:"/driver/dashboard",className:"text-gray-700 hover:text-blue-600",children:"Dashboard"}),a.jsx(c.Z,{}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:u,className:"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none",children:[a.jsx("span",{className:"mr-1",children:e.fullName}),a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),s&&a.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:a.jsx("button",{onClick:t,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign Out"})})]})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(n(),{href:"/login",className:"text-gray-700 hover:text-blue-600",children:"Login"}),a.jsx(n(),{href:"/signup",className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Sign Up"})]})}),(0,a.jsxs)("div",{className:"md:hidden flex items-center space-x-1",children:[e&&a.jsx(c.Z,{}),a.jsx("button",{onClick:u,className:"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors",style:{touchAction:"manipulation"},"aria-label":"Toggle menu",children:a.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s?a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"}):a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})})]})]})}),s&&a.jsx("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:a.jsx("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100 mb-2",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e.fullName}),a.jsx("p",{className:"text-xs text-gray-500 capitalize",children:e.role})]}),"rider"===e.role?a.jsx(n(),{href:"/book",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDCF1 Book a Ride"}):"driver"===e.role?a.jsx(n(),{href:"/driver/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDE97 Driver Dashboard"}):a.jsx(n(),{href:"/admin/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"⚙️ Admin Dashboard"}),a.jsx("button",{onClick:()=>{t(),r(!1)},className:"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},children:"\uD83D\uDEAA Sign Out"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(n(),{href:"/login",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"\uD83D\uDD11 Login"}),a.jsx(n(),{href:"/signup",className:"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>r(!1),children:"✨ Sign Up"})]})})})]})}[d,c]=x.then?(await x)():x,r()}catch(e){r(e)}})},2794:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{Z:()=>c});var a=s(997),l=s(6689),i=s(6317),n=s(3462),o=s(1492),d=e([i,n,o]);function c(){let{user:e}=(0,i.a)(),[t,s]=(0,l.useState)([]),[r,d]=(0,l.useState)(!1),[c,x]=(0,l.useState)(0),h=async t=>{if(e)try{await (0,o.updateDoc)((0,o.doc)(n.db,"notifications",t),{read:!0})}catch(e){console.error("Error marking notification as read:",e)}},u=async()=>{if(e)try{let e=t.filter(e=>!e.read).map(e=>(0,o.updateDoc)((0,o.doc)(n.db,"notifications",e.id),{read:!0}));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}},m=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"warning":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-blue-100 text-blue-800"}};return e?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>{d(!r)},className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[a.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),c>0&&a.jsx("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:c})]}),r&&a.jsx("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20",children:(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("div",{className:"px-4 py-2 bg-gray-100 flex justify-between items-center",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Notifications"}),c>0&&a.jsx("button",{onClick:u,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),a.jsx("div",{className:"max-h-96 overflow-y-auto",children:0===t.length?a.jsx("div",{className:"px-4 py-3 text-sm text-gray-500",children:"No notifications"}):t.map(e=>a.jsx("div",{className:`px-4 py-3 border-b border-gray-100 ${e.read?"":"bg-blue-50"}`,onClick:()=>h(e.id),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsxs)("div",{className:`flex-shrink-0 rounded-full p-1 ${m(e.type)}`,children:["success"===e.type&&a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"warning"===e.type&&a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),"error"===e.type&&a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),"info"===e.type&&a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})]}),(0,a.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[a.jsx("p",{className:"text-sm text-gray-900 whitespace-pre-line",children:e.message}),a.jsx("p",{className:"mt-1 text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()}),e.driverDetails&&a.jsx("div",{className:"mt-2 pt-2 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2",children:a.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-xs font-semibold",children:e.driverDetails.fullName}),(0,a.jsxs)("p",{className:"text-xs",children:[e.driverDetails.vehicleColor," ",e.driverDetails.vehicleMake," ",e.driverDetails.vehicleModel]}),(0,a.jsxs)("p",{className:"text-xs font-medium",children:["License: ",e.driverDetails.licensePlate]})]})]})})]})]})},e.id))})]})})]}):null}[i,n,o]=d.then?(await d)():d,r()}catch(e){r(e)}})},6317:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{H:()=>x,a:()=>u});var a=s(997),l=s(6689),i=s(3462),n=s(401),o=s(1492),d=s(1163),c=e([i,n,o]);[i,n,o]=c.then?(await c)():c;let h=(0,l.createContext)({user:null,loading:!0,signOut:async()=>{}});function x({children:e}){let[t,s]=(0,l.useState)(null),[r,o]=(0,l.useState)(!0);(0,d.useRouter)();let c=async()=>{await (0,n.signOut)(i.I),s(null)};return a.jsx(h.Provider,{value:{user:t,loading:r,signOut:c},children:e})}let u=()=>(0,l.useContext)(h);r()}catch(e){r(e)}})},1530:(e,t,s)=>{"use strict";s.d(t,{J:()=>n,l:()=>o});var r=s(997),a=s(6689);function l({message:e,type:t="info",duration:s=5e3,onClose:l,driverDetails:i}){let[n,o]=(0,a.useState)(!0);return n?r.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-md shadow-lg",children:(0,r.jsxs)("div",{className:`p-4 mb-4 text-sm rounded-lg border ${(()=>{switch(t){case"success":return"bg-green-100 border-green-500 text-green-700";case"warning":return"bg-yellow-100 border-yellow-500 text-yellow-700";case"error":return"bg-red-100 border-red-500 text-red-700";default:return"bg-blue-100 border-blue-500 text-blue-700"}})()}`,role:"alert",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"mr-2",children:(()=>{switch(t){case"success":return r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"warning":return r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"error":return r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}})()}),r.jsx("div",{className:"font-medium flex-grow whitespace-pre-line",children:e}),(0,r.jsxs)("button",{type:"button",className:"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500",onClick:()=>{o(!1),l&&l()},"aria-label":"Close",children:[r.jsx("span",{className:"sr-only",children:"Close"}),r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),i&&r.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3",children:r.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-semibold",children:i.fullName}),(0,r.jsxs)("p",{className:"text-xs",children:[i.vehicleColor," ",i.vehicleMake," ",i.vehicleModel]}),(0,r.jsxs)("p",{className:"text-xs font-medium",children:["License Plate: ",i.licensePlate]}),(0,r.jsxs)("p",{className:"text-xs",children:["Phone: ",i.phoneNumber]})]})]})})]})}):null}let i=(0,a.createContext)({showNotification:()=>{}});function n({children:e}){let[t,s]=(0,a.useState)([]),n=e=>{s(t=>t.filter(t=>t.id!==e))};return(0,r.jsxs)(i.Provider,{value:{showNotification:(e,t="info",r=5e3,a)=>{let l=Date.now().toString();s(s=>[...s,{id:l,message:e,type:t,duration:r,driverDetails:a}])}},children:[e,t.map(e=>r.jsx(l,{message:e.message,type:e.type,duration:e.duration,driverDetails:e.driverDetails,onClose:()=>n(e.id)},e.id))]})}let o=()=>(0,a.useContext)(i)},2942:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{r:()=>c,s:()=>u});var a=s(997),l=s(6689),i=s(6317),n=s(1163),o=s(1530),d=e([i]);i=(d.then?(await d)():d)[0];let x=(0,l.createContext)({hasAccess:()=>!1,checkAccess:()=>!1,userRole:"guest",isAdmin:!1,isDriver:!1,isRider:!1}),h={"/":["admin","driver","rider","guest"],"/login":["admin","driver","rider","guest"],"/signup":["admin","driver","rider","guest"],"/forgot-password":["admin","driver","rider","guest"],"/book":["admin","rider"],"/driver":["admin","driver"],"/driver/dashboard":["admin","driver"],"/admin":["admin"],"/admin/dashboard":["admin"],"/admin/users":["admin"],"/admin/bookings":["admin"]};function c({children:e}){let{user:t,loading:s}=(0,i.a)();(0,n.useRouter)();let{showNotification:r}=(0,o.l)(),[d,c]=(0,l.useState)(!1),u=t?"admin"===t.role?"admin":"driver"===t.role?"driver":"rider":"guest",m="admin"===u,g="driver"===u,v="rider"===u;return a.jsx(x.Provider,{value:{hasAccess:e=>{let t=h[e];return t?t.includes(u):"admin"===u},checkAccess:e=>e.includes(u),userRole:u,isAdmin:m,isDriver:g,isRider:v},children:d?e:null})}let u=()=>(0,l.useContext)(x);r()}catch(e){r(e)}})},3462:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{I:()=>c,db:()=>x});var a=s(3745),l=s(401),i=s(1492),n=s(3392),o=e([a,l,i,n]);[a,l,i,n]=o.then?(await o)():o;let d=(0,a.initializeApp)({apiKey:"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po",authDomain:"baroride.firebaseapp.com",projectId:"baroride",storageBucket:"baroride.firebasestorage.app",messagingSenderId:"191771619835",appId:"1:191771619835:web:2fc57d131cf64a35e2db5e"}),c=(0,l.getAuth)(d),x=(0,i.getFirestore)(d);(0,n.getStorage)(d),r()}catch(e){r(e)}})},3893:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>d});var a=s(997);s(108);var l=s(6317),i=s(1530),n=s(2942),o=e([l,n]);[l,n]=o.then?(await o)():o;let d=function({Component:e,pageProps:t}){return a.jsx(l.H,{children:a.jsx(i.J,{children:a.jsx(n.r,{children:a.jsx(e,{...t})})})})};r()}catch(e){r(e)}})},1070:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(997),a=s(6859);function l(){return(0,r.jsxs)(a.Html,{lang:"en",children:[r.jsx(a.Head,{}),(0,r.jsxs)("body",{children:[r.jsx(a.Main,{}),r.jsx(a.NextScript,{})]})]})}},108:()=>{}};