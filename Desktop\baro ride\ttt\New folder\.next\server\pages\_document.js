"use strict";(()=>{var e={};e.id=660,e.ids=[660],e.modules={1070:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var n=a(997),s=a(6859);function r(){return(0,n.jsxs)(s.Html,{lang:"en",children:[(0,n.jsxs)(s.Head,{children:[n.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"}),n.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),n.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),n.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),n.jsx("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),n.jsx("meta",{name:"format-detection",content:"telephone=no"}),n.jsx("meta",{name:"theme-color",content:"#1e3a5f"}),n.jsx("meta",{name:"msapplication-navbutton-color",content:"#1e3a5f"}),n.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),n.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),n.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,n.jsxs)("body",{className:"antialiased",children:[n.jsx(s.Main,{}),n.jsx(s.NextScript,{})]})]})}},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},5315:e=>{e.exports=require("path")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[567,859],()=>a(1070));module.exports=n})();