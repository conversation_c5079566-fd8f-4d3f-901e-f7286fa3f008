(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[197],{6141:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/404",function(){return t(651)}])},651:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return l}});var s=t(5893),o=t(7294),r=t(1163),a=t(2151),c=t(1664),i=t.n(c);function l(){let e=(0,r.useRouter)();return(0,o.useEffect)(()=>{console.log("404 - Page not found:",e.asPath)},[e.asPath]),(0,s.jsx)(a.Z,{title:"BaroRide - Page Not Found",children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-blue-500 mb-4",children:"404"}),(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Page Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8",children:"The page you are looking for doesn't exist or has been moved."}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)(i(),{href:"/",className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-md font-medium transition-colors",children:"Go Home"}),(0,s.jsx)("button",{onClick:()=>e.back(),className:"bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-md font-medium transition-colors",children:"Go Back"})]})]})})})}}},function(e){e.O(0,[996,151,888,774,179],function(){return e(e.s=6141)}),_N_E=e.O()}]);