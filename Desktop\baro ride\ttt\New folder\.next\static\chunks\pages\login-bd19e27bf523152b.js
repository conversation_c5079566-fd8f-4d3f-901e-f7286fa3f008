(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[459],{3236:function(e,r,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return s(8656)}])},8656:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return f}});var t=s(5893),a=s(7294),n=s(1163),o=s(9008),l=s.n(o),i=s(1664),c=s.n(i),d=s(6492),u=s(404),m=s(1517),h=s(109);let x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),p=e=>/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(e),g=async e=>{try{let r=(0,h.hJ)(u.db,"users"),s=(0,h.IO)(r,(0,h.ar)("phoneNumber","==",e)),t=await (0,h.PL)(s);if(t.empty)return null;let a=t.docs[0];return{id:a.id,...a.data()}}catch(e){return console.error("Error finding user by phone number:",e),null}},w=async(e,r)=>{try{let s,t;if(x(e)){s=await (0,m.e5)(u.I,e,r);let a=await (0,h.QT)((0,h.JU)(u.db,"users",s.user.uid));t={id:a.id,...a.data()}}else if(p(e)){let a=await g(e);if(!a)throw Error("User not found with this phone number");s=await (0,m.e5)(u.I,a.email,r),t=a}else throw Error("Invalid identifier format. Please enter a valid email or phone number.");return t}catch(e){throw console.error("Authentication error:",e),e}};function f(){let[e,r]=(0,a.useState)(""),[s,o]=(0,a.useState)(""),[i,u]=(0,a.useState)(!1),[m,h]=(0,a.useState)(""),[x,p]=(0,a.useState)(!1),g=(0,n.useRouter)(),{showNotification:f}=(0,d.l)(),b=async r=>{if(r.preventDefault(),!e){h("Please enter your email or phone number");return}if(!s){h("Please enter your password");return}p(!0),h("");try{let r=await w(e,s);f("Login successful! Redirecting...","success",3e3),"admin"===r.role?g.push("/admin/dashboard"):"driver"===r.role?g.push("/driver/dashboard"):g.push("/")}catch(e){console.error("Login error:",e),e instanceof Error?e.message.includes("user-not-found")||e.message.includes("User not found")?(h("No account found with this email or phone number."),f("No account found with this email or phone number.","error",3e3)):e.message.includes("wrong-password")||e.message.includes("invalid-credential")?(h("Incorrect password. Please try again."),f("Incorrect password. Please try again.","error",3e3)):e.message.includes("Invalid identifier format")?(h("Please enter a valid email or phone number."),f("Please enter a valid email or phone number.","error",3e3)):(h("Failed to login. Please check your credentials."),f("Login failed. Please check your credentials.","error",3e3)):(h("Failed to login. Please check your credentials."),f("Login failed. Please check your credentials.","error",3e3))}finally{p(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:[(0,t.jsxs)(l(),{children:[(0,t.jsx)("title",{children:"BaroRide - Login"}),(0,t.jsx)("meta",{name:"description",content:"Login to your BaroRide account"})]}),(0,t.jsx)("div",{className:"bg-white border rounded-lg shadow-lg p-4 sm:p-6 md:p-8 w-full max-w-md",children:(0,t.jsxs)("form",{onSubmit:b,className:"w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-16 w-16"})}),(0,t.jsx)("h2",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Sign in to your BaroRide account"})]}),m&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 text-sm",children:m}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email or Phone Number"}),(0,t.jsx)("input",{type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"Enter your email or phone number",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",autoComplete:"username",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:i?"text":"password",value:s,onChange:e=>o(e.target.value),placeholder:"Enter your password",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base pr-12",autoComplete:"current-password",required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-manipulation",onClick:()=>u(!i),style:{touchAction:"manipulation"},"aria-label":i?"Hide password":"Show password",children:i?(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,t.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,t.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,t.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,t.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]})]}),(0,t.jsx)("div",{className:"flex justify-end mb-4",children:(0,t.jsx)(c(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-700 transition-colors",children:"Forgot password?"})}),(0,t.jsx)("button",{type:"submit",className:"w-full py-3 px-4 rounded-lg font-medium text-base transition-all duration-200 touch-manipulation ".concat(x?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg"," text-white flex items-center justify-center"),disabled:x,style:{touchAction:"manipulation"},children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),"Sign In"]})}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(c(),{href:"/signup",className:"text-blue-600 hover:text-blue-700 font-medium transition-colors",children:"Create one here"})]})})]})]})})]})}}},function(e){e.O(0,[996,888,774,179],function(){return e(e.s=3236)}),_N_E=e.O()}]);