import DOMPurify from 'isomorphic-dompurify';

// Input sanitization
export const sanitizeInput = (input: string): string => {
  if (!input || typeof input !== 'string') return '';
  return DOMPurify.sanitize(input.trim());
};

// Enhanced email validation
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email) {
    return { isValid: false, error: 'Email is required' };
  }

  const sanitized = sanitizeInput(email);

  // RFC 5322 compliant email regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!emailRegex.test(sanitized)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  if (sanitized.length > 254) {
    return { isValid: false, error: 'Email address is too long' };
  }

  // Check for common malicious patterns
  const maliciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i,
    /data:text\/html/i
  ];

  if (maliciousPatterns.some(pattern => pattern.test(sanitized))) {
    return { isValid: false, error: 'Invalid email format' };
  }

  return { isValid: true };
};

// Enhanced phone number validation
export const validatePhoneNumber = (phone: string): { isValid: boolean; error?: string } => {
  if (!phone) {
    return { isValid: false, error: 'Phone number is required' };
  }

  const sanitized = sanitizeInput(phone);

  // Remove all non-digit characters except +
  const cleaned = sanitized.replace(/[^\d+]/g, '');

  // International phone number validation
  const phoneRegex = /^(\+\d{1,3})?\d{7,15}$/;

  if (!phoneRegex.test(cleaned)) {
    return { isValid: false, error: 'Please enter a valid phone number' };
  }

  // Check for suspicious patterns
  if (cleaned.length < 7 || cleaned.length > 18) {
    return { isValid: false, error: 'Phone number length is invalid' };
  }

  return { isValid: true };
};

// Enhanced password validation
export const validatePassword = (password: string): { isValid: boolean; error?: string; strength?: string } => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }

  if (password.length < 8) {
    return { isValid: false, error: 'Password must be at least 8 characters long' };
  }

  if (password.length > 128) {
    return { isValid: false, error: 'Password is too long' };
  }

  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const criteriaMet = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar].filter(Boolean).length;

  if (criteriaMet < 3) {
    return {
      isValid: false,
      error: 'Password must contain at least 3 of: uppercase, lowercase, numbers, special characters'
    };
  }

  // Check for common weak passwords
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', 'monkey'
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    return { isValid: false, error: 'This password is too common. Please choose a stronger password.' };
  }

  // Determine strength
  let strength = 'weak';
  if (password.length >= 12 && criteriaMet === 4) {
    strength = 'strong';
  } else if (password.length >= 10 && criteriaMet >= 3) {
    strength = 'medium';
  }

  return { isValid: true, strength };
};

// General input validation
export const validateInput = (input: string, type: 'text' | 'name' | 'address', maxLength = 255): { isValid: boolean; error?: string } => {
  if (!input || typeof input !== 'string') {
    return { isValid: false, error: 'Input is required' };
  }

  const sanitized = sanitizeInput(input);

  if (sanitized.length === 0) {
    return { isValid: false, error: 'Input cannot be empty' };
  }

  if (sanitized.length > maxLength) {
    return { isValid: false, error: `Input must be less than ${maxLength} characters` };
  }

  // Type-specific validation
  switch (type) {
    case 'name':
      // Names should only contain letters, spaces, hyphens, and apostrophes
      const nameRegex = /^[a-zA-Z\s\-']+$/;
      if (!nameRegex.test(sanitized)) {
        return { isValid: false, error: 'Name can only contain letters, spaces, hyphens, and apostrophes' };
      }
      break;

    case 'address':
      // Addresses can contain alphanumeric, spaces, and common punctuation
      const addressRegex = /^[a-zA-Z0-9\s\-.,#/]+$/;
      if (!addressRegex.test(sanitized)) {
        return { isValid: false, error: 'Address contains invalid characters' };
      }
      break;

    case 'text':
    default:
      // General text validation - no script tags or dangerous content
      const dangerousPatterns = [
        /<script/i,
        /<\/script>/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i,
        /<object/i,
        /<embed/i,
        /data:text\/html/i
      ];

      if (dangerousPatterns.some(pattern => pattern.test(sanitized))) {
        return { isValid: false, error: 'Input contains invalid content' };
      }
      break;
  }

  return { isValid: true };
};

// Vehicle details validation
export const validateVehicleDetails = (details: {
  make: string;
  model: string;
  color: string;
  licensePlate: string;
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  // Validate make
  const makeValidation = validateInput(details.make, 'text', 50);
  if (!makeValidation.isValid) {
    errors.make = makeValidation.error || 'Invalid vehicle make';
  }

  // Validate model
  const modelValidation = validateInput(details.model, 'text', 50);
  if (!modelValidation.isValid) {
    errors.model = modelValidation.error || 'Invalid vehicle model';
  }

  // Validate color
  const colorValidation = validateInput(details.color, 'text', 30);
  if (!colorValidation.isValid) {
    errors.color = colorValidation.error || 'Invalid vehicle color';
  }

  // Validate license plate
  const licensePlateRegex = /^[A-Z0-9\-\s]{2,15}$/i;
  const sanitizedPlate = sanitizeInput(details.licensePlate);
  if (!licensePlateRegex.test(sanitizedPlate)) {
    errors.licensePlate = 'License plate must be 2-15 characters and contain only letters, numbers, hyphens, and spaces';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Rate limiting validation
export const validateRateLimit = (attempts: number, maxAttempts: number, timeWindow: number, lastAttempt: number): { isAllowed: boolean; waitTime?: number } => {
  const now = Date.now();
  const timeSinceLastAttempt = now - lastAttempt;

  if (attempts >= maxAttempts && timeSinceLastAttempt < timeWindow) {
    const waitTime = timeWindow - timeSinceLastAttempt;
    return { isAllowed: false, waitTime };
  }

  return { isAllowed: true };
};
