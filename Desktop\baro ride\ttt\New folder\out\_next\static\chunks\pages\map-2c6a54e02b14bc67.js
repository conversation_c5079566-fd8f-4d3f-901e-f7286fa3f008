(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[538],{5078:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/map",function(){return t(556)}])},556:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return r}});var i=t(5893),s=t(9008),a=t.n(s);function r(){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a(),{children:[(0,i.jsx)("title",{children:"Map Page"}),(0,i.jsx)("meta",{name:"viewport",content:"initial-scale=1,maximum-scale=1,user-scalable=no"})]}),(0,i.jsxs)("div",{className:"container mx-auto p-4",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Map Page"}),(0,i.jsx)("p",{children:"Map functionality has been removed."}),(0,i.jsx)("p",{children:"Please implement your preferred mapping solution."})]})]})}},9008:function(n,e,t){n.exports=t(3867)}},function(n){n.O(0,[888,774,179],function(){return n(n.s=5078)}),_N_E=n.O()}]);