/*
 * Tailwind CSS Directives
 * These directives are processed by the Tailwind CSS framework
 * and may show as errors in some CSS validators, but they are valid
 * when processed by the Tailwind CSS compiler.
 *
 * To fix IDE validation errors, we've added settings in .vscode/settings.json
 * that disable CSS validation for this project.
 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import mobile-specific styles */
@import './mobile.css';

@layer base {
  /* Base styles */
  body {
    @apply bg-white text-gray-800;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium;
  }
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  /* Card styles */
  .card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm;
  }
}

/* Touch handling improvements */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Make buttons more touch-friendly */
button {
  touch-action: manipulation;
}

/* Improve marker visibility */
.mapboxgl-marker {
  cursor: pointer;
}

/* Improve popup styling */
.mapboxgl-popup-content {
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Improve popup close button */
.mapboxgl-popup-close-button {
  font-size: 20px;
  padding: 5px;
  line-height: 1;
}
