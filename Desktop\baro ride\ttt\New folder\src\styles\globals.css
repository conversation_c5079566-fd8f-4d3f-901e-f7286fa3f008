/*
 * Tailwind CSS Directives
 * These directives are processed by the Tailwind CSS framework
 * and may show as errors in some CSS validators, but they are valid
 * when processed by the Tailwind CSS compiler.
 *
 * To fix IDE validation errors, we've added settings in .vscode/settings.json
 * that disable CSS validation for this project.
 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-specific optimizations */
@layer utilities {
  /* Safe area support for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile input optimizations */
  .mobile-input {
    /* Prevent zoom on iOS when input is focused */
    font-size: 16px !important;
    /* Better touch target */
    min-height: 48px;
    /* Prevent iOS styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* Better mobile keyboard handling */
    -webkit-user-select: text;
    user-select: text;
  }

  /* Mobile button optimizations */
  .mobile-button {
    /* Better touch target */
    min-height: 48px;
    /* Prevent iOS styling */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* Prevent tap highlight */
    -webkit-tap-highlight-color: transparent;
    /* Better touch feedback */
    touch-action: manipulation;
  }

  /* Touch target optimization */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Mobile spinner */
  .mobile-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Mobile-specific base styles */
@layer base {
  /* Prevent horizontal scroll on mobile */
  html, body {
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Better mobile text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Prevent iOS zoom on form inputs */
  input, select, textarea {
    font-size: 16px !important;
  }

  /* Better mobile button styling */
  button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* Import universal device styles */
@import './universal.css';
/* Import mobile-specific styles */
@import './mobile.css';

@layer base {
  /* Base styles */
  body {
    @apply bg-white text-gray-800;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium;
  }
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  /* Card styles */
  .card {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm;
  }
}

/* Touch handling improvements */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Make buttons more touch-friendly */
button {
  touch-action: manipulation;
}

/* Improve marker visibility */
.mapboxgl-marker {
  cursor: pointer;
}

/* Improve popup styling */
.mapboxgl-popup-content {
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Improve popup close button */
.mapboxgl-popup-close-button {
  font-size: 20px;
  padding: 5px;
  line-height: 1;
}
