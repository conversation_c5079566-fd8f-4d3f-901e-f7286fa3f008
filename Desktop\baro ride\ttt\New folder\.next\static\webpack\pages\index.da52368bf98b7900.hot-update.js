"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/mobile-optimization.ts":
/*!******************************************!*\
  !*** ./src/utils/mobile-optimization.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   getDeviceInfo: function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   handleMobileKeyboard: function() { return /* binding */ handleMobileKeyboard; },\n/* harmony export */   handleMobileViewport: function() { return /* binding */ handleMobileViewport; },\n/* harmony export */   initializeMobileOptimizations: function() { return /* binding */ initializeMobileOptimizations; },\n/* harmony export */   needsMobileOptimization: function() { return /* binding */ needsMobileOptimization; },\n/* harmony export */   optimizeFormForMobile: function() { return /* binding */ optimizeFormForMobile; },\n/* harmony export */   optimizeMapForMobile: function() { return /* binding */ optimizeMapForMobile; },\n/* harmony export */   optimizeScrolling: function() { return /* binding */ optimizeScrolling; },\n/* harmony export */   optimizeTouchInteraction: function() { return /* binding */ optimizeTouchInteraction; },\n/* harmony export */   preventIOSZoom: function() { return /* binding */ preventIOSZoom; }\n/* harmony export */ });\n// Mobile optimization utilities for BaroRide\n// Detect device and browser information\nconst getDeviceInfo = ()=>{\n    if (false) {}\n    const userAgent = navigator.userAgent.toLowerCase();\n    const screenWidth = window.screen.width;\n    const screenHeight = window.screen.height;\n    const pixelRatio = window.devicePixelRatio || 1;\n    // Device detection\n    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;\n    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || screenWidth > 768 && screenWidth <= 1024;\n    const isDesktop = !isMobile && !isTablet;\n    // OS detection\n    const isIOS = /iphone|ipad|ipod/i.test(userAgent);\n    const isAndroid = /android/i.test(userAgent);\n    // Browser detection\n    const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent);\n    const isChrome = /chrome/i.test(userAgent);\n    // Touch support\n    const touchSupport = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n    // Orientation\n    const orientation = screenWidth > screenHeight ? \"landscape\" : \"portrait\";\n    return {\n        isMobile,\n        isTablet,\n        isDesktop,\n        isIOS,\n        isAndroid,\n        isSafari,\n        isChrome,\n        screenWidth,\n        screenHeight,\n        pixelRatio,\n        touchSupport,\n        orientation\n    };\n};\n// Optimize touch interactions\nconst optimizeTouchInteraction = (element)=>{\n    if (!element) return;\n    // Prevent iOS zoom on double tap\n    element.style.touchAction = \"manipulation\";\n    // Remove tap highlight\n    element.style.webkitTapHighlightColor = \"transparent\";\n    // Ensure minimum touch target size (44px)\n    const computedStyle = window.getComputedStyle(element);\n    const minSize = 44;\n    if (parseInt(computedStyle.height) < minSize) {\n        element.style.minHeight = \"\".concat(minSize, \"px\");\n    }\n    if (parseInt(computedStyle.width) < minSize) {\n        element.style.minWidth = \"\".concat(minSize, \"px\");\n    }\n};\n// Prevent iOS zoom on input focus\nconst preventIOSZoom = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isIOS) return;\n    // Set font size to 16px to prevent zoom\n    const inputs = document.querySelectorAll(\"input, select, textarea\");\n    inputs.forEach((input)=>{\n        const element = input;\n        if (element.style.fontSize === \"\" || parseInt(element.style.fontSize) < 16) {\n            element.style.fontSize = \"16px\";\n        }\n    });\n};\n// Handle viewport height issues on mobile\nconst handleMobileViewport = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Set CSS custom property for actual viewport height\n    const setVH = ()=>{\n        const vh = window.innerHeight * 0.01;\n        document.documentElement.style.setProperty(\"--vh\", \"\".concat(vh, \"px\"));\n    };\n    setVH();\n    window.addEventListener(\"resize\", setVH);\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(setVH, 100); // Delay to ensure orientation change is complete\n    });\n};\n// Optimize map interactions for mobile\nconst optimizeMapForMobile = (mapContainer)=>{\n    if (!mapContainer || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (deviceInfo.isMobile) {\n        // Prevent page scroll when interacting with map\n        mapContainer.style.touchAction = \"none\";\n        // Add mobile-specific event listeners\n        let isMapInteracting = false;\n        mapContainer.addEventListener(\"touchstart\", ()=>{\n            isMapInteracting = true;\n            document.body.style.overflow = \"hidden\";\n        }, {\n            passive: true\n        });\n        mapContainer.addEventListener(\"touchend\", ()=>{\n            isMapInteracting = false;\n            document.body.style.overflow = \"\";\n        }, {\n            passive: true\n        });\n        // Handle map container sizing\n        const resizeMap = ()=>{\n            const containerHeight = Math.min(window.innerHeight * 0.4, 400);\n            mapContainer.style.height = \"\".concat(containerHeight, \"px\");\n        };\n        resizeMap();\n        window.addEventListener(\"resize\", resizeMap);\n        window.addEventListener(\"orientationchange\", ()=>{\n            setTimeout(resizeMap, 100);\n        });\n    }\n};\n// Optimize form interactions for mobile\nconst optimizeFormForMobile = (form)=>{\n    if (!form || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Optimize all inputs in the form\n    const inputs = form.querySelectorAll(\"input, select, textarea, button\");\n    inputs.forEach((input)=>{\n        const element = input;\n        optimizeTouchInteraction(element);\n        // Add mobile-specific attributes\n        if (element.tagName === \"INPUT\") {\n            const inputElement = element;\n            // Prevent autocorrect and autocapitalize for certain input types\n            if (inputElement.type === \"email\" || inputElement.type === \"url\") {\n                inputElement.setAttribute(\"autocorrect\", \"off\");\n                inputElement.setAttribute(\"autocapitalize\", \"none\");\n                inputElement.setAttribute(\"spellcheck\", \"false\");\n            }\n            // Set appropriate input modes\n            if (inputElement.type === \"tel\") {\n                inputElement.setAttribute(\"inputmode\", \"tel\");\n            } else if (inputElement.type === \"email\") {\n                inputElement.setAttribute(\"inputmode\", \"email\");\n            } else if (inputElement.type === \"number\") {\n                inputElement.setAttribute(\"inputmode\", \"numeric\");\n            }\n        }\n    });\n};\n// Handle keyboard visibility on mobile\nconst handleMobileKeyboard = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    let initialViewportHeight = window.innerHeight;\n    const handleResize = ()=>{\n        const currentHeight = window.innerHeight;\n        const heightDifference = initialViewportHeight - currentHeight;\n        // If height decreased significantly, keyboard is likely open\n        if (heightDifference > 150) {\n            document.body.classList.add(\"keyboard-open\");\n            // Scroll active input into view\n            const activeElement = document.activeElement;\n            if (activeElement && (activeElement.tagName === \"INPUT\" || activeElement.tagName === \"TEXTAREA\")) {\n                setTimeout(()=>{\n                    activeElement.scrollIntoView({\n                        behavior: \"smooth\",\n                        block: \"center\"\n                    });\n                }, 100);\n            }\n        } else {\n            document.body.classList.remove(\"keyboard-open\");\n        }\n    };\n    window.addEventListener(\"resize\", handleResize);\n    // Reset on orientation change\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(()=>{\n            initialViewportHeight = window.innerHeight;\n        }, 500);\n    });\n};\n// Optimize scrolling performance\nconst optimizeScrolling = ()=>{\n    if (false) {}\n    // Enable smooth scrolling\n    document.documentElement.style.scrollBehavior = \"smooth\";\n    // Enable smooth scrolling on iOS\n    document.documentElement.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Add momentum scrolling for iOS\n    document.body.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Optimize scroll containers\n    const scrollContainers = document.querySelectorAll(\".overflow-auto, .overflow-y-auto, .overflow-x-auto\");\n    scrollContainers.forEach((container)=>{\n        const element = container;\n        element.style[\"webkitOverflowScrolling\"] = \"touch\";\n    });\n};\n// Initialize all mobile optimizations\nconst initializeMobileOptimizations = ()=>{\n    if (false) {}\n    // Wait for DOM to be ready\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>{\n            initializeMobileOptimizations();\n        });\n        return;\n    }\n    const deviceInfo = getDeviceInfo();\n    // Add device classes to body (only in browser environment)\n    if (typeof document !== \"undefined\" && document.body) {\n        document.body.classList.add(deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\", deviceInfo.isTablet ? \"is-tablet\" : \"\", deviceInfo.isIOS ? \"is-ios\" : \"\", deviceInfo.isAndroid ? \"is-android\" : \"\", deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\");\n    }\n    // Apply optimizations\n    handleMobileViewport();\n    preventIOSZoom();\n    handleMobileKeyboard();\n    optimizeScrolling();\n    // Optimize existing forms (only in browser environment)\n    if (typeof document !== \"undefined\") {\n        const forms = document.querySelectorAll(\"form\");\n        forms.forEach(optimizeFormForMobile);\n        // Optimize existing maps\n        const mapContainers = document.querySelectorAll('.map-container, [id*=\"map\"]');\n        mapContainers.forEach((container)=>optimizeMapForMobile(container));\n    }\n    console.log(\"Mobile optimizations initialized for:\", deviceInfo);\n};\n// Utility to check if device needs mobile optimizations\nconst needsMobileOptimization = ()=>{\n    const deviceInfo = getDeviceInfo();\n    return deviceInfo.isMobile || deviceInfo.isTablet;\n};\n// Export device info for use in components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/mobile-optimization.ts\n"));

/***/ })

});