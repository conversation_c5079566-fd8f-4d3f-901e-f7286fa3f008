(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[742],{7627:function(e,s,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/forgot-password",function(){return r(1536)}])},1536:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return x}});var t=r(5893),n=r(7294),a=r(1517),l=r(404),i=r(1163),o=r(9008),d=r.n(o),c=r(1664),u=r.n(c),m=r(6492);function x(){let[e,s]=(0,n.useState)(""),[r,o]=(0,n.useState)(!1),[c,x]=(0,n.useState)(""),[h,b]=(0,n.useState)(!1);(0,i.useRouter)();let{showNotification:f}=(0,m.l)(),g=async s=>{if(s.preventDefault(),!e){x("Please enter your email address");return}o(!0),x("");try{await (0,a.LS)(l.I,e),b(!0),f("Password reset email sent! Check your inbox.","success",5e3)}catch(s){let e=s instanceof Error?s.message:"Failed to send reset email";e.includes("user-not-found")?x("No account found with this email address"):e.includes("invalid-email")?x("Please enter a valid email address"):x("Failed to send reset email. Please try again later."),f("Error: "+(s instanceof Error?s.message:"Failed to send reset email"),"error",5e3)}finally{o(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,t.jsxs)(d(),{children:[(0,t.jsx)("title",{children:"BaroRide - Forgot Password"}),(0,t.jsx)("meta",{name:"description",content:"Reset your BaroRide account password"})]}),(0,t.jsxs)("div",{className:"w-full max-w-md p-6 bg-white rounded-lg shadow-md",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center",children:"Reset Your Password"}),h?(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:[(0,t.jsx)("p",{children:"Password reset email sent!"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Check your email inbox for instructions to reset your password."})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(u(),{href:"/login",className:"text-blue-500 hover:text-blue-700",children:"Return to Login"})})]}):(0,t.jsxs)(t.Fragment,{children:[c&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:c}),(0,t.jsx)("p",{className:"mb-4 text-gray-600",children:"Enter your email address below and we'll send you instructions to reset your password."}),(0,t.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,t.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),placeholder:"Enter your email",className:"w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500",disabled:r})]}),(0,t.jsx)("button",{type:"submit",className:"w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ".concat(r?"opacity-70 cursor-not-allowed":""),disabled:r,children:r?"Sending...":"Send Reset Link"}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsx)(u(),{href:"/login",className:"text-sm text-blue-500 hover:text-blue-700",children:"Back to Login"})})]})]})]})]})}}},function(e){e.O(0,[996,888,774,179],function(){return e(e.s=7627)}),_N_E=e.O()}]);