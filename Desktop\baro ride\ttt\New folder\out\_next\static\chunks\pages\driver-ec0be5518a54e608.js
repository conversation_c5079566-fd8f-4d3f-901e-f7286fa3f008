(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[747],{9457:function(e,n,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver",function(){return r(827)}])},827:function(e,n,r){"use strict";r.r(n),r.d(n,{default:function(){return u}});var t=r(5893),s=r(7294),c=r(1163),i=r(837);function u(){let e=(0,c.useRouter)(),{user:n}=(0,i.a)();return(0,s.useEffect)(()=>{e.push("/driver/dashboard")},[e]),(0,t.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Driver Portal"}),(0,t.jsx)("p",{children:"Redirecting to dashboard..."})]})})}}},function(e){e.O(0,[888,774,179],function(){return e(e.s=9457)}),_N_E=e.O()}]);