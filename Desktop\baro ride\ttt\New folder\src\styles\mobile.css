/* Mobile-first responsive styles for BaroRide */

/* Prevent horizontal scrolling on mobile */
html, body {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* Improve touch targets for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Better button styles for mobile */
.mobile-button {
  padding: 12px 16px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.mobile-button:active {
  transform: scale(0.98);
}

/* Improved form inputs for mobile */
.mobile-input {
  padding: 12px 16px;
  font-size: 16px; /* Prevents zoom on iOS */
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  transition: border-color 0.2s ease;
}

.mobile-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Card styles for mobile */
.mobile-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
}

/* Navigation improvements for mobile */
.mobile-nav {
  position: sticky;
  top: 0;
  z-index: 50;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

/* Map container responsive styles */
.map-container {
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
}

@media (min-width: 640px) {
  .map-container {
    height: 400px;
  }
}

@media (min-width: 1024px) {
  .map-container {
    height: 500px;
  }
}

/* Modal improvements for mobile */
.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: rgba(0, 0, 0, 0.5);
}

.mobile-modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Loading spinner for mobile */
.mobile-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification styles for mobile */
.mobile-notification {
  position: fixed;
  top: 16px;
  left: 16px;
  right: 16px;
  z-index: 60;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Grid improvements for mobile */
.mobile-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .mobile-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Typography improvements for mobile */
.mobile-heading {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 16px;
}

@media (min-width: 640px) {
  .mobile-heading {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .mobile-heading {
    font-size: 2.25rem;
  }
}

/* Table improvements for mobile */
.mobile-table {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-table table {
  min-width: 600px;
}

/* Status badge styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-accepted {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-in-progress {
  background-color: #e0e7ff;
  color: #3730a3;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent text selection on buttons */
button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improve tap targets on iOS */
@media (max-width: 767px) {
  button, 
  input[type="button"], 
  input[type="submit"], 
  input[type="reset"], 
  a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(16px, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(16px, env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(16px, env(safe-area-inset-right));
  }
}
