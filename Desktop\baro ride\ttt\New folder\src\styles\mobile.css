/* Enhanced Mobile-first responsive styles for BaroRide */

/* Prevent horizontal scrolling and improve mobile performance */
html, body {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Use CSS custom properties for dynamic viewport height */
:root {
  --vh: 1vh;
}

/* Mobile viewport height fix */
.mobile-vh {
  height: calc(var(--vh, 1vh) * 100);
  min-height: calc(var(--vh, 1vh) * 100);
}

/* Prevent zoom on iOS */
input, select, textarea {
  font-size: 16px !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Better mobile keyboard handling */
.keyboard-open {
  position: fixed;
  width: 100%;
}

/* Enhanced touch targets */
.touch-target,
button,
a,
input[type="button"],
input[type="submit"],
input[type="reset"] {
  min-height: 44px;
  min-width: 44px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Improve touch targets for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Better button styles for mobile */
.mobile-button {
  padding: 12px 16px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.mobile-button:active {
  transform: scale(0.98);
}

/* Enhanced form inputs for mobile */
.mobile-input,
input,
select,
textarea {
  padding: 14px 16px;
  font-size: 16px !important; /* Prevents zoom on iOS */
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: white;
  width: 100%;
  box-sizing: border-box;
}

.mobile-input:focus,
input:focus,
select:focus,
textarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-specific input improvements */
@media (max-width: 640px) {
  .mobile-input,
  input,
  select,
  textarea {
    padding: 16px;
    font-size: 16px !important;
    min-height: 48px;
    border-radius: 12px;
  }

  /* Better select styling on mobile */
  select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 20px;
    padding-right: 48px;
  }

  /* Textarea specific styling */
  textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* Number input controls */
  input[type="number"] {
    -moz-appearance: textfield;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

/* Card styles for mobile */
.mobile-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
}

/* Navigation improvements for mobile */
.mobile-nav {
  position: sticky;
  top: 0;
  z-index: 50;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

/* Enhanced Map container responsive styles */
.map-container {
  height: 250px;
  border-radius: 8px;
  overflow: hidden;
  touch-action: none; /* Prevent page scroll when interacting with map */
  position: relative;
}

/* Mobile-specific map optimizations */
@media (max-width: 640px) {
  .map-container {
    height: 40vh;
    min-height: 250px;
    max-height: 350px;
    margin: 0 -16px; /* Full width on mobile */
    border-radius: 0;
  }

  /* Larger touch targets for map controls */
  .mapboxgl-ctrl-group button {
    width: 44px !important;
    height: 44px !important;
    font-size: 18px !important;
  }

  /* Better popup styling on mobile */
  .mapboxgl-popup-content {
    padding: 16px !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  }

  .mapboxgl-popup-content button {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    margin: 4px !important;
  }
}

@media (min-width: 640px) {
  .map-container {
    height: 400px;
  }
}

@media (min-width: 1024px) {
  .map-container {
    height: 500px;
  }
}

/* Modal improvements for mobile */
.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: rgba(0, 0, 0, 0.5);
}

.mobile-modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Loading spinner for mobile */
.mobile-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification styles for mobile */
.mobile-notification {
  position: fixed;
  top: 16px;
  left: 16px;
  right: 16px;
  z-index: 60;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Grid improvements for mobile */
.mobile-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .mobile-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Typography improvements for mobile */
.mobile-heading {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 16px;
}

@media (min-width: 640px) {
  .mobile-heading {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .mobile-heading {
    font-size: 2.25rem;
  }
}

/* Table improvements for mobile */
.mobile-table {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-table table {
  min-width: 600px;
}

/* Status badge styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-accepted {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-in-progress {
  background-color: #e0e7ff;
  color: #3730a3;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Prevent text selection on buttons */
button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Improve tap targets on iOS */
@media (max-width: 767px) {
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(16px, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }

  .safe-area-left {
    padding-left: max(16px, env(safe-area-inset-left));
  }

  .safe-area-right {
    padding-right: max(16px, env(safe-area-inset-right));
  }
}
