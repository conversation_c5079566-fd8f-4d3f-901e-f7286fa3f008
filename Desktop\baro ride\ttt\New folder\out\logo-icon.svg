<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="#f5f5f5" stroke="#20c997" stroke-width="10"/>
  
  <!-- B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Shape -->
    <path d="M15 20 L15 95 L20 95 L20 20 Z"/>
    <!-- B Letter Top Section -->
    <path d="M15 20 L50 20 Q65 20 65 30 Q65 40 50 40 L20 40 L20 30 L50 30 Q55 30 55 32 Q55 34 50 34 L20 34 L20 40 L50 40 Q65 40 65 30 Q65 20 50 20 L15 20 Z"/>
    <!-- B Letter Bottom Section -->
    <path d="M15 50 L55 50 Q70 50 70 60 Q70 70 55 70 L20 70 L20 60 L55 60 Q60 60 60 62 Q60 64 55 64 L20 64 L20 70 L55 70 Q70 70 70 60 Q70 50 55 50 L15 50 Z"/>
    <!-- B <PERSON> Middle Connection -->
    <rect x="15" y="40" width="5" height="10"/>
  </g>
  
  <!-- R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Vertical Bar -->
    <rect x="130" y="20" width="20" height="75"/>
    <!-- R Letter Top Section -->
    <path d="M130 20 L165 20 Q180 20 180 30 Q180 40 165 40 L150 40 L150 30 L165 30 Q170 30 170 32 Q170 34 165 34 L150 34 L150 40 L165 40 Q180 40 180 30 Q180 20 165 20 L130 20 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M150 40 L165 40 L185 95 Q187 98 184 98 L178 98 Q175 98 174 95 L156 55 L150 55 Z"/>
  </g>
  
  <!-- Car Icon positioned between B and R -->
  <g transform="translate(70, 60)">
    <!-- Car Main Body -->
    <path d="M10 20 Q10 15 15 15 L45 15 Q50 15 50 20 L50 30 L45 30 L45 35 L40 35 Q37 37 35 35 L25 35 Q23 37 20 35 L15 35 L15 30 L10 30 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M17 15 Q17 10 22 10 L38 10 Q43 10 43 15 L38 15 L22 15 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="20" y="11" width="20" height="4" rx="1" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="14" y="12" width="4" height="3" rx="1" fill="#f5f5f5"/>
    <rect x="42" y="12" width="4" height="3" rx="1" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="12" cy="18" r="1.5" fill="#1e3a5f"/>
    <circle cx="48" cy="18" r="1.5" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="7" cy="25" rx="2.5" ry="2" fill="#20c997"/>
    <ellipse cx="53" cy="25" rx="2.5" ry="2" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="20" cy="35" r="4" fill="#1e3a5f"/>
    <circle cx="40" cy="35" r="4" fill="#1e3a5f"/>
    <circle cx="20" cy="35" r="2" fill="#6c757d"/>
    <circle cx="40" cy="35" r="2" fill="#6c757d"/>
  </g>
  
  <!-- Wave Elements -->
  <g transform="translate(0, 110)">
    <!-- Wave 1 -->
    <path d="M10 0 Q50 -10 100 0 Q150 10 190 0 L190 15 Q150 25 100 15 Q50 5 10 15 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M10 10 Q50 0 100 10 Q150 20 190 10 L190 25 Q150 35 100 25 Q50 15 10 25 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M10 20 Q50 10 100 20 Q150 30 190 20 L190 35 Q150 45 100 35 Q50 25 10 35 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M10 30 Q50 20 100 30 Q150 40 190 30 L190 45 Q150 55 100 45 Q50 35 10 45 Z" fill="#0f6674"/>
  </g>
  
  <!-- BARO RIDE Text -->
  <text x="100" y="175" font-family="Arial, sans-serif" font-size="14" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
</svg>
