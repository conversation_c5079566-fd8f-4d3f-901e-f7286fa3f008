(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{2255:function(e,t,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/book",function(){return o(5930)}])},5695:function(e,t,o){"use strict";o.d(t,{Z:function(){return c}});var n=o(5893),r=o(7294),s=o(1163),a=o(837),l=o(7339),i=o(6492);function c(e){let{children:t,requiredRoles:o,redirectTo:c="/login"}=e,{user:d,loading:u}=(0,a.a)(),{checkAccess:m}=(0,l.s)(),p=(0,s.useRouter)(),{showNotification:x}=(0,i.l)();return((0,r.useEffect)(()=>{if(!u){if(!d){p.push(c),x("Please log in to access this page","warning");return}if(!m(o)){let e="/",t="Access denied. You do not have permission to view this page.";"driver"===d.role?(e="/driver/dashboard",t="Access denied. Redirected to driver dashboard."):"rider"===d.role&&(e="/",t="Access denied. Redirected to home page."),p.push(e),x(t,"warning")}}},[d,u,o,p,c,m]),u)?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):d&&m(o)?(0,n.jsx)(n.Fragment,{children:t}):null}},5930:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return w}});var n=o(5893),r=o(7294),s=o(837),a=o(404),l=o(109);function i(e){let{height:t="300px",selectable:o=!1,onLocationSelected:s,initialLocation:a}=e,l=(0,r.useRef)(null),[i,c]=(0,r.useState)(!1),d=(0,r.useRef)(null),u=e=>{d.current&&e&&0!==e.lat&&0!==e.lng&&(document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([e.lng,e.lat]).addTo(d.current),d.current.flyTo({center:[e.lng,e.lat],zoom:14,essential:!0}),e.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([e.lng,e.lat]).setHTML('<p style="margin: 0;">'.concat(e.address,"</p>")).addTo(d.current))};return(0,r.useEffect)(()=>{let e=()=>{if(!l.current||!window.mapboxgl)return;window.mapboxgl.accessToken="pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A";let e=34.5925,t=8.2483;a&&0!==a.lat&&0!==a.lng&&(e=a.lng,t=a.lat),console.log("Using manual location selection only - automatic geolocation disabled");let n=new window.mapboxgl.Map({container:l.current,style:"mapbox://styles/mapbox/streets-v11",center:[e,t],zoom:13});if(d.current=n,n.addControl(new window.mapboxgl.NavigationControl),a&&0!==a.lat&&0!==a.lng?(new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([a.lng,a.lat]).addTo(n),a.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([a.lng,a.lat]).setHTML('<p style="margin: 0;">'.concat(a.address,"</p>")).addTo(n)):new window.mapboxgl.Marker().setLngLat([e,t]).addTo(n),o&&s){n.getCanvas().style.cursor="crosshair";let e=async e=>{let{lng:t,lat:o}=e.lngLat;console.log("Location selected:",{lng:t,lat:o}),document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),document.querySelectorAll(".mapboxgl-popup").forEach(e=>e.remove());let r=new window.mapboxgl.Marker({color:"#3b82f6",scale:1.2}).setLngLat([t,o]).addTo(n);c(!0);try{let e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(t,",").concat(o,".json?access_token=").concat(window.mapboxgl.accessToken),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Geocoding failed: ".concat(e.status));let a=await e.json();console.log("Geocoding response:",a);let l="Selected location";a.features&&a.features.length>0&&(l=a.features[0].place_name);let i=new window.mapboxgl.Popup({offset:25,closeButton:!0,closeOnClick:!1,maxWidth:"300px"}).setLngLat([t,o]).setHTML('\n                <div style="text-align: center; padding: 8px;">\n                  <p style="margin: 0 0 12px 0; font-weight: 500; color: #374151;">'.concat(l,'</p>\n                  <div style="display: flex; gap: 8px; justify-content: center;">\n                    <button id="select-location" style="\n                      background-color: #3b82f6;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      font-weight: 500;\n                      min-height: 44px;\n                      min-width: 100px;\n                      touch-action: manipulation;\n                    ">\n                      ✓ Select\n                    </button>\n                    <button id="cancel-location" style="\n                      background-color: #6b7280;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      font-weight: 500;\n                      min-height: 44px;\n                      min-width: 80px;\n                      touch-action: manipulation;\n                    ">\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              ')).addTo(n);setTimeout(()=>{let e=document.getElementById("select-location"),n=document.getElementById("cancel-location");e&&(e.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),console.log("Select button clicked, calling onLocationSelected"),s({lat:o,lng:t,address:l}),i.remove()}),e.addEventListener("touchend",e=>{e.preventDefault(),e.stopPropagation(),console.log("Select button touched, calling onLocationSelected"),s({lat:o,lng:t,address:l}),i.remove()})),n&&(n.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),i.remove(),r.remove()}),n.addEventListener("touchend",e=>{e.preventDefault(),e.stopPropagation(),i.remove(),r.remove()}))},100)}catch(r){console.error("Error geocoding location:",r);let e=new window.mapboxgl.Popup({offset:25,closeButton:!0,closeOnClick:!1}).setLngLat([t,o]).setHTML('\n                <div style="text-align: center; padding: 8px;">\n                  <p style="margin: 0 0 12px 0; color: #374151;">Selected location</p>\n                  <p style="margin: 0 0 12px 0; font-size: 12px; color: #6b7280;">Coordinates: '.concat(o.toFixed(6),", ").concat(t.toFixed(6),'</p>\n                  <div style="display: flex; gap: 8px; justify-content: center;">\n                    <button id="select-location-fallback" style="\n                      background-color: #3b82f6;\n                      color: white;\n                      border: none;\n                      padding: 8px 16px;\n                      border-radius: 6px;\n                      cursor: pointer;\n                      font-size: 14px;\n                      min-height: 44px;\n                      touch-action: manipulation;\n                    ">\n                      ✓ Select\n                    </button>\n                  </div>\n                </div>\n              ')).addTo(n);setTimeout(()=>{let n=document.getElementById("select-location-fallback");if(n){let r=n=>{n.preventDefault(),n.stopPropagation(),s({lat:o,lng:t,address:"Selected location"}),e.remove()};n.addEventListener("click",r),n.addEventListener("touchend",r)}},100)}finally{c(!1)}};n.on("click",e),n.on("touchend",e)}else n.getCanvas().style.cursor="";return()=>n.remove()};(()=>{if(window.mapboxgl){e();return}let t=document.createElement("script");t.src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js",t.async=!0,t.onload=()=>{e()},document.head.appendChild(t);let o=document.createElement("link");o.href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css",o.rel="stylesheet",document.head.appendChild(o)})()},[o,s]),(0,r.useEffect)(()=>{if(a&&0!==a.lat&&0!==a.lng){let e=setInterval(()=>{d.current&&(u(a),clearInterval(e))},100);setTimeout(()=>clearInterval(e),5e3)}},[a]),(0,n.jsxs)("div",{style:{position:"relative"},children:[(0,n.jsx)("div",{ref:l,style:{width:"100%",height:t,position:"relative",border:"1px solid #ddd",borderRadius:"4px"}}),i&&(0,n.jsx)("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.2)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:10},children:(0,n.jsx)("div",{style:{backgroundColor:"white",padding:"12px",borderRadius:"50%",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:(0,n.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",opacity:"0.25"}),(0,n.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",strokeDasharray:"60 30",style:{animation:"spin 1s linear infinite"}}),(0,n.jsx)("style",{children:"\n                @keyframes spin {\n                  0% { transform: rotate(0deg); }\n                  100% { transform: rotate(360deg); }\n                }\n              "})]})})}),o&&(0,n.jsx)("div",{style:{position:"absolute",top:"10px",left:"10px",right:"10px",backgroundColor:"#3b82f6",color:"white",padding:"12px 16px",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",zIndex:5,fontSize:"14px",fontWeight:"500",textAlign:"center",border:"2px solid #1e40af"},children:"\uD83D\uDCCD Tap anywhere on the map to select your pickup location"})]})}var c=o(7994),d=o(6492);let u=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,n=0;for(;n<o;)try{let o=(0,l.IO)((0,l.hJ)(a.db,e),...t),n=await (0,l.PL)(o),r=[];return n.forEach(e=>{r.push({id:e.id,...e.data()})}),r}catch(e){if(n++,console.error("Error querying documents (attempt ".concat(n,"/").concat(o,"):"),e),n>=o)throw e;await new Promise(e=>setTimeout(e,1e3))}return[]},m=async function(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,n=0;for(;n<o;)try{return(await (0,l.ET)((0,l.hJ)(a.db,e),t)).id}catch(e){if(n++,console.error("Error adding document (attempt ".concat(n,"/").concat(o,"):"),e),n>=o)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to add document after maximum retries")},p=async function(e,t,o){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2,r=0;for(;r<n;)try{let n=(0,l.doc)(a.db,e,t);await (0,l.r7)(n,o);return}catch(e){if(r++,console.error("Error updating document (attempt ".concat(r,"/").concat(n,"):"),e),r>=n)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to update document after maximum retries")},x=()=>!0,g=()=>{if(!x())return!1;let e=window.location.hostname;return e.includes("firebaseapp.com")||e.includes("web.app")||"baroride.web.app"===e};var h=o(5695);let b=[{code:"GMB",name:"Gambela International Airport"}];function f(){let{user:e}=(0,s.a)(),{showNotification:t}=(0,d.l)(),[o,x]=(0,r.useState)({address:"",lat:0,lng:0}),[h,f]=(0,r.useState)(b[0]),[w,y]=(0,r.useState)(1),[v,k]=(0,r.useState)(!1),[j,N]=(0,r.useState)(0),[L,E]=(0,r.useState)([]),[T,D]=(0,r.useState)(!1),[I,P]=(0,r.useState)(!1),[C,A]=(0,r.useState)(null),[S,M]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&B()},[e]);let B=async()=>{if(e){D(!0);try{g()&&console.log("Fetching previous bookings in Firebase hosting environment");let t=[];if(g()){let o=[(0,l.ar)("riderId","==",e.id),(0,l.ar)("status","==","completed"),(0,l.Xo)("updatedAt","desc"),(0,l.b9)(5)];t=(await u("bookings",o)).map(e=>({id:e.id,...e,scheduledTime:e.scheduledTime?new Date(e.scheduledTime):new Date,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,passengers:e.passengers||1}))}else{let o=(0,l.IO)((0,l.hJ)(a.db,"bookings"),(0,l.ar)("riderId","==",e.id),(0,l.ar)("status","==","completed"),(0,l.Xo)("updatedAt","desc"),(0,l.b9)(5));(await (0,l.PL)(o)).forEach(e=>{let o=e.data(),n={id:e.id,...o,scheduledTime:o.scheduledTime?new Date(o.scheduledTime):new Date,createdAt:o.createdAt?new Date(o.createdAt):new Date,updatedAt:o.updatedAt?new Date(o.updatedAt):new Date,passengers:o.passengers||1};t.push(n)})}E(t),console.log("Fetched ".concat(t.length," previous bookings"))}catch(e){console.error("Error fetching previous bookings:",e),E([])}finally{D(!1)}}},F=()=>{x({address:"",lat:0,lng:0}),A(null)},W=async e=>{if(e&&!(e.trim().length<3))try{let t=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(encodeURIComponent(e),".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A&limit=1"));if(t.ok){let o=await t.json();if(o.features&&o.features.length>0){let t=o.features[0],[n,r]=t.center;x(o=>({...o,lat:r,lng:n,address:t.place_name||e}));let s=O({lat:r,lng:n,address:e});N(s),console.log("Address geocoded successfully:",{lat:r,lng:n,address:t.place_name})}}}catch(e){console.error("Error geocoding address:",e)}};(0,r.useEffect)(()=>{let e=setTimeout(()=>{o.address&&o.address.length>3&&(0===o.lat||0===o.lng)&&W(o.address)},1e3);return()=>clearTimeout(e)},[o.address]);let z=async()=>{if(!navigator.geolocation){t("Geolocation is not supported by your browser. Please enter your location manually.","error");return}M(!0),t("Getting your current location...","info");try{let{latitude:e,longitude:o}=(await new Promise((e,t)=>{navigator.geolocation.getCurrentPosition(t=>{console.log("GPS position obtained:",t.coords),e(t)},e=>{console.error("GPS error:",e.code,e.message);let o="Unable to retrieve your location.";1===e.code?o="Location access denied. Please enable location services in your browser settings.":2===e.code?o="Your current position is unavailable. Please try again later.":3===e.code&&(o="Location request timed out. Please try again."),t(Error(o))},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})).coords;console.log("GPS coordinates: ".concat(e,", ").concat(o));try{let n=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(o,",").concat(e,".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A"));if(!n.ok)throw Error("Geocoding API error: ".concat(n.status));let r=await n.json();console.log("Geocoding response:",r);let s="Your current location";r.features&&r.features.length>0&&(s=r.features[0].place_name,console.log("Address found:",s));let a={lat:e,lng:o,address:s};x(a),A(null);let l=O(a);N(l),t("Your current location has been set as the pickup point.","success")}catch(s){console.error("Error getting address:",s);let n={lat:e,lng:o,address:"Your current location"};x(n),A(null);let r=O(n);N(r),t("Location set, but we couldn't get your exact address. You can edit it manually.","warning")}}catch(e){console.error("Error getting location:",e),t(e instanceof Error?e.message:"Unable to retrieve your location. Please try again or select manually.","error")}finally{M(!1)}},_=e=>{x(e.pickupLocation);let t=b.find(t=>t.code===e.airport.code);t&&f(t),A(e.id),P(!1)},O=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o;return 0===e.lat||0===e.lng?0:35+Math.floor(10+e.lat*e.lng%100/5)+(w-1)*5};(0,r.useEffect)(()=>{0!==o.lat&&0!==o.lng&&N(O(o))},[o,w]);let R=async()=>{if(!e){t("Please log in to book a ride.","error");return}try{let n;t("Creating your booking...","info");let r=new Date,s={riderId:e.id,pickupLocation:{address:o.address,lat:o.lat,lng:o.lng},airport:{name:h.name,code:h.code},status:"pending",fare:j>0?j:O(),passengers:w,createdAt:r,updatedAt:r};if(console.log("Creating booking with data:",s),g())console.log("Using safe function to create booking in production"),n=await m("bookings",s),console.log("Booking created with ID:",n);else try{n=(await (0,l.ET)((0,l.hJ)(a.db,"bookings"),s)).id,console.log("Booking created with ID:",n)}catch(e){console.error("Error creating booking:",e),t("Retrying booking creation...","info"),await new Promise(e=>setTimeout(e,1e3)),n=(await (0,l.ET)((0,l.hJ)(a.db,"bookings"),s)).id,console.log("Booking created on second attempt with ID:",n)}try{if(g()){let t=await (0,l.QT)((0,l.doc)(a.db,"users",e.id));if(t.exists()){let o=t.data().bookingHistory||[];o.includes(n)||(await p("users",e.id,{bookingHistory:[...o,n]}),console.log("Updated user booking history"))}}else{let t=(0,l.doc)(a.db,"users",e.id),o=await (0,l.QT)(t);if(o.exists()){let e=o.data().bookingHistory||[];e.includes(n)||(await (0,l.r7)(t,{bookingHistory:[...e,n]}),console.log("Updated user booking history"))}}}catch(e){console.error("Error updating booking history:",e)}try{let t={userId:e.id,message:"Your ride has been booked successfully. Waiting for a driver to accept.",type:"info",read:!1,relatedBookingId:n,createdAt:r};g()?await m("notifications",t):await (0,l.ET)((0,l.hJ)(a.db,"notifications"),t),console.log("Created notification for user")}catch(e){console.error("Error creating notification:",e)}t("Booking created successfully! Waiting for a driver to accept.","success"),x({address:"",lat:0,lng:0}),y(1),N(0),A(null)}catch(o){console.error("Error creating booking:",o);let e="Failed to create booking. Please try again.";o instanceof Error&&(o.message.includes("network")?e="Network error. Please check your internet connection and try again.":o.message.includes("permission-denied")?e="Permission denied. Please log out and log back in.":o.message.includes("not-found")&&(e="Database connection error. Please refresh the page and try again.")),t(e,"error")}},H=async n=>{if(n.preventDefault(),!e){t("Please log in to book a ride.","error");return}if(!o.address||0===o.lat||0===o.lng){t("Please select a valid pickup location.","error");return}await R()};return(0,n.jsx)(c.Z,{title:"BaroRide - Book a Ride",children:(0,n.jsxs)("div",{className:"container mx-auto p-2 sm:p-4 max-w-4xl",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900",children:"Book Your Ride"}),(0,n.jsx)("p",{className:"text-sm sm:text-base text-gray-600 mt-1",children:"Quick and easy airport transportation"})]}),(0,n.jsxs)("form",{onSubmit:H,className:"space-y-4 sm:space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3",children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2 sm:mb-0",children:"Pickup Location"}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,n.jsx)("button",{type:"button",onClick:()=>{k(!v)},className:"text-xs sm:text-sm px-3 py-1 rounded-full transition-colors touch-manipulation font-medium ".concat(v?"text-white bg-blue-600 hover:bg-blue-700 border border-blue-600":"text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200"),style:{touchAction:"manipulation"},children:v?"\uD83D\uDCDD Manual Entry":"\uD83D\uDDFA️ Select on Map"}),(0,n.jsx)("button",{type:"button",onClick:z,className:"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center",style:{touchAction:"manipulation"},disabled:S,children:S?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Getting..."]}):(0,n.jsx)(n.Fragment,{children:"\uD83D\uDCCD My Location"})}),L.length>0&&(0,n.jsx)("button",{type:"button",onClick:()=>{P(!I)},className:"text-sm text-green-500 hover:text-green-700",children:I?"Hide Previous":"Use Previous"})]})]}),v?(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsxs)("svg",{className:"w-5 h-5 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Map Selection Mode Active"}),(0,n.jsx)("p",{className:"text-xs text-blue-600",children:"Tap anywhere on the map below to select your pickup location"})]})]})}):(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{type:"text",placeholder:"Enter your pickup address manually",className:"w-full p-2 border rounded",value:o.address,onChange:e=>{x(t=>({...t,address:e.target.value})),C&&A(null)}}),o.address&&(0,n.jsx)("button",{type:"button",onClick:F,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,n.jsx)("svg",{className:"w-3 h-3 inline-block mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),'Enter your address manually, click "Use My Location", or select it on the map']})]}),I&&(0,n.jsxs)("div",{className:"mt-2 border rounded shadow-sm overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-50 px-3 py-2 border-b",children:(0,n.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Previous Bookings"})}),(0,n.jsx)("div",{className:"max-h-60 overflow-y-auto",children:T?(0,n.jsxs)("div",{className:"p-4 text-center",children:[(0,n.jsx)("div",{className:"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2"}),(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Loading..."})]}):0===L.length?(0,n.jsx)("div",{className:"p-4 text-center text-sm text-gray-600",children:"No previous bookings found"}):(0,n.jsx)("ul",{className:"divide-y divide-gray-200",children:L.map(e=>(0,n.jsx)("li",{className:"p-3 hover:bg-gray-50 cursor-pointer transition-colors ".concat(C===e.id?"bg-blue-50 border-l-4 border-blue-500":""),onClick:()=>_(e),children:(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e.pickupLocation.address}),(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["To: ",e.airport.name]})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"text-xs text-gray-500 mr-2",children:new Date(e.updatedAt).toLocaleDateString()}),C===e.id&&(0,n.jsx)("span",{className:"text-xs text-blue-500 font-medium",children:"Selected"})]})]})},e.id))})})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Pickup Location Map"}),(0,n.jsx)(i,{height:"300px",selectable:v,onLocationSelected:e=>{if(!e||"number"!=typeof e.lat||"number"!=typeof e.lng){t("Invalid location selected. Please try again.","error");return}if(0===e.lat&&0===e.lng){t("Invalid coordinates selected. Please try again.","error");return}let o={lat:e.lat,lng:e.lng,address:e.address||"Selected location"};console.log("Location selected from map:",o),x(o),A(null),k(!1),N(O(o)),t("Pickup location selected successfully!","success")},initialLocation:0!==o.lat?o:void 0}),0!==o.lat&&0!==o.lng&&(0,n.jsx)("div",{className:"mt-2 p-2 bg-blue-50 border border-blue-100 rounded",children:(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("p",{className:"text-sm font-medium",children:["Selected Pickup: ",o.address]}),C&&(0,n.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:"From Previous Booking"})]}),(0,n.jsx)("button",{type:"button",onClick:F,className:"ml-2 text-xs text-red-500 hover:text-red-700",children:"Clear"})]})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Airport"}),(0,n.jsx)("select",{value:h.code,onChange:e=>{let t=b.find(t=>t.code===e.target.value);t&&f(t)},className:"w-full p-2 border rounded",children:b.map(e=>(0,n.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Passengers"}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("button",{type:"button",onClick:()=>y(e=>Math.max(1,e-1)),className:"p-2 bg-gray-100 border rounded-l hover:bg-gray-200",children:(0,n.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M20 12H4"})})}),(0,n.jsx)("input",{type:"number",min:"1",max:"8",value:w,onChange:e=>y(Math.max(1,Math.min(8,parseInt(e.target.value)||1))),className:"w-full p-2 border-t border-b text-center"}),(0,n.jsx)("button",{type:"button",onClick:()=>y(e=>Math.min(8,e+1)),className:"p-2 bg-gray-100 border rounded-r hover:bg-gray-200",children:(0,n.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v12M6 12h12"})})})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum 8 passengers per ride"})]})]}),j>0&&(0,n.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded p-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-blue-800 mb-2",children:"Fare Estimate"}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-blue-700",children:"Base fare"}),(0,n.jsx)("p",{className:"text-sm text-blue-700",children:"Distance"}),w>1&&(0,n.jsxs)("p",{className:"text-sm text-blue-700",children:["Additional passengers (",w-1,")"]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("p",{className:"text-sm text-blue-700",children:"$35.00"}),(0,n.jsxs)("p",{className:"text-sm text-blue-700",children:["$",Math.floor(10+o.lat*o.lng%100/5).toFixed(2)]}),w>1&&(0,n.jsxs)("p",{className:"text-sm text-blue-700",children:["$",((w-1)*5).toFixed(2)]})]})]}),(0,n.jsxs)("div",{className:"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center",children:[(0,n.jsx)("p",{className:"font-medium text-blue-800",children:"Total estimated fare"}),(0,n.jsxs)("p",{className:"font-medium text-blue-800",children:["$",j.toFixed(2)]})]}),(0,n.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"* Actual fare may vary based on traffic, weather, and other factors."})]}),(0,n.jsxs)("div",{className:"pt-4",children:[(0,n.jsx)("button",{type:"submit",className:"w-full ".concat(o.address&&0!==o.lat&&0!==o.lng?"bg-blue-500 hover:bg-blue-600":"bg-gray-400 cursor-not-allowed"," text-white p-3 rounded font-medium transition-colors"),disabled:!o.address||0===o.lat||0===o.lng,children:e?"Book Ride Now":"Please Log In to Book a Ride"}),(!o.address||0===o.lat||0===o.lng)&&(0,n.jsxs)("div",{className:"flex items-center mt-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-red-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,n.jsx)("p",{className:"text-red-500 text-sm",children:"Please enter or select a pickup location"})]}),!e&&(0,n.jsxs)("div",{className:"flex items-center mt-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-yellow-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,n.jsx)("p",{className:"text-yellow-500 text-sm",children:"You need to be logged in to book a ride"})]})]})]})]})})}function w(){return(0,n.jsx)(h.Z,{requiredRoles:["admin","rider"],children:(0,n.jsx)(f,{})})}}},function(e){e.O(0,[996,994,888,774,179],function(){return e(e.s=2255)}),_N_E=e.O()}]);