export type BookingStatus =
  | 'pending'
  | 'accepted'
  | 'inProgress'
  | 'completed'
  | 'cancelled'
  | 'deleted';

export interface Booking {
  id: string;
  riderId: string;
  driverId?: string;
  pickupLocation: {
    address: string;
    lat: number;
    lng: number;
  };
  airport: {
    name: string;
    code: string;
    terminal?: string;
  };
  status: BookingStatus;
  fare: number;
  passengers: number;
  scheduledTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}
