import { db } from '@/firebase/config';
import { collection, getDocs } from 'firebase/firestore';
import { normalizePhoneNumber } from './auth-helpers';

/**
 * Debug utility to check all users and their phone number formats
 * This helps identify phone number format inconsistencies
 */
export const debugUsers = async () => {
  try {
    console.log('=== DEBUGGING USERS ===');

    const usersRef = collection(db, 'users');
    const querySnapshot = await getDocs(usersRef);

    if (querySnapshot.empty) {
      console.log('No users found in the database');
      return;
    }

    console.log(`Found ${querySnapshot.docs.length} users:`);

    querySnapshot.docs.forEach((doc, index) => {
      const userData = doc.data();
      console.log(`\n--- User ${index + 1} ---`);
      console.log('ID:', doc.id);
      console.log('Email:', userData.email);
      console.log('Phone Number (original):', userData.phoneNumber);
      console.log('Phone Number (normalized):', userData.phoneNumber ? normalizePhoneNumber(userData.phoneNumber) : 'N/A');
      console.log('Full Name:', userData.fullName);
      console.log('Role:', userData.role);
      console.log('Created At:', userData.createdAt?.toDate?.() || userData.createdAt);
    });

    console.log('\n=== END DEBUG ===');
  } catch (error) {
    console.error('Error debugging users:', error);
  }
};

/**
 * Find users with specific phone number patterns
 */
export const findUsersByPhonePattern = async (pattern: string) => {
  try {
    console.log(`=== SEARCHING FOR PHONE PATTERN: ${pattern} ===`);

    const usersRef = collection(db, 'users');
    const querySnapshot = await getDocs(usersRef);

    const matchingUsers = [];

    querySnapshot.docs.forEach((doc) => {
      const userData = doc.data();
      if (userData.phoneNumber && userData.phoneNumber.includes(pattern)) {
        matchingUsers.push({
          id: doc.id,
          ...userData
        });
      }
    });

    console.log(`Found ${matchingUsers.length} users with phone pattern "${pattern}":`, matchingUsers);
    return matchingUsers;
  } catch (error) {
    console.error('Error searching users by phone pattern:', error);
    return [];
  }
};

/**
 * Test phone number normalization with various formats
 */
export const testPhoneNormalization = () => {
  console.log('=== TESTING PHONE NORMALIZATION ===');

  const testNumbers = [
    '1234567890',
    '+1234567890',
    '(*************',
    '************',
    '************',
    '+****************',
    '+251 123 456 789',
    '0123456789',
    '+251-123-456-789'
  ];

  testNumbers.forEach(number => {
    console.log(`Original: "${number}" -> Normalized: "${normalizePhoneNumber(number)}"`);
  });

  console.log('=== END NORMALIZATION TEST ===');
};

/**
 * Create a test user for debugging phone number login
 */
export const createTestUser = async () => {
  try {
    const { createUserWithEmailAndPassword } = await import('firebase/auth');
    const { doc, setDoc } = await import('firebase/firestore');
    const { auth } = await import('@/firebase/config');

    console.log('=== CREATING TEST USER ===');

    const testEmail = '<EMAIL>';
    const testPassword = 'test123456';
    const testPhone = '+************';

    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
    const uid = userCredential.user.uid;

    // Create user document in Firestore
    const userData = {
      id: uid,
      email: testEmail,
      phoneNumber: testPhone,
      fullName: 'Test User',
      role: 'rider',
      createdAt: new Date(),
      updatedAt: new Date(),
      bookingHistory: []
    };

    await setDoc(doc(db, 'users', uid), userData);

    console.log('Test user created successfully:');
    console.log('Email:', testEmail);
    console.log('Password:', testPassword);
    console.log('Phone:', testPhone);
    console.log('Normalized Phone:', normalizePhoneNumber(testPhone));
    console.log('=== END TEST USER CREATION ===');

    return userData;
  } catch (error) {
    console.error('Error creating test user:', error);
    if (error.code === 'auth/email-already-in-use') {
      console.log('Test user already exists. You can use:');
      console.log('Email: <EMAIL>');
      console.log('Password: test123456');
      console.log('Phone: +************');
    }
  }
};
