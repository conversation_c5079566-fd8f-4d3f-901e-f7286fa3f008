"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/MobileOptimizer.tsx":
/*!********************************************!*\
  !*** ./src/components/MobileOptimizer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UniversalDeviceStyles: function() { return /* binding */ UniversalDeviceStyles; },\n/* harmony export */   \"default\": function() { return /* binding */ UniversalDeviceOptimizer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/mobile-optimization */ \"./src/utils/mobile-optimization.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction UniversalDeviceOptimizer(param) {\n    let { children } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if ( false || typeof document === \"undefined\") {\n            return;\n        }\n        // Initialize mobile optimizations when component mounts\n        (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n        // Add device-specific classes to body\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        // Clean up existing device classes\n        document.body.classList.remove(\"is-mobile\", \"is-desktop\", \"is-tablet\", \"is-ios\", \"is-android\", \"has-touch\", \"no-touch\", \"is-safari\", \"is-chrome\", \"is-firefox\", \"is-edge\", \"input-touch\", \"input-mouse\", \"input-hybrid\", \"connection-slow\", \"connection-fast\");\n        // Add comprehensive device classes\n        const classesToAdd = [\n            // Device type\n            deviceInfo.deviceType === \"mobile\" ? \"is-mobile\" : \"\",\n            deviceInfo.deviceType === \"tablet\" ? \"is-tablet\" : \"\",\n            deviceInfo.deviceType === \"desktop\" ? \"is-desktop\" : \"\",\n            // Operating system\n            deviceInfo.isIOS ? \"is-ios\" : \"\",\n            deviceInfo.isAndroid ? \"is-android\" : \"\",\n            // Browser\n            deviceInfo.isSafari ? \"is-safari\" : \"\",\n            deviceInfo.isChrome ? \"is-chrome\" : \"\",\n            deviceInfo.isFirefox ? \"is-firefox\" : \"\",\n            deviceInfo.isEdge ? \"is-edge\" : \"\",\n            // Input method\n            deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\",\n            \"input-\".concat(deviceInfo.inputMethod),\n            // Connection quality\n            [\n                \"slow-2g\",\n                \"2g\",\n                \"3g\"\n            ].includes(deviceInfo.connectionType) ? \"connection-slow\" : \"connection-fast\",\n            // Orientation\n            \"orientation-\".concat(deviceInfo.orientation),\n            // Performance\n            deviceInfo.isLowPowerMode ? \"low-power-mode\" : \"\"\n        ].filter(Boolean); // Remove empty strings\n        document.body.classList.add(...classesToAdd);\n        // Add mobile-specific meta tags if on mobile\n        if (deviceInfo.isMobile) {\n            // Ensure viewport meta tag is properly set\n            let viewportMeta = document.querySelector('meta[name=\"viewport\"]');\n            if (!viewportMeta) {\n                viewportMeta = document.createElement(\"meta\");\n                viewportMeta.setAttribute(\"name\", \"viewport\");\n                document.head.appendChild(viewportMeta);\n            }\n            viewportMeta.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\");\n            // Add mobile web app meta tags\n            const addMetaTag = (name, content)=>{\n                let meta = document.querySelector('meta[name=\"'.concat(name, '\"]'));\n                if (!meta) {\n                    meta = document.createElement(\"meta\");\n                    meta.setAttribute(\"name\", name);\n                    document.head.appendChild(meta);\n                }\n                meta.setAttribute(\"content\", content);\n            };\n            addMetaTag(\"mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-status-bar-style\", \"default\");\n            addMetaTag(\"apple-mobile-web-app-title\", \"BaroRide\");\n            addMetaTag(\"theme-color\", \"#1e3a5f\");\n            addMetaTag(\"format-detection\", \"telephone=no\");\n        }\n        // Handle orientation changes\n        const handleOrientationChange = ()=>{\n            // Re-initialize optimizations after orientation change\n            setTimeout(()=>{\n                (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n            }, 100);\n        };\n        window.addEventListener(\"orientationchange\", handleOrientationChange);\n        window.addEventListener(\"resize\", handleOrientationChange);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", handleOrientationChange);\n            window.removeEventListener(\"resize\", handleOrientationChange);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(UniversalDeviceOptimizer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = UniversalDeviceOptimizer;\n// CSS-in-JS styles for universal device optimizations\nconst UniversalDeviceStyles = ()=>{\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if ( false || typeof document === \"undefined\") {\n            return;\n        }\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        // Apply styles for all devices, not just mobile\n        {\n            // Add mobile-specific styles\n            const style = document.createElement(\"style\");\n            style.textContent = \"\\n        /* Mobile-specific overrides */\\n        .is-mobile input,\\n        .is-mobile select,\\n        .is-mobile textarea {\\n          font-size: 16px !important;\\n          -webkit-appearance: none;\\n          -moz-appearance: none;\\n          appearance: none;\\n        }\\n\\n        .is-mobile button {\\n          min-height: 44px;\\n          touch-action: manipulation;\\n          -webkit-tap-highlight-color: transparent;\\n        }\\n\\n        .is-mobile .map-container {\\n          touch-action: none;\\n        }\\n\\n        /* Prevent zoom on input focus */\\n        .is-mobile input:focus,\\n        .is-mobile select:focus,\\n        .is-mobile textarea:focus {\\n          font-size: 16px !important;\\n        }\\n\\n        /* Better scrolling on mobile */\\n        .is-mobile {\\n          -webkit-overflow-scrolling: touch;\\n        }\\n\\n        /* Hide scrollbars on mobile for cleaner look */\\n        .is-mobile ::-webkit-scrollbar {\\n          width: 0px;\\n          background: transparent;\\n        }\\n\\n        /* Mobile-specific form improvements */\\n        .is-mobile .mobile-form {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-form input,\\n        .is-mobile .mobile-form select,\\n        .is-mobile .mobile-form textarea {\\n          padding: 16px;\\n          border-radius: 12px;\\n          border: 2px solid #e5e7eb;\\n          font-size: 16px !important;\\n        }\\n\\n        .is-mobile .mobile-form button {\\n          padding: 16px;\\n          border-radius: 12px;\\n          font-size: 16px;\\n          font-weight: 600;\\n        }\\n\\n        /* Mobile navigation improvements */\\n        .is-mobile .mobile-nav {\\n          position: sticky;\\n          top: 0;\\n          z-index: 50;\\n          background: white;\\n          border-bottom: 1px solid #e5e7eb;\\n          padding: 12px 16px;\\n        }\\n\\n        /* Mobile card improvements */\\n        .is-mobile .mobile-card {\\n          margin: 8px;\\n          border-radius: 16px;\\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n        }\\n\\n        /* Mobile modal improvements */\\n        .is-mobile .mobile-modal {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-modal-content {\\n          border-radius: 16px;\\n          max-height: 85vh;\\n        }\\n\\n        /* Mobile table improvements */\\n        .is-mobile .mobile-table {\\n          font-size: 14px;\\n        }\\n\\n        .is-mobile .mobile-table th,\\n        .is-mobile .mobile-table td {\\n          padding: 12px 8px;\\n        }\\n\\n        /* Mobile notification improvements */\\n        .is-mobile .mobile-notification {\\n          margin: 16px;\\n          border-radius: 12px;\\n          padding: 16px;\\n          font-size: 16px;\\n        }\\n\\n        /* Keyboard handling */\\n        .is-mobile.keyboard-open {\\n          position: fixed;\\n          width: 100%;\\n        }\\n\\n        /* Safe area handling for devices with notches */\\n        .is-mobile .safe-area-top {\\n          padding-top: max(16px, env(safe-area-inset-top));\\n        }\\n\\n        .is-mobile .safe-area-bottom {\\n          padding-bottom: max(16px, env(safe-area-inset-bottom));\\n        }\\n\\n        .is-mobile .safe-area-left {\\n          padding-left: max(16px, env(safe-area-inset-left));\\n        }\\n\\n        .is-mobile .safe-area-right {\\n          padding-right: max(16px, env(safe-area-inset-right));\\n        }\\n      \";\n            document.head.appendChild(style);\n            return ()=>{\n                document.head.removeChild(style);\n            };\n        }\n    }, []);\n    return null;\n};\n_s1(UniversalDeviceStyles, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = UniversalDeviceStyles;\nvar _c, _c1;\n$RefreshReg$(_c, \"UniversalDeviceOptimizer\");\n$RefreshReg$(_c1, \"UniversalDeviceStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MobileOptimizer.tsx\n"));

/***/ })

});