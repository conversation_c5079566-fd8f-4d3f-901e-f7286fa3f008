{"c": ["webpack"], "r": ["pages/driver/dashboard"], "m": ["./node_modules/mapbox-gl/dist/mapbox-gl.css", "./node_modules/mapbox-gl/dist/mapbox-gl.js", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[12].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[12].use[2]!./node_modules/mapbox-gl/dist/mapbox-gl.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cdriver%5Cdashboard.tsx&page=%2Fdriver%2Fdashboard!", "./src/components/MapboxMap.tsx", "./src/components/ProtectedRoute.tsx", "./src/pages/driver/dashboard.tsx"]}