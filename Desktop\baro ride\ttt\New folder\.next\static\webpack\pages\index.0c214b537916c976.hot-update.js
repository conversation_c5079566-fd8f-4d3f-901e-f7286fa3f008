"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/MobileOptimizer.tsx":
/*!********************************************!*\
  !*** ./src/components/MobileOptimizer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileStyles: function() { return /* binding */ MobileStyles; },\n/* harmony export */   \"default\": function() { return /* binding */ MobileOptimizer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/mobile-optimization */ \"./src/utils/mobile-optimization.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction MobileOptimizer(param) {\n    let { children } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if ( false || typeof document === \"undefined\") {\n            return;\n        }\n        // Initialize mobile optimizations when component mounts\n        (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n        // Add device-specific classes to body\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        // Clean up existing device classes\n        document.body.classList.remove(\"is-mobile\", \"is-desktop\", \"is-tablet\", \"is-ios\", \"is-android\", \"has-touch\", \"no-touch\");\n        // Add current device classes\n        document.body.classList.add(deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\", deviceInfo.isTablet ? \"is-tablet\" : \"\", deviceInfo.isIOS ? \"is-ios\" : \"\", deviceInfo.isAndroid ? \"is-android\" : \"\", deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\");\n        // Add mobile-specific meta tags if on mobile\n        if (deviceInfo.isMobile) {\n            // Ensure viewport meta tag is properly set\n            let viewportMeta = document.querySelector('meta[name=\"viewport\"]');\n            if (!viewportMeta) {\n                viewportMeta = document.createElement(\"meta\");\n                viewportMeta.setAttribute(\"name\", \"viewport\");\n                document.head.appendChild(viewportMeta);\n            }\n            viewportMeta.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\");\n            // Add mobile web app meta tags\n            const addMetaTag = (name, content)=>{\n                let meta = document.querySelector('meta[name=\"'.concat(name, '\"]'));\n                if (!meta) {\n                    meta = document.createElement(\"meta\");\n                    meta.setAttribute(\"name\", name);\n                    document.head.appendChild(meta);\n                }\n                meta.setAttribute(\"content\", content);\n            };\n            addMetaTag(\"mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-status-bar-style\", \"default\");\n            addMetaTag(\"apple-mobile-web-app-title\", \"BaroRide\");\n            addMetaTag(\"theme-color\", \"#1e3a5f\");\n            addMetaTag(\"format-detection\", \"telephone=no\");\n        }\n        // Handle orientation changes\n        const handleOrientationChange = ()=>{\n            // Re-initialize optimizations after orientation change\n            setTimeout(()=>{\n                (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n            }, 100);\n        };\n        window.addEventListener(\"orientationchange\", handleOrientationChange);\n        window.addEventListener(\"resize\", handleOrientationChange);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", handleOrientationChange);\n            window.removeEventListener(\"resize\", handleOrientationChange);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(MobileOptimizer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = MobileOptimizer;\n// CSS-in-JS styles for mobile optimizations\nconst MobileStyles = ()=>{\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        if (deviceInfo.isMobile) {\n            // Add mobile-specific styles\n            const style = document.createElement(\"style\");\n            style.textContent = \"\\n        /* Mobile-specific overrides */\\n        .is-mobile input,\\n        .is-mobile select,\\n        .is-mobile textarea {\\n          font-size: 16px !important;\\n          -webkit-appearance: none;\\n          -moz-appearance: none;\\n          appearance: none;\\n        }\\n\\n        .is-mobile button {\\n          min-height: 44px;\\n          touch-action: manipulation;\\n          -webkit-tap-highlight-color: transparent;\\n        }\\n\\n        .is-mobile .map-container {\\n          touch-action: none;\\n        }\\n\\n        /* Prevent zoom on input focus */\\n        .is-mobile input:focus,\\n        .is-mobile select:focus,\\n        .is-mobile textarea:focus {\\n          font-size: 16px !important;\\n        }\\n\\n        /* Better scrolling on mobile */\\n        .is-mobile {\\n          -webkit-overflow-scrolling: touch;\\n        }\\n\\n        /* Hide scrollbars on mobile for cleaner look */\\n        .is-mobile ::-webkit-scrollbar {\\n          width: 0px;\\n          background: transparent;\\n        }\\n\\n        /* Mobile-specific form improvements */\\n        .is-mobile .mobile-form {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-form input,\\n        .is-mobile .mobile-form select,\\n        .is-mobile .mobile-form textarea {\\n          padding: 16px;\\n          border-radius: 12px;\\n          border: 2px solid #e5e7eb;\\n          font-size: 16px !important;\\n        }\\n\\n        .is-mobile .mobile-form button {\\n          padding: 16px;\\n          border-radius: 12px;\\n          font-size: 16px;\\n          font-weight: 600;\\n        }\\n\\n        /* Mobile navigation improvements */\\n        .is-mobile .mobile-nav {\\n          position: sticky;\\n          top: 0;\\n          z-index: 50;\\n          background: white;\\n          border-bottom: 1px solid #e5e7eb;\\n          padding: 12px 16px;\\n        }\\n\\n        /* Mobile card improvements */\\n        .is-mobile .mobile-card {\\n          margin: 8px;\\n          border-radius: 16px;\\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n        }\\n\\n        /* Mobile modal improvements */\\n        .is-mobile .mobile-modal {\\n          padding: 16px;\\n        }\\n\\n        .is-mobile .mobile-modal-content {\\n          border-radius: 16px;\\n          max-height: 85vh;\\n        }\\n\\n        /* Mobile table improvements */\\n        .is-mobile .mobile-table {\\n          font-size: 14px;\\n        }\\n\\n        .is-mobile .mobile-table th,\\n        .is-mobile .mobile-table td {\\n          padding: 12px 8px;\\n        }\\n\\n        /* Mobile notification improvements */\\n        .is-mobile .mobile-notification {\\n          margin: 16px;\\n          border-radius: 12px;\\n          padding: 16px;\\n          font-size: 16px;\\n        }\\n\\n        /* Keyboard handling */\\n        .is-mobile.keyboard-open {\\n          position: fixed;\\n          width: 100%;\\n        }\\n\\n        /* Safe area handling for devices with notches */\\n        .is-mobile .safe-area-top {\\n          padding-top: max(16px, env(safe-area-inset-top));\\n        }\\n\\n        .is-mobile .safe-area-bottom {\\n          padding-bottom: max(16px, env(safe-area-inset-bottom));\\n        }\\n\\n        .is-mobile .safe-area-left {\\n          padding-left: max(16px, env(safe-area-inset-left));\\n        }\\n\\n        .is-mobile .safe-area-right {\\n          padding-right: max(16px, env(safe-area-inset-right));\\n        }\\n      \";\n            document.head.appendChild(style);\n            return ()=>{\n                document.head.removeChild(style);\n            };\n        }\n    }, []);\n    return null;\n};\n_s1(MobileStyles, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = MobileStyles;\nvar _c, _c1;\n$RefreshReg$(_c, \"MobileOptimizer\");\n$RefreshReg$(_c1, \"MobileStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MobileOptimizer.tsx\n"));

/***/ })

});