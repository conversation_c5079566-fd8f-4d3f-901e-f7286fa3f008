"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/book",{

/***/ "./src/utils/mobile-optimization.ts":
/*!******************************************!*\
  !*** ./src/utils/mobile-optimization.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   getDeviceInfo: function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   handleMobileKeyboard: function() { return /* binding */ handleMobileKeyboard; },\n/* harmony export */   handleMobileViewport: function() { return /* binding */ handleMobileViewport; },\n/* harmony export */   initializeMobileOptimizations: function() { return /* binding */ initializeMobileOptimizations; },\n/* harmony export */   needsMobileOptimization: function() { return /* binding */ needsMobileOptimization; },\n/* harmony export */   optimizeFormForMobile: function() { return /* binding */ optimizeFormForMobile; },\n/* harmony export */   optimizeMapForMobile: function() { return /* binding */ optimizeMapForMobile; },\n/* harmony export */   optimizeScrolling: function() { return /* binding */ optimizeScrolling; },\n/* harmony export */   optimizeTouchInteraction: function() { return /* binding */ optimizeTouchInteraction; },\n/* harmony export */   preventIOSZoom: function() { return /* binding */ preventIOSZoom; }\n/* harmony export */ });\n// Universal device optimization utilities for BaroRide\n// Works consistently across mobile, tablet, and desktop devices\n// Detect device and browser information\nconst getDeviceInfo = ()=>{\n    var _navigator_connection;\n    if (false) {}\n    const userAgent = navigator.userAgent.toLowerCase();\n    const screenWidth = window.screen.width;\n    const screenHeight = window.screen.height;\n    const pixelRatio = window.devicePixelRatio || 1;\n    // Device detection\n    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;\n    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || screenWidth > 768 && screenWidth <= 1024;\n    const isDesktop = !isMobile && !isTablet;\n    // OS detection\n    const isIOS = /iphone|ipad|ipod/i.test(userAgent);\n    const isAndroid = /android/i.test(userAgent);\n    // Browser detection\n    const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent);\n    const isChrome = /chrome/i.test(userAgent);\n    const isFirefox = /firefox/i.test(userAgent);\n    const isEdge = /edge|edg/i.test(userAgent);\n    // Touch support\n    const touchSupport = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n    // Orientation\n    const orientation = screenWidth > screenHeight ? \"landscape\" : \"portrait\";\n    // Device type\n    const deviceType = isMobile ? \"mobile\" : isTablet ? \"tablet\" : \"desktop\";\n    // Input method detection\n    const inputMethod = touchSupport ? isMobile ? \"touch\" : \"hybrid\" : \"mouse\";\n    // Connection type\n    const connectionType = ((_navigator_connection = navigator.connection) === null || _navigator_connection === void 0 ? void 0 : _navigator_connection.effectiveType) || \"unknown\";\n    // Low power mode detection (iOS specific)\n    const isLowPowerMode = isIOS && navigator.hardwareConcurrency <= 2;\n    return {\n        isMobile,\n        isTablet,\n        isDesktop,\n        isIOS,\n        isAndroid,\n        isSafari,\n        isChrome,\n        isFirefox,\n        isEdge,\n        screenWidth,\n        screenHeight,\n        pixelRatio,\n        touchSupport,\n        orientation,\n        deviceType,\n        inputMethod,\n        connectionType,\n        isLowPowerMode\n    };\n};\n// Optimize touch interactions\nconst optimizeTouchInteraction = (element)=>{\n    if (!element) return;\n    // Prevent iOS zoom on double tap\n    element.style.touchAction = \"manipulation\";\n    // Remove tap highlight\n    element.style.webkitTapHighlightColor = \"transparent\";\n    // Ensure minimum touch target size (44px)\n    const computedStyle = window.getComputedStyle(element);\n    const minSize = 44;\n    if (parseInt(computedStyle.height) < minSize) {\n        element.style.minHeight = \"\".concat(minSize, \"px\");\n    }\n    if (parseInt(computedStyle.width) < minSize) {\n        element.style.minWidth = \"\".concat(minSize, \"px\");\n    }\n};\n// Prevent iOS zoom on input focus\nconst preventIOSZoom = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isIOS) return;\n    // Set font size to 16px to prevent zoom\n    const inputs = document.querySelectorAll(\"input, select, textarea\");\n    inputs.forEach((input)=>{\n        const element = input;\n        if (element.style.fontSize === \"\" || parseInt(element.style.fontSize) < 16) {\n            element.style.fontSize = \"16px\";\n        }\n    });\n};\n// Handle viewport height issues on mobile\nconst handleMobileViewport = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Set CSS custom property for actual viewport height\n    const setVH = ()=>{\n        const vh = window.innerHeight * 0.01;\n        document.documentElement.style.setProperty(\"--vh\", \"\".concat(vh, \"px\"));\n    };\n    setVH();\n    window.addEventListener(\"resize\", setVH);\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(setVH, 100); // Delay to ensure orientation change is complete\n    });\n};\n// Optimize map interactions for mobile\nconst optimizeMapForMobile = (mapContainer)=>{\n    if (!mapContainer || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (deviceInfo.isMobile) {\n        // Prevent page scroll when interacting with map\n        mapContainer.style.touchAction = \"none\";\n        // Add mobile-specific event listeners\n        mapContainer.addEventListener(\"touchstart\", ()=>{\n            document.body.style.overflow = \"hidden\";\n        }, {\n            passive: true\n        });\n        mapContainer.addEventListener(\"touchend\", ()=>{\n            document.body.style.overflow = \"\";\n        }, {\n            passive: true\n        });\n        // Handle map container sizing\n        const resizeMap = ()=>{\n            const containerHeight = Math.min(window.innerHeight * 0.4, 400);\n            mapContainer.style.height = \"\".concat(containerHeight, \"px\");\n        };\n        resizeMap();\n        window.addEventListener(\"resize\", resizeMap);\n        window.addEventListener(\"orientationchange\", ()=>{\n            setTimeout(resizeMap, 100);\n        });\n    }\n};\n// Optimize form interactions for mobile\nconst optimizeFormForMobile = (form)=>{\n    if (!form || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Optimize all inputs in the form\n    const inputs = form.querySelectorAll(\"input, select, textarea, button\");\n    inputs.forEach((input)=>{\n        const element = input;\n        optimizeTouchInteraction(element);\n        // Add mobile-specific attributes\n        if (element.tagName === \"INPUT\") {\n            const inputElement = element;\n            // Prevent autocorrect and autocapitalize for certain input types\n            if (inputElement.type === \"email\" || inputElement.type === \"url\") {\n                inputElement.setAttribute(\"autocorrect\", \"off\");\n                inputElement.setAttribute(\"autocapitalize\", \"none\");\n                inputElement.setAttribute(\"spellcheck\", \"false\");\n            }\n            // Set appropriate input modes\n            if (inputElement.type === \"tel\") {\n                inputElement.setAttribute(\"inputmode\", \"tel\");\n            } else if (inputElement.type === \"email\") {\n                inputElement.setAttribute(\"inputmode\", \"email\");\n            } else if (inputElement.type === \"number\") {\n                inputElement.setAttribute(\"inputmode\", \"numeric\");\n            }\n        }\n    });\n};\n// Handle keyboard visibility on mobile\nconst handleMobileKeyboard = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    let initialViewportHeight = window.innerHeight;\n    const handleResize = ()=>{\n        const currentHeight = window.innerHeight;\n        const heightDifference = initialViewportHeight - currentHeight;\n        // If height decreased significantly, keyboard is likely open\n        if (heightDifference > 150) {\n            document.body.classList.add(\"keyboard-open\");\n            // Scroll active input into view\n            const activeElement = document.activeElement;\n            if (activeElement && (activeElement.tagName === \"INPUT\" || activeElement.tagName === \"TEXTAREA\")) {\n                setTimeout(()=>{\n                    activeElement.scrollIntoView({\n                        behavior: \"smooth\",\n                        block: \"center\"\n                    });\n                }, 100);\n            }\n        } else {\n            document.body.classList.remove(\"keyboard-open\");\n        }\n    };\n    window.addEventListener(\"resize\", handleResize);\n    // Reset on orientation change\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(()=>{\n            initialViewportHeight = window.innerHeight;\n        }, 500);\n    });\n};\n// Optimize scrolling performance\nconst optimizeScrolling = ()=>{\n    if (false) {}\n    // Enable smooth scrolling\n    document.documentElement.style.scrollBehavior = \"smooth\";\n    // Enable smooth scrolling on iOS\n    document.documentElement.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Add momentum scrolling for iOS\n    document.body.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Optimize scroll containers\n    const scrollContainers = document.querySelectorAll(\".overflow-auto, .overflow-y-auto, .overflow-x-auto\");\n    scrollContainers.forEach((container)=>{\n        const element = container;\n        element.style[\"webkitOverflowScrolling\"] = \"touch\";\n    });\n};\n// Initialize all mobile optimizations\nconst initializeMobileOptimizations = ()=>{\n    if (false) {}\n    // Wait for DOM to be ready\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>{\n            initializeMobileOptimizations();\n        });\n        return;\n    }\n    const deviceInfo = getDeviceInfo();\n    // Add device classes to body (only in browser environment)\n    if (typeof document !== \"undefined\" && document.body) {\n        const classesToAdd = [\n            deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\",\n            deviceInfo.isTablet ? \"is-tablet\" : \"\",\n            deviceInfo.isIOS ? \"is-ios\" : \"\",\n            deviceInfo.isAndroid ? \"is-android\" : \"\",\n            deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\"\n        ].filter(Boolean); // Remove empty strings\n        document.body.classList.add(...classesToAdd);\n    }\n    // Apply optimizations\n    handleMobileViewport();\n    preventIOSZoom();\n    handleMobileKeyboard();\n    optimizeScrolling();\n    // Optimize existing forms (only in browser environment)\n    if (typeof document !== \"undefined\") {\n        const forms = document.querySelectorAll(\"form\");\n        forms.forEach(optimizeFormForMobile);\n        // Optimize existing maps\n        const mapContainers = document.querySelectorAll('.map-container, [id*=\"map\"]');\n        mapContainers.forEach((container)=>optimizeMapForMobile(container));\n    }\n    console.log(\"Mobile optimizations initialized for:\", deviceInfo);\n};\n// Utility to check if device needs mobile optimizations\nconst needsMobileOptimization = ()=>{\n    const deviceInfo = getDeviceInfo();\n    return deviceInfo.isMobile || deviceInfo.isTablet;\n};\n// Export device info for use in components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/mobile-optimization.ts\n"));

/***/ })

});