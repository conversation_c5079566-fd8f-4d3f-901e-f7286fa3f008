import { useEffect } from 'react';
import { initializeMobileOptimizations, getDeviceInfo } from '@/utils/mobile-optimization';

interface MobileOptimizerProps {
  children: React.ReactNode;
}

export default function MobileOptimizer({ children }: MobileOptimizerProps) {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    // Initialize mobile optimizations when component mounts
    initializeMobileOptimizations();

    // Add device-specific classes to body
    const deviceInfo = getDeviceInfo();

    // Clean up existing device classes
    document.body.classList.remove(
      'is-mobile', 'is-desktop', 'is-tablet', 'is-ios', 'is-android', 'has-touch', 'no-touch'
    );

    // Add current device classes
    document.body.classList.add(
      deviceInfo.isMobile ? 'is-mobile' : 'is-desktop',
      deviceInfo.isTablet ? 'is-tablet' : '',
      deviceInfo.isIOS ? 'is-ios' : '',
      deviceInfo.isAndroid ? 'is-android' : '',
      deviceInfo.touchSupport ? 'has-touch' : 'no-touch'
    );

    // Add mobile-specific meta tags if on mobile
    if (deviceInfo.isMobile) {
      // Ensure viewport meta tag is properly set
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.setAttribute('name', 'viewport');
        document.head.appendChild(viewportMeta);
      }
      viewportMeta.setAttribute(
        'content',
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );

      // Add mobile web app meta tags
      const addMetaTag = (name: string, content: string) => {
        let meta = document.querySelector(`meta[name="${name}"]`);
        if (!meta) {
          meta = document.createElement('meta');
          meta.setAttribute('name', name);
          document.head.appendChild(meta);
        }
        meta.setAttribute('content', content);
      };

      addMetaTag('mobile-web-app-capable', 'yes');
      addMetaTag('apple-mobile-web-app-capable', 'yes');
      addMetaTag('apple-mobile-web-app-status-bar-style', 'default');
      addMetaTag('apple-mobile-web-app-title', 'BaroRide');
      addMetaTag('theme-color', '#1e3a5f');
      addMetaTag('format-detection', 'telephone=no');
    }

    // Handle orientation changes
    const handleOrientationChange = () => {
      // Re-initialize optimizations after orientation change
      setTimeout(() => {
        initializeMobileOptimizations();
      }, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    // Cleanup
    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, []);

  return <>{children}</>;
}

// CSS-in-JS styles for mobile optimizations
export const MobileStyles = () => {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return;
    }

    const deviceInfo = getDeviceInfo();

    if (deviceInfo.isMobile) {
      // Add mobile-specific styles
      const style = document.createElement('style');
      style.textContent = `
        /* Mobile-specific overrides */
        .is-mobile input,
        .is-mobile select,
        .is-mobile textarea {
          font-size: 16px !important;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
        }

        .is-mobile button {
          min-height: 44px;
          touch-action: manipulation;
          -webkit-tap-highlight-color: transparent;
        }

        .is-mobile .map-container {
          touch-action: none;
        }

        /* Prevent zoom on input focus */
        .is-mobile input:focus,
        .is-mobile select:focus,
        .is-mobile textarea:focus {
          font-size: 16px !important;
        }

        /* Better scrolling on mobile */
        .is-mobile {
          -webkit-overflow-scrolling: touch;
        }

        /* Hide scrollbars on mobile for cleaner look */
        .is-mobile ::-webkit-scrollbar {
          width: 0px;
          background: transparent;
        }

        /* Mobile-specific form improvements */
        .is-mobile .mobile-form {
          padding: 16px;
        }

        .is-mobile .mobile-form input,
        .is-mobile .mobile-form select,
        .is-mobile .mobile-form textarea {
          padding: 16px;
          border-radius: 12px;
          border: 2px solid #e5e7eb;
          font-size: 16px !important;
        }

        .is-mobile .mobile-form button {
          padding: 16px;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
        }

        /* Mobile navigation improvements */
        .is-mobile .mobile-nav {
          position: sticky;
          top: 0;
          z-index: 50;
          background: white;
          border-bottom: 1px solid #e5e7eb;
          padding: 12px 16px;
        }

        /* Mobile card improvements */
        .is-mobile .mobile-card {
          margin: 8px;
          border-radius: 16px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Mobile modal improvements */
        .is-mobile .mobile-modal {
          padding: 16px;
        }

        .is-mobile .mobile-modal-content {
          border-radius: 16px;
          max-height: 85vh;
        }

        /* Mobile table improvements */
        .is-mobile .mobile-table {
          font-size: 14px;
        }

        .is-mobile .mobile-table th,
        .is-mobile .mobile-table td {
          padding: 12px 8px;
        }

        /* Mobile notification improvements */
        .is-mobile .mobile-notification {
          margin: 16px;
          border-radius: 12px;
          padding: 16px;
          font-size: 16px;
        }

        /* Keyboard handling */
        .is-mobile.keyboard-open {
          position: fixed;
          width: 100%;
        }

        /* Safe area handling for devices with notches */
        .is-mobile .safe-area-top {
          padding-top: max(16px, env(safe-area-inset-top));
        }

        .is-mobile .safe-area-bottom {
          padding-bottom: max(16px, env(safe-area-inset-bottom));
        }

        .is-mobile .safe-area-left {
          padding-left: max(16px, env(safe-area-inset-left));
        }

        .is-mobile .safe-area-right {
          padding-right: max(16px, env(safe-area-inset-right));
        }
      `;

      document.head.appendChild(style);

      return () => {
        document.head.removeChild(style);
      };
    }
  }, []);

  return null;
};
