import { ReactNode } from 'react';
import Head from 'next/head';
import Navbar from './Navbar';

interface LayoutProps {
  children: ReactNode;
  title?: string;
}

export default function Layout({ children, title = 'BaroRide' }: LayoutProps) {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content="Book your ride with BaroRide - fixed fares and reliable service" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="BaroRide" />
        <meta name="mobile-web-app-capable" content="yes" />
        <link rel="icon" href="/logo-icon.svg" />
        <link rel="apple-touch-icon" href="/logo-icon.svg" />
        <link rel="shortcut icon" href="/logo-icon.svg" />
      </Head>
      <div className="min-h-screen flex flex-col bg-white text-gray-900 overflow-x-hidden">
        <Navbar />
        <main className="flex-grow w-full">{children}</main>
        <footer className="bg-gray-100 border-t border-gray-200 py-4 mt-auto">
          <div className="container mx-auto px-4 text-center text-gray-600 text-sm">
            &copy; {new Date().getFullYear()} BaroRide. All rights reserved.
          </div>
        </footer>
      </div>
    </>
  );
}
