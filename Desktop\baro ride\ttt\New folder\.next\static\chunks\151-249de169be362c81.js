"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[151],{2151:function(e,t,n){n.d(t,{Z:function(){return g}});var i=n(5893),o=n(9008),a=n.n(o),s=n(7294),r=n(1664),l=n.n(r),d=n(1163),c=n(837),m=n(404),u=n(109);function h(){let{user:e}=(0,c.a)(),[t,n]=(0,s.useState)([]),[o,a]=(0,s.useState)(!1),[r,l]=(0,s.useState)(0);(0,s.useEffect)(()=>{if(!e)return;let t=(0,u.IO)((0,u.hJ)(m.db,"notifications"),(0,u.ar)("userId","==",e.id)),i=(0,u.cf)(t,e=>{let t=[],i=0;e.forEach(e=>{var n;let o=e.data(),a={id:e.id,...o,createdAt:(null===(n=o.createdAt)||void 0===n?void 0:n.toDate())||new Date};t.push(a),!a.read&&i++}),t.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime()),n(t),l(i)});return()=>i()},[e]);let d=async t=>{if(e)try{await (0,u.r7)((0,u.doc)(m.db,"notifications",t),{read:!0})}catch(e){console.error("Error marking notification as read:",e)}},h=async()=>{if(e)try{let e=t.filter(e=>!e.read).map(e=>(0,u.r7)((0,u.doc)(m.db,"notifications",e.id),{read:!0}));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}},p=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"warning":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-blue-100 text-blue-800"}};return e?(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsxs)("button",{onClick:()=>{a(!o)},className:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none","aria-label":"Notifications",children:[(0,i.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),r>0&&(0,i.jsx)("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:r})]}),o&&(0,i.jsx)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20",children:(0,i.jsxs)("div",{className:"py-2",children:[(0,i.jsxs)("div",{className:"px-4 py-2 bg-gray-100 flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Notifications"}),r>0&&(0,i.jsx)("button",{onClick:h,className:"text-xs text-blue-600 hover:text-blue-800",children:"Mark all as read"})]}),(0,i.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===t.length?(0,i.jsx)("div",{className:"px-4 py-3 text-sm text-gray-500",children:"No notifications"}):t.map(e=>(0,i.jsx)("div",{className:"px-4 py-3 border-b border-gray-100 ".concat(e.read?"":"bg-blue-50"),onClick:()=>d(e.id),children:(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsxs)("div",{className:"flex-shrink-0 rounded-full p-1 ".concat(p(e.type)),children:["success"===e.type&&(0,i.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"warning"===e.type&&(0,i.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),"error"===e.type&&(0,i.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),"info"===e.type&&(0,i.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})]}),(0,i.jsxs)("div",{className:"ml-3 w-0 flex-1",children:[(0,i.jsx)("p",{className:"text-sm text-gray-900 whitespace-pre-line",children:e.message}),(0,i.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()}),e.driverDetails&&(0,i.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2",children:(0,i.jsx)("svg",{className:"w-5 h-5 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-xs font-semibold",children:e.driverDetails.fullName}),(0,i.jsxs)("p",{className:"text-xs",children:[e.driverDetails.vehicleColor," ",e.driverDetails.vehicleMake," ",e.driverDetails.vehicleModel]}),(0,i.jsxs)("p",{className:"text-xs font-medium",children:["License: ",e.driverDetails.licensePlate]})]})]})})]})]})},e.id))})]})})]}):null}function p(){let{user:e,signOut:t}=(0,c.a)(),[n,o]=(0,s.useState)(!1),[a,r]=(0,s.useState)(!1),m=(0,d.useRouter)();(0,s.useEffect)(()=>{r("/"!==m.pathname)},[m.pathname]);let u=()=>{o(!n)};return(0,i.jsxs)("nav",{className:"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50",children:[(0,i.jsx)("div",{className:"container mx-auto px-2 sm:px-4",children:(0,i.jsxs)("div",{className:"flex justify-between h-14 sm:h-16",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[a&&(0,i.jsx)("button",{onClick:()=>{m.back()},className:"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors","aria-label":"Go back",style:{touchAction:"manipulation"},children:(0,i.jsx)("svg",{className:"w-5 h-5 sm:w-7 sm:h-7 text-gray-700",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,i.jsxs)(l(),{href:"/",className:"flex-shrink-0 flex items-center space-x-2",children:[(0,i.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-8 w-8 sm:h-10 sm:w-10"}),(0,i.jsx)("span",{className:"text-blue-600 font-bold text-lg sm:text-xl",children:"BaroRide"})]})]}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:e?(0,i.jsxs)(i.Fragment,{children:["rider"===e.role?(0,i.jsx)(l(),{href:"/book",className:"text-gray-700 hover:text-blue-600",children:"Book a Ride"}):(0,i.jsx)(l(),{href:"/driver/dashboard",className:"text-gray-700 hover:text-blue-600",children:"Dashboard"}),(0,i.jsx)(h,{}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsxs)("button",{onClick:u,className:"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none",children:[(0,i.jsx)("span",{className:"mr-1",children:e.fullName}),(0,i.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),n&&(0,i.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200",children:(0,i.jsx)("button",{onClick:t,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign Out"})})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l(),{href:"/login",className:"text-gray-700 hover:text-blue-600",children:"Login"}),(0,i.jsx)(l(),{href:"/signup",className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"Sign Up"})]})}),(0,i.jsxs)("div",{className:"md:hidden flex items-center space-x-1",children:[e&&(0,i.jsx)(h,{}),(0,i.jsx)("button",{onClick:u,className:"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors",style:{touchAction:"manipulation"},"aria-label":"Toggle menu",children:(0,i.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n?(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"}):(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})})]})]})}),n&&(0,i.jsx)("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,i.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100 mb-2",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.fullName}),(0,i.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.role})]}),"rider"===e.role?(0,i.jsx)(l(),{href:"/book",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>o(!1),children:"\uD83D\uDCF1 Book a Ride"}):"driver"===e.role?(0,i.jsx)(l(),{href:"/driver/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>o(!1),children:"\uD83D\uDE97 Driver Dashboard"}):(0,i.jsx)(l(),{href:"/admin/dashboard",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>o(!1),children:"⚙️ Admin Dashboard"}),(0,i.jsx)("button",{onClick:()=>{t(),o(!1)},className:"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},children:"\uD83D\uDEAA Sign Out"})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l(),{href:"/login",className:"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>o(!1),children:"\uD83D\uDD11 Login"}),(0,i.jsx)(l(),{href:"/signup",className:"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation",style:{touchAction:"manipulation"},onClick:()=>o(!1),children:"✨ Sign Up"})]})})})]})}var x=n(445);function b(e){let{children:t}=e;return(0,s.useEffect)(()=>{if("undefined"==typeof document)return;(0,x.dN)();let e=(0,x.dz)();document.body.classList.remove("is-mobile","is-desktop","is-tablet","is-ios","is-android","has-touch","no-touch");let t=[e.isMobile?"is-mobile":"is-desktop",e.isTablet?"is-tablet":"",e.isIOS?"is-ios":"",e.isAndroid?"is-android":"",e.touchSupport?"has-touch":"no-touch"].filter(Boolean);if(document.body.classList.add(...t),e.isMobile){let e=document.querySelector('meta[name="viewport"]');e||((e=document.createElement("meta")).setAttribute("name","viewport"),document.head.appendChild(e)),e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover");let t=(e,t)=>{let n=document.querySelector('meta[name="'.concat(e,'"]'));n||((n=document.createElement("meta")).setAttribute("name",e),document.head.appendChild(n)),n.setAttribute("content",t)};t("mobile-web-app-capable","yes"),t("apple-mobile-web-app-capable","yes"),t("apple-mobile-web-app-status-bar-style","default"),t("apple-mobile-web-app-title","BaroRide"),t("theme-color","#1e3a5f"),t("format-detection","telephone=no")}let n=()=>{setTimeout(()=>{(0,x.dN)()},100)};return window.addEventListener("orientationchange",n),window.addEventListener("resize",n),()=>{window.removeEventListener("orientationchange",n),window.removeEventListener("resize",n)}},[]),(0,i.jsx)(i.Fragment,{children:t})}let f=()=>((0,s.useEffect)(()=>{if("undefined"!=typeof document&&(0,x.dz)().isMobile){let e=document.createElement("style");return e.textContent="\n        /* Mobile-specific overrides */\n        .is-mobile input,\n        .is-mobile select,\n        .is-mobile textarea {\n          font-size: 16px !important;\n          -webkit-appearance: none;\n          -moz-appearance: none;\n          appearance: none;\n        }\n\n        .is-mobile button {\n          min-height: 44px;\n          touch-action: manipulation;\n          -webkit-tap-highlight-color: transparent;\n        }\n\n        .is-mobile .map-container {\n          touch-action: none;\n        }\n\n        /* Prevent zoom on input focus */\n        .is-mobile input:focus,\n        .is-mobile select:focus,\n        .is-mobile textarea:focus {\n          font-size: 16px !important;\n        }\n\n        /* Better scrolling on mobile */\n        .is-mobile {\n          -webkit-overflow-scrolling: touch;\n        }\n\n        /* Hide scrollbars on mobile for cleaner look */\n        .is-mobile ::-webkit-scrollbar {\n          width: 0px;\n          background: transparent;\n        }\n\n        /* Mobile-specific form improvements */\n        .is-mobile .mobile-form {\n          padding: 16px;\n        }\n\n        .is-mobile .mobile-form input,\n        .is-mobile .mobile-form select,\n        .is-mobile .mobile-form textarea {\n          padding: 16px;\n          border-radius: 12px;\n          border: 2px solid #e5e7eb;\n          font-size: 16px !important;\n        }\n\n        .is-mobile .mobile-form button {\n          padding: 16px;\n          border-radius: 12px;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        /* Mobile navigation improvements */\n        .is-mobile .mobile-nav {\n          position: sticky;\n          top: 0;\n          z-index: 50;\n          background: white;\n          border-bottom: 1px solid #e5e7eb;\n          padding: 12px 16px;\n        }\n\n        /* Mobile card improvements */\n        .is-mobile .mobile-card {\n          margin: 8px;\n          border-radius: 16px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        /* Mobile modal improvements */\n        .is-mobile .mobile-modal {\n          padding: 16px;\n        }\n\n        .is-mobile .mobile-modal-content {\n          border-radius: 16px;\n          max-height: 85vh;\n        }\n\n        /* Mobile table improvements */\n        .is-mobile .mobile-table {\n          font-size: 14px;\n        }\n\n        .is-mobile .mobile-table th,\n        .is-mobile .mobile-table td {\n          padding: 12px 8px;\n        }\n\n        /* Mobile notification improvements */\n        .is-mobile .mobile-notification {\n          margin: 16px;\n          border-radius: 12px;\n          padding: 16px;\n          font-size: 16px;\n        }\n\n        /* Keyboard handling */\n        .is-mobile.keyboard-open {\n          position: fixed;\n          width: 100%;\n        }\n\n        /* Safe area handling for devices with notches */\n        .is-mobile .safe-area-top {\n          padding-top: max(16px, env(safe-area-inset-top));\n        }\n\n        .is-mobile .safe-area-bottom {\n          padding-bottom: max(16px, env(safe-area-inset-bottom));\n        }\n\n        .is-mobile .safe-area-left {\n          padding-left: max(16px, env(safe-area-inset-left));\n        }\n\n        .is-mobile .safe-area-right {\n          padding-right: max(16px, env(safe-area-inset-right));\n        }\n      ",document.head.appendChild(e),()=>{document.head.removeChild(e)}}},[]),null);function g(e){let{children:t,title:n="BaroRide"}=e;return(0,i.jsxs)(b,{children:[(0,i.jsxs)(a(),{children:[(0,i.jsx)("title",{children:n}),(0,i.jsx)("meta",{name:"description",content:"Book your ride with BaroRide - fixed fares and reliable service"}),(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"}),(0,i.jsx)("meta",{name:"theme-color",content:"#1e3a5f"}),(0,i.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,i.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,i.jsx)("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),(0,i.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,i.jsx)("meta",{name:"format-detection",content:"telephone=no"}),(0,i.jsx)("link",{rel:"icon",href:"/logo-icon.svg"}),(0,i.jsx)("link",{rel:"apple-touch-icon",href:"/logo-icon.svg"}),(0,i.jsx)("link",{rel:"shortcut icon",href:"/logo-icon.svg"})]}),(0,i.jsx)(f,{}),(0,i.jsxs)("div",{className:"min-h-screen flex flex-col bg-white text-gray-900 safe-area-top safe-area-bottom",children:[(0,i.jsx)(p,{}),(0,i.jsx)("main",{className:"flex-grow w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-6 safe-area-left safe-area-right",children:t}),(0,i.jsx)("footer",{className:"bg-gray-100 border-t border-gray-200 py-4 mt-auto safe-area-bottom",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center text-gray-600 text-sm",children:["\xa9 ",new Date().getFullYear()," BaroRide. All rights reserved."]})})]})]})}},445:function(e,t,n){n.d(t,{dN:function(){return m},dz:function(){return i},t9:function(){return r}});let i=()=>{let e=navigator.userAgent.toLowerCase(),t=window.screen.width,n=window.screen.height,i=window.devicePixelRatio||1,o=/android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(e)||t<=768,a=/ipad|android(?!.*mobile)/i.test(e)||t>768&&t<=1024,s=/iphone|ipad|ipod/i.test(e),r=/android/i.test(e),l=/safari/i.test(e)&&!/chrome/i.test(e);return{isMobile:o,isTablet:a,isDesktop:!o&&!a,isIOS:s,isAndroid:r,isSafari:l,isChrome:/chrome/i.test(e),screenWidth:t,screenHeight:n,pixelRatio:i,touchSupport:"ontouchstart"in window||navigator.maxTouchPoints>0,orientation:t>n?"landscape":"portrait"}},o=e=>{if(!e)return;e.style.touchAction="manipulation",e.style.webkitTapHighlightColor="transparent";let t=window.getComputedStyle(e);44>parseInt(t.height)&&(e.style.minHeight="".concat(44,"px")),44>parseInt(t.width)&&(e.style.minWidth="".concat(44,"px"))},a=()=>{i().isIOS&&document.querySelectorAll("input, select, textarea").forEach(e=>{(""===e.style.fontSize||16>parseInt(e.style.fontSize))&&(e.style.fontSize="16px")})},s=()=>{if(!i().isMobile)return;let e=()=>{let e=.01*window.innerHeight;document.documentElement.style.setProperty("--vh","".concat(e,"px"))};e(),window.addEventListener("resize",e),window.addEventListener("orientationchange",()=>{setTimeout(e,100)})},r=e=>{if(e&&i().isMobile){e.style.touchAction="none",e.addEventListener("touchstart",()=>{document.body.style.overflow="hidden"},{passive:!0}),e.addEventListener("touchend",()=>{document.body.style.overflow=""},{passive:!0});let t=()=>{let t=Math.min(.4*window.innerHeight,400);e.style.height="".concat(t,"px")};t(),window.addEventListener("resize",t),window.addEventListener("orientationchange",()=>{setTimeout(t,100)})}},l=e=>{e&&i().isMobile&&e.querySelectorAll("input, select, textarea, button").forEach(e=>{o(e),"INPUT"===e.tagName&&(("email"===e.type||"url"===e.type)&&(e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","none"),e.setAttribute("spellcheck","false")),"tel"===e.type?e.setAttribute("inputmode","tel"):"email"===e.type?e.setAttribute("inputmode","email"):"number"===e.type&&e.setAttribute("inputmode","numeric"))})},d=()=>{if(!i().isMobile)return;let e=window.innerHeight;window.addEventListener("resize",()=>{let t=window.innerHeight;if(e-t>150){document.body.classList.add("keyboard-open");let e=document.activeElement;e&&("INPUT"===e.tagName||"TEXTAREA"===e.tagName)&&setTimeout(()=>{e.scrollIntoView({behavior:"smooth",block:"center"})},100)}else document.body.classList.remove("keyboard-open")}),window.addEventListener("orientationchange",()=>{setTimeout(()=>{e=window.innerHeight},500)})},c=()=>{document.documentElement.style.scrollBehavior="smooth",document.documentElement.style.webkitOverflowScrolling="touch",document.body.style.webkitOverflowScrolling="touch",document.querySelectorAll(".overflow-auto, .overflow-y-auto, .overflow-x-auto").forEach(e=>{e.style.webkitOverflowScrolling="touch"})},m=()=>{if("loading"===document.readyState){document.addEventListener("DOMContentLoaded",()=>{m()});return}let e=i();if("undefined"!=typeof document&&document.body){let t=[e.isMobile?"is-mobile":"is-desktop",e.isTablet?"is-tablet":"",e.isIOS?"is-ios":"",e.isAndroid?"is-android":"",e.touchSupport?"has-touch":"no-touch"].filter(Boolean);document.body.classList.add(...t)}s(),a(),d(),c(),"undefined"!=typeof document&&(document.querySelectorAll("form").forEach(l),document.querySelectorAll('.map-container, [id*="map"]').forEach(e=>r(e))),console.log("Mobile optimizations initialized for:",e)}}}]);