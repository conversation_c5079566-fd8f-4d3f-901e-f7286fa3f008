import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import { getDeviceInfo } from '@/utils/mobile-optimization';
import BasicMap from '@/components/BasicMap';

export default function MobileTestPage() {
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const info = getDeviceInfo();
    setDeviceInfo(info);

    // Run mobile compatibility tests
    const runTests = () => {
      const results: Record<string, boolean> = {};

      // Test 1: Viewport meta tag
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      results.viewportMeta = viewportMeta !== null && 
        viewportMeta.getAttribute('content')?.includes('user-scalable=no') === true;

      // Test 2: Touch action support
      const testElement = document.createElement('div');
      testElement.style.touchAction = 'manipulation';
      results.touchAction = testElement.style.touchAction === 'manipulation';

      // Test 3: CSS custom properties support
      results.cssCustomProperties = CSS.supports('height', 'calc(var(--vh, 1vh) * 100)');

      // Test 4: Safe area support
      results.safeAreaSupport = CSS.supports('padding', 'env(safe-area-inset-top)');

      // Test 5: Input font size (prevents zoom)
      const input = document.createElement('input');
      input.style.fontSize = '16px';
      results.inputFontSize = input.style.fontSize === '16px';

      // Test 6: Touch event support
      results.touchEvents = 'ontouchstart' in window;

      // Test 7: Device pixel ratio
      results.devicePixelRatio = window.devicePixelRatio > 0;

      // Test 8: Orientation support
      results.orientationSupport = 'orientation' in window || 'onorientationchange' in window;

      setTestResults(results);
    };

    runTests();
  }, []);

  const handleLocationSelected = (location: any) => {
    console.log('Location selected:', location);
  };

  if (!deviceInfo) {
    return (
      <Layout title="Mobile Test - Loading">
        <div className="flex items-center justify-center min-h-64">
          <div className="mobile-spinner"></div>
          <span className="ml-2">Loading device info...</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Mobile Compatibility Test - BaroRide">
      <div className="container mx-auto p-4 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">Mobile Compatibility Test</h1>

        {/* Device Information */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Device Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Device Type:</strong> {deviceInfo.isMobile ? 'Mobile' : deviceInfo.isTablet ? 'Tablet' : 'Desktop'}
            </div>
            <div>
              <strong>Operating System:</strong> {deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : 'Other'}
            </div>
            <div>
              <strong>Browser:</strong> {deviceInfo.isSafari ? 'Safari' : deviceInfo.isChrome ? 'Chrome' : 'Other'}
            </div>
            <div>
              <strong>Touch Support:</strong> {deviceInfo.touchSupport ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Screen Size:</strong> {deviceInfo.screenWidth} × {deviceInfo.screenHeight}
            </div>
            <div>
              <strong>Pixel Ratio:</strong> {deviceInfo.pixelRatio}
            </div>
            <div>
              <strong>Orientation:</strong> {deviceInfo.orientation}
            </div>
          </div>
        </div>

        {/* Compatibility Tests */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Compatibility Tests</h2>
          <div className="space-y-2">
            {Object.entries(testResults).map(([test, passed]) => (
              <div key={test} className="flex items-center justify-between p-2 border rounded">
                <span className="text-sm">{test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {passed ? 'PASS' : 'FAIL'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Form Testing */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Form Input Testing</h2>
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Text Input (should not zoom on focus)</label>
              <input
                type="text"
                placeholder="Type here to test zoom prevention"
                className="mobile-input w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Email Input</label>
              <input
                type="email"
                placeholder="<EMAIL>"
                className="mobile-input w-full"
                autoComplete="email"
                inputMode="email"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Phone Input</label>
              <input
                type="tel"
                placeholder="+****************"
                className="mobile-input w-full"
                autoComplete="tel"
                inputMode="tel"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Number Input</label>
              <input
                type="number"
                placeholder="123"
                className="mobile-input w-full"
                inputMode="numeric"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Select Dropdown</label>
              <select className="mobile-input w-full">
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Textarea</label>
              <textarea
                placeholder="Type a longer message here..."
                className="mobile-input w-full"
                rows={4}
              />
            </div>
          </form>
        </div>

        {/* Button Testing */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Button Touch Testing</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="mobile-button bg-blue-500 text-white">
              Primary Button
            </button>
            <button className="mobile-button bg-gray-500 text-white">
              Secondary Button
            </button>
            <button className="mobile-button bg-green-500 text-white">
              Success Button
            </button>
            <button className="mobile-button bg-red-500 text-white">
              Danger Button
            </button>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            All buttons should have minimum 44px touch targets and proper touch feedback.
          </p>
        </div>

        {/* Map Testing */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Map Interaction Testing</h2>
          <p className="text-sm text-gray-600 mb-4">
            Test map interactions on mobile. The map should prevent page scrolling when interacting.
          </p>
          <div className="map-container">
            <BasicMap
              height={deviceInfo.isMobile ? "40vh" : "300px"}
              selectable={true}
              onLocationSelected={handleLocationSelected}
            />
          </div>
        </div>

        {/* Scroll Testing */}
        <div className="mobile-card mb-6">
          <h2 className="text-lg font-semibold mb-4">Scroll Performance Testing</h2>
          <div className="h-64 overflow-y-auto border rounded p-4 bg-gray-50">
            <p className="mb-4">This is a scrollable area to test smooth scrolling performance on mobile.</p>
            {Array.from({ length: 50 }, (_, i) => (
              <p key={i} className="mb-2">
                Scroll test item {i + 1} - This should scroll smoothly with momentum on iOS devices.
              </p>
            ))}
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="mobile-card">
          <h2 className="text-lg font-semibold mb-4">Performance Metrics</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Connection:</strong> {(navigator as any).connection?.effectiveType || 'Unknown'}
            </div>
            <div>
              <strong>Memory:</strong> {(performance as any).memory?.usedJSHeapSize ? 
                `${Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB` : 'Unknown'}
            </div>
            <div>
              <strong>Hardware Concurrency:</strong> {navigator.hardwareConcurrency || 'Unknown'}
            </div>
          </div>
        </div>

        {/* Test Summary */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 className="font-semibold text-blue-800 mb-2">Test Summary</h3>
          <p className="text-blue-700 text-sm">
            {Object.values(testResults).filter(Boolean).length} of {Object.keys(testResults).length} tests passed.
            {deviceInfo.isMobile ? ' Mobile optimizations are active.' : ' Desktop mode detected.'}
          </p>
        </div>
      </div>
    </Layout>
  );
}
