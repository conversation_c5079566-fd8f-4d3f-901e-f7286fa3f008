<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 900" width="900" height="900">
  <!-- Background -->
  <rect width="900" height="900" fill="#f8f9fa"/>

  <!-- Large B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Left Vertical -->
    <rect x="50" y="50" width="80" height="350" rx="40"/>
    <!-- B Letter Top Section -->
    <path d="M50 50 L280 50 Q350 50 350 120 Q350 190 280 190 L130 190 L130 120 L280 120 Q300 120 300 140 Q300 160 280 160 L130 160 L130 190 L280 190 Q350 190 350 120 Q350 50 280 50 L50 50 Z"/>
    <!-- B Letter Bottom Section -->
    <path d="M50 210 L280 210 Q350 210 350 280 Q350 350 280 350 L130 350 L130 280 L280 280 Q300 280 300 300 Q300 320 280 320 L130 320 L130 350 L280 350 Q350 350 350 280 Q350 210 280 210 L50 210 Z"/>
  </g>

  <!-- Large R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Left Vertical -->
    <rect x="500" y="50" width="80" height="350" rx="40"/>
    <!-- R Letter Top Section -->
    <path d="M500 50 L720 50 Q790 50 790 120 Q790 190 720 190 L580 190 L580 120 L720 120 Q740 120 740 140 Q740 160 720 160 L580 160 L580 190 L720 190 Q790 190 790 120 Q790 50 720 50 L500 50 Z"/>
    <!-- R Letter Diagonal -->
    <path d="M580 190 L720 190 L820 350 Q830 370 810 370 L730 370 Q710 370 700 350 L620 230 L580 230 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(350, 350)">
    <!-- Car Body -->
    <path d="M20 30 Q20 10 40 10 L160 10 Q180 10 180 30 L180 60 L160 60 L160 80 L140 80 Q130 90 120 80 L80 80 Q70 90 60 80 L40 80 L40 60 L20 60 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M50 10 Q50 -10 70 -10 L130 -10 Q150 -10 150 10 L130 10 L70 10 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="60" y="-5" width="80" height="15" rx="5" fill="#e9ecef"/>
    <!-- Side Windows -->
    <rect x="45" y="0" width="15" height="10" rx="2" fill="#e9ecef"/>
    <rect x="140" y="0" width="15" height="10" rx="2" fill="#e9ecef"/>
    <!-- Headlights -->
    <ellipse cx="10" cy="45" rx="8" ry="6" fill="#20c997"/>
    <ellipse cx="190" cy="45" rx="8" ry="6" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="50" cy="80" r="12" fill="#1e3a5f"/>
    <circle cx="150" cy="80" r="12" fill="#1e3a5f"/>
    <circle cx="50" cy="80" r="6" fill="#6c757d"/>
    <circle cx="150" cy="80" r="6" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 520)">
    <!-- Wave 1 -->
    <path d="M0 0 Q150 -30 300 0 Q450 30 600 0 Q750 -30 900 0 L900 40 Q750 70 600 40 Q450 10 300 40 Q150 70 0 40 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M0 25 Q150 -5 300 25 Q450 55 600 25 Q750 -5 900 25 L900 65 Q750 95 600 65 Q450 35 300 65 Q150 95 0 65 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M0 50 Q150 20 300 50 Q450 80 600 50 Q750 20 900 50 L900 90 Q750 120 600 90 Q450 60 300 90 Q150 120 0 90 Z" fill="#138496"/>
  </g>

  <!-- BARO RIDE Text -->
  <g transform="translate(50, 720)">
    <text x="0" y="100" font-family="Arial, sans-serif" font-size="120" font-weight="900" fill="#1e3a5f">BARO RIDE</text>
  </g>
</svg>
