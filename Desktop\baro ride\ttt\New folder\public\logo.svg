<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 900" width="900" height="900">
  <!-- Background -->
  <rect width="900" height="900" fill="#f5f5f5"/>
  
  <!-- Large B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Shape -->
    <path d="M50 80 L50 420 L80 420 L80 80 Z"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M50 80 L280 80 Q380 80 380 140 Q380 200 280 200 L80 200 L80 140 L280 140 Q320 140 320 160 Q320 180 280 180 L80 180 L80 200 L280 200 Q380 200 380 140 Q380 80 280 80 L50 80 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M50 250 L300 250 Q400 250 400 310 Q400 370 300 370 L80 370 L80 310 L300 310 Q340 310 340 330 Q340 350 300 350 L80 350 L80 370 L300 370 Q400 370 400 310 Q400 250 300 250 L50 250 Z"/>
    <!-- B <PERSON> Middle Connection -->
    <rect x="50" y="200" width="30" height="50"/>
  </g>
  
  <!-- Large R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Vertical Bar -->
    <rect x="500" y="80" width="80" height="340"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M500 80 L720 80 Q820 80 820 140 Q820 200 720 200 L580 200 L580 140 L720 140 Q760 140 760 160 Q760 180 720 180 L580 180 L580 200 L720 200 Q820 200 820 140 Q820 80 720 80 L500 80 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M580 200 L720 200 L850 420 Q860 440 840 440 L780 440 Q760 440 750 420 L640 240 L580 240 Z"/>
  </g>
  
  <!-- Car Icon positioned between B and R -->
  <g transform="translate(320, 280)">
    <!-- Car Main Body -->
    <path d="M40 80 Q40 60 60 60 L200 60 Q220 60 220 80 L220 120 L200 120 L200 140 L180 140 Q170 150 160 140 L100 140 Q90 150 80 140 L60 140 L60 120 L40 120 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M70 60 Q70 40 90 40 L170 40 Q190 40 190 60 L170 60 L90 60 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="80" y="45" width="100" height="15" rx="5" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="65" y="50" width="20" height="10" rx="2" fill="#f5f5f5"/>
    <rect x="175" y="50" width="20" height="10" rx="2" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="55" cy="75" r="5" fill="#1e3a5f"/>
    <circle cx="205" cy="75" r="5" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="30" cy="100" rx="10" ry="8" fill="#20c997"/>
    <ellipse cx="230" cy="100" rx="10" ry="8" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="80" cy="140" r="18" fill="#1e3a5f"/>
    <circle cx="180" cy="140" r="18" fill="#1e3a5f"/>
    <circle cx="80" cy="140" r="10" fill="#6c757d"/>
    <circle cx="180" cy="140" r="10" fill="#6c757d"/>
  </g>
  
  <!-- Wave Elements -->
  <g transform="translate(0, 500)">
    <!-- Wave 1 -->
    <path d="M0 0 Q150 -40 300 0 Q450 40 600 0 Q750 -40 900 0 L900 50 Q750 90 600 50 Q450 10 300 50 Q150 90 0 50 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M0 30 Q150 -10 300 30 Q450 70 600 30 Q750 -10 900 30 L900 80 Q750 120 600 80 Q450 40 300 80 Q150 120 0 80 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M0 60 Q150 20 300 60 Q450 100 600 60 Q750 20 900 60 L900 110 Q750 150 600 110 Q450 70 300 110 Q150 150 0 110 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M0 90 Q150 50 300 90 Q450 130 600 90 Q750 50 900 90 L900 140 Q750 180 600 140 Q450 100 300 140 Q150 180 0 140 Z" fill="#0f6674"/>
  </g>
  
  <!-- BARO RIDE Text -->
  <g transform="translate(50, 700)">
    <text x="400" y="100" font-family="Arial, sans-serif" font-size="120" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
  </g>
</svg>
