<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 900" width="900" height="900">
  <!-- Background -->
  <rect width="900" height="900" fill="#f5f5f5"/>

  <!-- Large B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Vertical Bar -->
    <rect x="50" y="80" width="80" height="400"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M50 80 L300 80 Q400 80 400 150 Q400 220 300 220 L130 220 L130 150 L300 150 Q350 150 350 165 Q350 180 300 180 L130 180 L130 220 L300 220 Q400 220 400 150 Q400 80 300 80 L50 80 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M50 260 L320 260 Q420 260 420 330 Q420 400 320 400 L130 400 L130 330 L320 330 Q370 330 370 345 Q370 360 320 360 L130 360 L130 400 L320 400 Q420 400 420 330 Q420 260 320 260 L50 260 Z"/>
    <!-- B Letter Middle Connection -->
    <rect x="50" y="220" width="80" height="40"/>
  </g>

  <!-- Large R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Main Vertical Bar -->
    <rect x="480" y="80" width="80" height="400"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M480 80 L720 80 Q820 80 820 150 Q820 220 720 220 L560 220 L560 150 L720 150 Q770 150 770 165 Q770 180 720 180 L560 180 L560 220 L720 220 Q820 220 820 150 Q820 80 720 80 L480 80 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M560 220 L720 220 L850 480 Q860 500 840 500 L780 500 Q760 500 750 480 L640 280 L560 280 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(350, 320)">
    <!-- Car Main Body -->
    <path d="M0 40 Q0 20 20 20 L180 20 Q200 20 200 40 L200 80 L180 80 L180 100 L160 100 Q150 110 140 100 L60 100 Q50 110 40 100 L20 100 L20 80 L0 80 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M30 20 Q30 0 50 0 L150 0 Q170 0 170 20 L150 20 L50 20 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="40" y="5" width="120" height="15" rx="5" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="25" y="10" width="25" height="10" rx="2" fill="#f5f5f5"/>
    <rect x="150" y="10" width="25" height="10" rx="2" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="15" cy="35" r="8" fill="#1e3a5f"/>
    <circle cx="185" cy="35" r="8" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="-10" cy="60" rx="15" ry="12" fill="#20c997"/>
    <ellipse cx="210" cy="60" rx="15" ry="12" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="40" cy="100" r="25" fill="#1e3a5f"/>
    <circle cx="160" cy="100" r="25" fill="#1e3a5f"/>
    <circle cx="40" cy="100" r="15" fill="#6c757d"/>
    <circle cx="160" cy="100" r="15" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 550)">
    <!-- Wave 1 -->
    <path d="M0 0 Q150 -30 300 0 Q450 30 600 0 Q750 -30 900 0 L900 40 Q750 70 600 40 Q450 10 300 40 Q150 70 0 40 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M0 25 Q150 -5 300 25 Q450 55 600 25 Q750 -5 900 25 L900 65 Q750 95 600 65 Q450 35 300 65 Q150 95 0 65 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M0 50 Q150 20 300 50 Q450 80 600 50 Q750 20 900 50 L900 90 Q750 120 600 90 Q450 60 300 90 Q150 120 0 90 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M0 75 Q150 45 300 75 Q450 105 600 75 Q750 45 900 75 L900 115 Q750 145 600 115 Q450 85 300 115 Q150 145 0 115 Z" fill="#0f6674"/>
  </g>

  <!-- BARO RIDE Text -->
  <g transform="translate(50, 750)">
    <text x="400" y="80" font-family="Arial, sans-serif" font-size="100" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
  </g>
</svg>
