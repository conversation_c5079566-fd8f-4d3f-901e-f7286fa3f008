<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 900" width="900" height="900">
  <!-- Background -->
  <rect width="900" height="900" fill="#f5f5f5"/>

  <!-- Large B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Main Shape -->
    <path d="M50 50 L50 350 L80 350 L80 50 Z"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M50 50 L250 50 Q350 50 350 120 Q350 190 250 190 L80 190 L80 120 L250 120 Q300 120 300 140 Q300 160 250 160 L80 160 L80 190 L250 190 Q350 190 350 120 Q350 50 250 50 L50 50 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M50 210 L270 210 Q370 210 370 280 Q370 350 270 350 L80 350 L80 280 L270 280 Q320 280 320 300 Q320 320 270 320 L80 320 L80 350 L270 350 Q370 350 370 280 Q370 210 270 210 L50 210 Z"/>
    <!-- B Letter Middle Connection -->
    <rect x="50" y="190" width="30" height="20"/>
  </g>

  <!-- Large R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Main Shape -->
    <path d="M500 50 L500 350 L530 350 L530 50 Z"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M500 50 L700 50 Q800 50 800 120 Q800 190 700 190 L530 190 L530 120 L700 120 Q750 120 750 140 Q750 160 700 160 L530 160 L530 190 L700 190 Q800 190 800 120 Q800 50 700 50 L500 50 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M530 190 L700 190 L850 350 Q860 370 840 370 L780 370 Q760 370 750 350 L620 230 L530 230 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(300, 250)">
    <!-- Car Main Body -->
    <path d="M50 80 Q50 60 70 60 L230 60 Q250 60 250 80 L250 120 L230 120 L230 140 L210 140 Q200 150 190 140 L110 140 Q100 150 90 140 L70 140 L70 120 L50 120 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M80 60 Q80 40 100 40 L200 40 Q220 40 220 60 L200 60 L100 60 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="90" y="45" width="120" height="15" rx="5" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="75" y="50" width="20" height="10" rx="2" fill="#f5f5f5"/>
    <rect x="205" y="50" width="20" height="10" rx="2" fill="#f5f5f5"/>
    <!-- Side Mirrors -->
    <circle cx="65" cy="75" r="8" fill="#1e3a5f"/>
    <circle cx="235" cy="75" r="8" fill="#1e3a5f"/>
    <!-- Headlights -->
    <ellipse cx="40" cy="100" rx="12" ry="10" fill="#20c997"/>
    <ellipse cx="260" cy="100" rx="12" ry="10" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="90" cy="140" r="20" fill="#1e3a5f"/>
    <circle cx="210" cy="140" r="20" fill="#1e3a5f"/>
    <circle cx="90" cy="140" r="12" fill="#6c757d"/>
    <circle cx="210" cy="140" r="12" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 450)">
    <!-- Wave 1 -->
    <path d="M0 0 Q150 -40 300 0 Q450 40 600 0 Q750 -40 900 0 L900 50 Q750 90 600 50 Q450 10 300 50 Q150 90 0 50 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M0 30 Q150 -10 300 30 Q450 70 600 30 Q750 -10 900 30 L900 80 Q750 120 600 80 Q450 40 300 80 Q150 120 0 80 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M0 60 Q150 20 300 60 Q450 100 600 60 Q750 20 900 60 L900 110 Q750 150 600 110 Q450 70 300 110 Q150 150 0 110 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M0 90 Q150 50 300 90 Q450 130 600 90 Q750 50 900 90 L900 140 Q750 180 600 140 Q450 100 300 140 Q150 180 0 140 Z" fill="#0f6674"/>
  </g>

  <!-- BARO RIDE Text -->
  <g transform="translate(50, 650)">
    <text x="400" y="100" font-family="Arial, sans-serif" font-size="120" font-weight="900" fill="#1e3a5f" text-anchor="middle">BARO RIDE</text>
  </g>
</svg>
