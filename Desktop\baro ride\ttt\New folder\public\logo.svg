<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 900" width="900" height="900">
  <!-- Background -->
  <rect width="900" height="900" fill="#f5f5f5"/>

  <!-- Large B Letter -->
  <g fill="#1e3a5f">
    <!-- B Letter Vertical Bar -->
    <rect x="50" y="50" width="80" height="400" rx="20"/>
    <!-- B Letter Top Rounded Section -->
    <path d="M50 50 L280 50 Q380 50 380 130 Q380 210 280 210 L130 210 L130 130 L280 130 Q320 130 320 150 Q320 170 280 170 L130 170 L130 210 L280 210 Q380 210 380 130 Q380 50 280 50 L50 50 Z"/>
    <!-- B Letter Bottom Rounded Section -->
    <path d="M50 240 L300 240 Q400 240 400 320 Q400 400 300 400 L130 400 L130 320 L300 320 Q340 320 340 340 Q340 360 300 360 L130 360 L130 400 L300 400 Q400 400 400 320 Q400 240 300 240 L50 240 Z"/>
  </g>

  <!-- Large R Letter -->
  <g fill="#1e3a5f">
    <!-- R Letter Vertical Bar -->
    <rect x="500" y="50" width="80" height="400" rx="20"/>
    <!-- R Letter Top Rounded Section -->
    <path d="M500 50 L720 50 Q820 50 820 130 Q820 210 720 210 L580 210 L580 130 L720 130 Q760 130 760 150 Q760 170 720 170 L580 170 L580 210 L720 210 Q820 210 820 130 Q820 50 720 50 L500 50 Z"/>
    <!-- R Letter Diagonal Leg -->
    <path d="M580 210 L720 210 L850 450 Q860 470 840 470 L780 470 Q760 470 750 450 L640 250 L580 250 Z"/>
  </g>

  <!-- Car Icon positioned between B and R -->
  <g transform="translate(350, 320)">
    <!-- Car Body -->
    <path d="M20 60 Q20 40 40 40 L160 40 Q180 40 180 60 L180 100 L160 100 L160 120 L140 120 Q130 130 120 120 L80 120 Q70 130 60 120 L40 120 L40 100 L20 100 Z" fill="#1e3a5f"/>
    <!-- Car Roof -->
    <path d="M50 40 Q50 20 70 20 L130 20 Q150 20 150 40 L130 40 L70 40 Z" fill="#1e3a5f"/>
    <!-- Windshield -->
    <rect x="60" y="25" width="80" height="15" rx="5" fill="#f5f5f5"/>
    <!-- Side Windows -->
    <rect x="45" y="30" width="15" height="10" rx="2" fill="#f5f5f5"/>
    <rect x="140" y="30" width="15" height="10" rx="2" fill="#f5f5f5"/>
    <!-- Headlights -->
    <ellipse cx="15" cy="80" rx="8" ry="6" fill="#20c997"/>
    <ellipse cx="185" cy="80" rx="8" ry="6" fill="#20c997"/>
    <!-- Wheels -->
    <circle cx="60" cy="120" r="15" fill="#1e3a5f"/>
    <circle cx="140" cy="120" r="15" fill="#1e3a5f"/>
    <circle cx="60" cy="120" r="8" fill="#6c757d"/>
    <circle cx="140" cy="120" r="8" fill="#6c757d"/>
  </g>

  <!-- Wave Elements -->
  <g transform="translate(0, 580)">
    <!-- Wave 1 -->
    <path d="M0 0 Q225 -50 450 0 Q675 50 900 0 L900 40 Q675 90 450 40 Q225 -10 0 40 Z" fill="#20c997"/>
    <!-- Wave 2 -->
    <path d="M0 30 Q225 -20 450 30 Q675 80 900 30 L900 70 Q675 120 450 70 Q225 20 0 70 Z" fill="#17a2b8"/>
    <!-- Wave 3 -->
    <path d="M0 60 Q225 10 450 60 Q675 110 900 60 L900 100 Q675 150 450 100 Q225 50 0 100 Z" fill="#138496"/>
    <!-- Wave 4 -->
    <path d="M0 90 Q225 40 450 90 Q675 140 900 90 L900 130 Q675 180 450 130 Q225 80 0 130 Z" fill="#0f6674"/>
  </g>

  <!-- BARO RIDE Text -->
  <g transform="translate(50, 750)">
    <text x="0" y="100" font-family="Arial, sans-serif" font-size="120" font-weight="900" fill="#1e3a5f">BARO RIDE</text>
  </g>
</svg>
