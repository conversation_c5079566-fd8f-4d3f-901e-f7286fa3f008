(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[517],{5031:function(e,t,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/driver/dashboard",function(){return o(5400)}])},5695:function(e,t,o){"use strict";o.d(t,{Z:function(){return c}});var n=o(5893),s=o(7294),r=o(1163),a=o(837),i=o(7339),l=o(6492);function c(e){let{children:t,requiredRoles:o,redirectTo:c="/login"}=e,{user:d,loading:u}=(0,a.a)(),{checkAccess:m}=(0,i.s)(),h=(0,r.useRouter)(),{showNotification:g}=(0,l.l)();return((0,s.useEffect)(()=>{if(!u){if(!d){h.push(c),g("Please log in to access this page","warning");return}if(!m(o)){let e="/",t="Access denied. You do not have permission to view this page.";"driver"===d.role?(e="/driver/dashboard",t="Access denied. Redirected to driver dashboard."):"rider"===d.role&&(e="/",t="Access denied. Redirected to home page."),h.push(e),g(t,"warning")}}},[d,u,o,h,c,m]),u)?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):d&&m(o)?(0,n.jsx)(n.Fragment,{children:t}):null}},5400:function(e,t,o){"use strict";o.r(t),o.d(t,{default:function(){return p}});var n=o(5893),s=o(7294),r=o(837),a=o(404),i=o(109),l=o(6158),c=o.n(l);function d(e){let{currentLocation:t,pickupLocation:o,height:r="400px",selectable:a=!1,onDirectionsCalculated:i,onLocationSelected:l}=e,d=(0,s.useRef)(null),u=(0,s.useRef)(null),[m,h]=(0,s.useState)(null),[g,p]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(!d.current){console.error("Map container ref is not available");return}if(u.current){console.log("Map already initialized, skipping"),t&&0!==t.lat&&0!==t.lng&&(console.log("Updating map center to current location:",t),u.current.flyTo({center:[t.lng,t.lat],zoom:13}));return}let e=t&&0!==t.lng?t.lng:34.5925,n=t&&0!==t.lat?t.lat:8.2483;console.log("Using manual location selection only - automatic geolocation disabled");try{return console.log("Initializing map with center:",[e,n]),setTimeout(()=>{try{console.log("Creating new map instance");let s=new(c()).Map({container:d.current,style:"mapbox://styles/mapbox/streets-v12",center:[e,n],zoom:13,attributionControl:!1,touchZoomRotate:!0,dragRotate:!1,touchPitch:!1});s.touchZoomRotate.enable(),s.touchZoomRotate.disableRotation(),u.current=s,s.addControl(new(c()).NavigationControl,"top-right"),s.on("load",()=>{if(console.log("Map loaded successfully"),s.resize(),t&&0!==t.lat&&o&&0!==o.lat){console.log("Both locations available, updating markers and calculating route"),document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove());let e=document.createElement("div");e.className="marker driver-marker",e.style.backgroundColor="#3b82f6",e.style.width="20px",e.style.height="20px",e.style.borderRadius="50%",e.style.border="2px solid white",new(c()).Marker(e).setLngLat([t.lng,t.lat]).addTo(s);let n=document.createElement("div");n.className="marker pickup-marker",n.style.backgroundColor="#ef4444",n.style.width="20px",n.style.height="20px",n.style.borderRadius="50%",n.style.border="2px solid white";let r=new(c()).Popup({offset:25}).setHTML("<p>".concat(o.address||"Pickup Location","</p>"));new(c()).Marker(n).setLngLat([o.lng,o.lat]).setPopup(r).addTo(s);let a=new(c()).LngLatBounds().extend([t.lng,t.lat]).extend([o.lng,o.lat]);s.fitBounds(a,{padding:100}),setTimeout(()=>{x(t,o)},500)}}),s.on("error",e=>{console.error("Mapbox error:",e)}),console.log("Mapbox map initialization started")}catch(e){console.error("Error in delayed map initialization:",e)}},300),()=>{u.current&&(console.log("Removing map instance"),u.current.remove(),u.current=null)}}catch(e){console.error("Error initializing Mapbox map:",e)}},[t]),(0,s.useEffect)(()=>{if(!u.current||!a)return;let e=async e=>{console.log("Map interaction detected:",e.type);let{lng:t,lat:o}=e.lngLat,n=document.createElement("div");n.className="marker temp-marker",n.style.backgroundColor="#10b981",n.style.width="24px",n.style.height="24px",n.style.borderRadius="50%",n.style.border="2px solid white",n.style.boxShadow="0 0 0 2px rgba(0,0,0,0.1)",n.style.cursor="pointer",document.querySelectorAll(".temp-marker").forEach(e=>e.remove()),u.current&&new(c()).Marker(n).setLngLat([t,o]).addTo(u.current),p(!0);try{let e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(t,",").concat(o,".json?access_token=").concat(c().accessToken)),n=await e.json(),s="Unknown location";n.features&&n.features.length>0&&(s=n.features[0].place_name);let r={lat:o,lng:t,address:s};if(u.current){let e=new(c()).Popup({offset:25,closeButton:!0,closeOnClick:!1,maxWidth:"300px"}).setLngLat([t,o]).setHTML('\n              <div style="padding: 5px;">\n                <p style="margin-bottom: 10px; font-weight: 500;">'.concat(s,'</p>\n                <button\n                  id="select-location"\n                  style="background-color: #3b82f6; color: white; padding: 8px 16px; border-radius: 4px; border: none; width: 100%; font-size: 14px; cursor: pointer; touch-action: manipulation;"\n                >\n                  Select this location\n                </button>\n              </div>\n            ')).addTo(u.current);setTimeout(()=>{let t=document.getElementById("select-location");if(t){let o=()=>{console.log("Selection button clicked/touched"),l&&l(r),e.remove()};t.addEventListener("click",o),t.addEventListener("touchend",e=>{e.preventDefault(),o()})}else console.error("Select button not found in popup")},200)}}catch(e){console.error("Error geocoding location:",e)}finally{p(!1)}};return u.current.on("click",e),u.current.on("touchend",e),()=>{u.current&&(u.current.off("click",e),u.current.off("touchend",e))}},[a,l]),(0,s.useEffect)(()=>{if(!u.current){console.log("Map not initialized yet, skipping marker update");return}if(console.log("Updating markers with currentLocation:",t,"pickupLocation:",o),document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),t&&0!==t.lat&&0!==t.lng){console.log("Adding current location marker at:",t);let e=document.createElement("div");e.className="marker driver-marker",e.style.backgroundColor="#3b82f6",e.style.width="24px",e.style.height="24px",e.style.borderRadius="50%",e.style.border="2px solid white",e.style.boxShadow="0 0 0 2px rgba(0,0,0,0.1)",e.style.cursor="pointer",new(c()).Marker(e).setLngLat([t.lng,t.lat]).addTo(u.current),o&&0!==o.lat&&0!==o.lng||(console.log("Centering map on current location"),u.current.flyTo({center:[t.lng,t.lat],zoom:13}))}else console.log("Current location not set or invalid");if(o&&0!==o.lat&&0!==o.lng){console.log("Adding pickup location marker at:",o);let e=document.createElement("div");e.className="marker pickup-marker",e.style.backgroundColor="#ef4444",e.style.width="24px",e.style.height="24px",e.style.borderRadius="50%",e.style.border="2px solid white",e.style.boxShadow="0 0 0 2px rgba(0,0,0,0.1)",e.style.cursor="pointer";let n=new(c()).Popup({offset:25,closeButton:!0,maxWidth:"300px"}).setHTML('<div style="padding: 5px;"><p style="font-weight: 500;">'.concat(o.address||"Pickup Location","</p></div>"));if(new(c()).Marker(e).setLngLat([o.lng,o.lat]).setPopup(n).addTo(u.current),t&&0!==t.lat&&0!==t.lng){console.log("Both locations set, fitting bounds and calculating directions");try{let e=new(c()).LngLatBounds().extend([t.lng,t.lat]).extend([o.lng,o.lat]);u.current.fitBounds(e,{padding:100}),setTimeout(()=>{x(t,o)},500)}catch(e){console.error("Error fitting bounds:",e)}}else console.log("Current location not set, centering on pickup location only"),u.current.flyTo({center:[o.lng,o.lat],zoom:13})}else console.log("Pickup location not set or invalid")},[t,o]);let x=async(e,t)=>{if(!u.current){console.error("Cannot calculate directions: map is not initialized");return}if(console.log("Calculating directions from",e,"to",t),!e||!t||0===e.lat||0===e.lng||0===t.lat||0===t.lng){console.error("Invalid coordinates for directions:",{start:e,end:t});return}try{let o="https://api.mapbox.com/directions/v5/mapbox/driving/".concat(e.lng,",").concat(e.lat,";").concat(t.lng,",").concat(t.lat,"?steps=true&geometries=geojson&access_token=").concat(c().accessToken);console.log("Fetching directions from URL:",o);let n=await fetch(o);if(!n.ok)throw Error("Directions API error: ".concat(n.status," ").concat(n.statusText));let s=await n.json();if(console.log("Directions API response:",s),s.routes&&s.routes.length>0){let e=s.routes[0];console.log("Route data:",e),h(e),i?(console.log("Calling onDirectionsCalculated with route data"),i(e)):console.warn("onDirectionsCalculated callback is not provided"),u.current?(console.log("Adding route to map"),u.current.getLayer("route")&&(console.log("Removing existing route layer"),u.current.removeLayer("route")),u.current.getSource("route")&&(console.log("Removing existing route source"),u.current.removeSource("route")),u.current.addSource("route",{type:"geojson",data:{type:"Feature",properties:{},geometry:e.geometry}}),u.current.addLayer({id:"route",type:"line",source:"route",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#3b82f6","line-width":6,"line-opacity":.8}}),console.log("Route added to map successfully")):console.error("Map reference lost while adding route")}else console.error("No routes found in directions API response:",s)}catch(e){console.error("Error calculating directions:",e)}};return(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{ref:d,className:"touch-manipulation",style:{width:"100%",height:r,position:"relative",touchAction:"manipulation"},children:[g&&(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 z-10",children:(0,n.jsx)("div",{className:"bg-white p-3 rounded-full",children:(0,n.jsxs)("svg",{className:"animate-spin h-6 w-6 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})}),a&&(0,n.jsx)("div",{className:"absolute top-2 left-2 right-2 bg-white p-2 rounded shadow z-10 text-sm",children:(0,n.jsx)("p",{className:"text-gray-700",children:"Click anywhere on the map to select a pickup location"})})]}),m&&(0,n.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:[(0,n.jsxs)("p",{children:["Distance: ",(m.distance/1e3).toFixed(2)," km"]}),(0,n.jsxs)("p",{children:["Duration: ",Math.round(m.duration/60)," minutes"]})]})]})}o(571),c().accessToken="pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A";var u=o(7994),m=o(6492),h=o(5695);function g(){let{user:e}=(0,r.a)(),{showNotification:t}=(0,m.l)(),[o,l]=(0,s.useState)([]),[c,h]=(0,s.useState)([]),[g,p]=(0,s.useState)(null),[x,f]=(0,s.useState)(!1),[b,w]=(0,s.useState)({lat:0,lng:0}),[v,j]=(0,s.useState)(!1),[k,y]=(0,s.useState)(!1),[N,L]=(0,s.useState)(""),[C,A]=(0,s.useState)(!1),[D,P]=(0,s.useState)(null),[M,E]=(0,s.useState)(null),[T,I]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!e)return;let t=(async()=>{try{let t=(0,i.JU)(a.db,"users",e.id);return(0,i.cf)(t,e=>{if(e.exists()){let t=e.data();f(t.isOnline||!1),t.currentLocation&&w(t.currentLocation),t.locationAddress&&L(t.locationAddress)}})}catch(e){return console.error("Error getting driver status:",e),()=>{}}})();return()=>{t.then(e=>e())}},[e]),(0,s.useEffect)(()=>{if(!e)return;let t=(0,i.IO)((0,i.hJ)(a.db,"bookings"),(0,i.ar)("status","==","pending")),o=(0,i.cf)(t,e=>{let t=[];e.forEach(e=>{var o,n,s;let r=e.data(),a={id:e.id,...r,scheduledTime:(null===(o=r.scheduledTime)||void 0===o?void 0:o.toDate())||new Date,createdAt:(null===(n=r.createdAt)||void 0===n?void 0:n.toDate())||new Date,updatedAt:(null===(s=r.updatedAt)||void 0===s?void 0:s.toDate())||new Date};t.push(a)}),l(t)}),n=(0,i.IO)((0,i.hJ)(a.db,"bookings"),(0,i.ar)("driverId","==",e.id),(0,i.ar)("status","in",["accepted","inProgress","completed"])),s=(0,i.cf)(n,e=>{let t=[];e.forEach(e=>{var o,n,s;let r=e.data(),a={id:e.id,...r,scheduledTime:(null===(o=r.scheduledTime)||void 0===o?void 0:o.toDate())||new Date,createdAt:(null===(n=r.createdAt)||void 0===n?void 0:n.toDate())||new Date,updatedAt:(null===(s=r.updatedAt)||void 0===s?void 0:s.toDate())||new Date};t.push(a)}),h(t)});return()=>{o(),s()}},[e]);let B=async()=>{if(!e)return;let o=!x,n=o?"online":"offline",s=o?"Going online will make you available to receive ride requests. Continue?":"Going offline will hide you from new ride requests. Continue?";if(window.confirm(s))try{f(o),t("You are now ".concat(n),o?"success":"info"),await (0,i.r7)((0,i.JU)(a.db,"users",e.id),{isOnline:o,lastStatusChange:new Date}),console.log("Driver status changed to ".concat(n))}catch(e){f(!o),console.error("Error updating online status:",e),t("Failed to update status. Please try again.","error")}},S=async()=>{if(e){y(!0);try{if(!navigator.geolocation)throw Error("Geolocation is not supported by your browser");console.log("Getting current position...");let{latitude:o,longitude:n}=(await new Promise((e,t)=>{navigator.geolocation.getCurrentPosition(t=>{console.log("Position obtained:",t.coords),e(t)},e=>{console.error("Geolocation error:",e.message,e.code),t(Error("Geolocation error: ".concat(e.message," (code: ").concat(e.code,")")))},{enableHighAccuracy:!0,timeout:15e3,maximumAge:0})})).coords;console.log("Coordinates: ".concat(o,", ").concat(n));let s={lat:o,lng:n};w(s),g&&["accepted","enRouteToPickup"].includes(g.status)&&console.log("Recalculating route after GPS update...");let r="Current location";try{console.log("Fetching address from coordinates...");let e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(n,",").concat(o,".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A"));if(!e.ok)throw Error("Geocoding API error: ".concat(e.status," ").concat(e.statusText));let t=await e.json();console.log("Geocoding response:",t),t.features&&t.features.length>0&&(r=t.features[0].place_name,console.log("Address found:",r))}catch(e){console.error("Error getting address:",e)}L(r),console.log("Updating database with new location..."),await (0,i.r7)((0,i.JU)(a.db,"users",e.id),{currentLocation:s,locationAddress:r,locationUpdatedAt:new Date}),console.log("Location updated successfully"),t("Location updated successfully","success")}catch(e){console.error("Error updating location:",e),e instanceof Error?e.message.includes("User denied Geolocation")?t("Location access denied. Please enable location services in your browser settings and try again.","error"):e.message.includes("timeout")?t("Location request timed out. Please try again in an area with better GPS signal.","error"):t("Failed to update location: ".concat(e.message),"error"):t("Failed to update location. Please try again.","error")}finally{y(!1)}}},z=async e=>{try{console.log("Getting rider information for ID:",e);let t=await (0,i.QT)((0,i.JU)(a.db,"users",e));if(t.exists()){let e=t.data();console.log("Rider data:",e),I({fullName:e.fullName||"Unknown User",phoneNumber:e.phoneNumber||"No phone number"})}else console.error("Rider document not found"),I({fullName:"Unknown User",phoneNumber:"No phone number"})}catch(e){console.error("Error getting rider information:",e),I({fullName:"Unknown User",phoneNumber:"No phone number"})}},R=async o=>{if(e)try{console.log("Accepting booking:",o);let t=(0,i.JU)(a.db,"bookings",o.id);await (0,i.i3)(a.db,async o=>{let n=await o.get(t);if(!n.exists())throw Error("Booking no longer exists");let s=n.data();if("pending"!==s.status)throw Error("This ride has already been accepted by another driver");o.update(t,{driverId:e.id,status:"inProgress",updatedAt:new Date})});let c={...o,driverId:e.id,status:"inProgress",updatedAt:new Date};console.log("Setting selected booking:",c),p(c),await z(o.riderId);try{let t=await (0,i.QT)((0,i.JU)(a.db,"users",o.riderId));if(t.exists()){var n,s,r,l;let c=t.data().bookingHistory||[];c.includes(o.id)||(await (0,i.r7)((0,i.JU)(a.db,"users",o.riderId),{bookingHistory:[...c,o.id]}),console.log("Added booking to rider history"));let d={fullName:e.fullName,phoneNumber:e.phoneNumber,licensePlate:(null===(n=e.vehicleDetails)||void 0===n?void 0:n.licensePlate)||"N/A",vehicleColor:(null===(s=e.vehicleDetails)||void 0===s?void 0:s.color)||"N/A",vehicleMake:(null===(r=e.vehicleDetails)||void 0===r?void 0:r.make)||"N/A",vehicleModel:(null===(l=e.vehicleDetails)||void 0===l?void 0:l.model)||"N/A"},u="Your ride has been accepted by ".concat(e.fullName,".\n          Your ride is starting!\n          Vehicle: ").concat(d.vehicleColor," ").concat(d.vehicleMake," ").concat(d.vehicleModel,"\n          License Plate: ").concat(d.licensePlate,"\n          Driver Phone: ").concat(d.phoneNumber,"\n          Passengers: ").concat(o.passengers," ").concat(1===o.passengers?"person":"people");await (0,i.ET)((0,i.hJ)(a.db,"notifications"),{userId:o.riderId,message:u,type:"success",read:!1,relatedBookingId:o.id,driverDetails:d,createdAt:new Date}),console.log("Created notification for rider"),G(o)}}catch(e){console.error("Error updating rider booking history:",e)}E(null),console.log("Scheduling direction calculation..."),setTimeout(()=>{console.log("Calculating directions for accepted booking"),H(c)},500)}catch(e){console.error("Error accepting booking:",e),e instanceof Error?t(e.message,"error"):t("Failed to accept booking. Please try again.","error")}},U=e=>new Promise(t=>{"199612"===prompt("Please enter the driver password for ".concat(e,":"))?t(!0):(alert("Incorrect password. ".concat(e," not authorized. Please contact your administrator if you need assistance.")),t(!1))}),W=()=>U("ride cancellation"),O=async o=>{if(e&&await U("booking deletion"))try{let n=await (0,i.QT)((0,i.JU)(a.db,"bookings",o));if(!n.exists()){t("Booking not found","error");return}if(n.data().driverId!==e.id){t("You can only delete your own bookings","error");return}await (0,i.r7)((0,i.JU)(a.db,"bookings",o),{status:"deleted",updatedAt:new Date}),h(e=>e.filter(e=>e.id!==o)),g&&g.id===o&&(p(null),E(null),I(null)),t("Booking has been marked as deleted","success")}catch(e){console.error("Error deleting booking:",e),t("Failed to delete booking","error")}},F=async(o,n)=>{if(e&&("cancelled"!==n||await W()))try{await (0,i.r7)((0,i.JU)(a.db,"bookings",o.id),{status:n,updatedAt:new Date}),j(!1);let e={...o,status:n};if(p(e),"accepted"!==n||(console.log("Booking accepted, getting rider information..."),T||await z(o.riderId)),"inProgress"!==n||(E(null),T||await z(o.riderId)),"completed"===n||"cancelled"===n){if("cancelled"===n)try{await (0,i.ET)((0,i.hJ)(a.db,"notifications"),{userId:o.riderId,message:"Your ride has been cancelled by the driver. Please book a new ride if needed.",type:"warning",read:!1,relatedBookingId:o.id,createdAt:new Date}),t("Ride has been cancelled successfully.","info")}catch(e){console.error("Error creating cancellation notification:",e)}p(null),E(null),I(null)}}catch(e){console.error("Error updating booking status:",e)}},H=e=>{if(console.log("Driver dashboard: calculating directions to pickup location"),console.log("Current location:",b),console.log("Pickup location:",e.pickupLocation),!b||0===b.lat||0===b.lng){console.error("Invalid current location for directions calculation");return}if(!e.pickupLocation||0===e.pickupLocation.lat||0===e.pickupLocation.lng){console.error("Invalid pickup location for directions calculation");return}console.log("Locations valid, MapboxMap component will calculate directions")};(0,s.useEffect)(()=>{g&&["inProgress"].includes(g.status)&&b&&0!==b.lat&&0!==b.lng&&(console.log("Current location changed, recalculating route..."),H(g))},[b,g]),(0,s.useEffect)(()=>{e&&console.log("Automatic GPS location fetch disabled - drivers must update location manually")},[e]);let J=e=>{p(e),j(!1),H(e)},V=e=>e?new Date(e).toLocaleString():"Not scheduled",_=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"accepted":return"bg-blue-100 text-blue-800";case"inProgress":return"bg-green-100 text-green-800";case"completed":default:return"bg-gray-100 text-gray-800";case"cancelled":return"bg-red-100 text-red-800";case"deleted":return"bg-purple-100 text-purple-800"}},G=e=>{t("You've started a ride to ".concat(e.airport.name),"success")};return(0,n.jsx)(u.Z,{title:"BaroRide - Driver Dashboard",children:(0,n.jsxs)("div",{className:"container mx-auto p-2 sm:p-4 max-w-6xl",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-3 sm:space-y-0",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center",children:[(0,n.jsx)("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900",children:"Driver Dashboard"}),(0,n.jsx)("div",{className:"mt-2 sm:mt-0 sm:ml-3 px-3 py-1 rounded-full text-xs sm:text-sm font-medium ".concat(x?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200"),children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-2 h-2 rounded-full mr-1 ".concat(x?"bg-green-500 animate-pulse":"bg-red-500")}),x?"Online":"Offline"]})})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:space-x-2",children:[(0,n.jsx)("button",{onClick:S,disabled:k,className:"flex items-center justify-center px-3 sm:px-4 py-2 rounded-lg bg-green-500 hover:bg-green-600 active:bg-green-700 text-white font-medium shadow-md transition-all duration-200 touch-manipulation text-sm sm:text-base",title:"Update your current location using GPS",style:{touchAction:"manipulation"},children:k?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-1 sm:mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,n.jsx)("span",{className:"hidden sm:inline",children:"Updating..."}),(0,n.jsx)("span",{className:"sm:hidden",children:"Updating"})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("svg",{className:"w-4 h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsx)("span",{className:"hidden sm:inline",children:"Update Location"}),(0,n.jsx)("span",{className:"sm:hidden",children:"Update"})]})}),(0,n.jsxs)("button",{onClick:B,className:"flex items-center justify-center px-4 sm:px-5 py-2 rounded-full shadow-md transition-all duration-300 touch-manipulation text-sm sm:text-base font-medium ".concat(x?"bg-green-500 hover:bg-green-600 active:bg-green-700":"bg-red-500 hover:bg-red-600 active:bg-red-700"," text-white"),style:{touchAction:"manipulation"},children:[(0,n.jsx)("span",{className:"inline-block w-2 sm:w-3 h-2 sm:h-3 rounded-full mr-1 sm:mr-2 ".concat(x?"bg-green-200 animate-pulse":"bg-red-200")}),(0,n.jsx)("span",{className:"hidden sm:inline",children:x?"Online":"Offline"}),(0,n.jsx)("span",{className:"sm:hidden",children:x?"Online":"Offline"})]})]})]}),N&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-100 rounded-lg flex items-center",children:[(0,n.jsxs)("svg",{className:"w-5 h-5 text-blue-500 mr-2 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-blue-700",children:"Your current location"}),(0,n.jsx)("p",{className:"text-sm text-blue-600",children:N})]})]}),g&&"inProgress"===g.status&&M&&(0,n.jsx)("div",{className:"mb-4 p-4 bg-green-100 border border-green-200 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"bg-green-500 rounded-full p-2 mr-3",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-bold text-green-800",children:"Ride in Progress"}),(0,n.jsxs)("p",{className:"text-green-700",children:["Destination: ",g.airport.name]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-sm font-medium text-green-800",children:M.formattedDistance}),(0,n.jsx)("div",{className:"text-sm font-medium text-green-800",children:M.formattedDuration})]})]})}),g&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsxs)("button",{onClick:()=>p(null),className:"flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded shadow-md transition-colors",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Dashboard"]})}),(0,n.jsx)("div",{className:"mb-8 border rounded overflow-hidden p-4 bg-gray-50",children:g?(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(d,{currentLocation:b,pickupLocation:g.pickupLocation,height:"400px",selectable:v,onDirectionsCalculated:e=>{if(console.log("Route calculated:",e),e){let t=(e.distance/1e3).toFixed(2),o=Math.round(e.duration/60);E({distance:e.distance,duration:e.duration,formattedDistance:"".concat(t," km"),formattedDuration:"".concat(o," ").concat(1===o?"minute":"minutes")})}else E(null)},onLocationSelected:e=>{console.log("Location selected:",e),g&&(0,i.r7)((0,i.JU)(a.db,"bookings",g.id),{pickupLocation:{lat:e.lat,lng:e.lng,address:e.address||"Selected location"},updatedAt:new Date}).then(()=>{p({...g,pickupLocation:{lat:e.lat,lng:e.lng,address:e.address||"Selected location"}}),j(!1)}).catch(e=>{console.error("Error updating pickup location:",e)})}}),(0,n.jsx)("div",{className:"absolute top-2 right-2 z-20",children:v?(0,n.jsx)("button",{onClick:()=>j(!1),className:"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded shadow-sm text-sm",children:"Cancel"}):["pending","accepted","enRouteToPickup"].includes(g.status)&&(0,n.jsx)("button",{onClick:()=>j(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded shadow-sm text-sm",children:"Change Pickup Location"})})]}),(0,n.jsxs)("div",{className:"mt-4 p-4 border rounded bg-white",children:[(0,n.jsx)("h3",{className:"font-bold",children:"Selected Booking"}),(0,n.jsxs)("p",{children:["Pickup: ",g.pickupLocation.address]}),(0,n.jsxs)("p",{children:["Airport: ",g.airport.name]}),(0,n.jsxs)("p",{children:["Status: ",g.status]}),T&&["accepted","inProgress"].includes(g.status)&&(0,n.jsxs)("div",{className:"mt-3 p-3 bg-green-50 border border-green-100 rounded",children:[(0,n.jsxs)("h4",{className:"font-medium text-green-800 flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Rider Information"]}),(0,n.jsxs)("div",{className:"mt-2",children:[(0,n.jsxs)("div",{className:"flex items-center mb-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),(0,n.jsx)("p",{className:"text-green-800",children:T.fullName})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),(0,n.jsx)("a",{href:"tel:".concat(T.phoneNumber),className:"text-green-800 underline",children:T.phoneNumber})]}),(0,n.jsxs)("div",{className:"flex items-center mt-2",children:[(0,n.jsx)("svg",{className:"w-4 h-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),(0,n.jsxs)("p",{className:"text-green-800",children:["Passengers: ",g.passengers," ",1===g.passengers?"person":"people"]})]})]})]}),M&&["accepted"].includes(g.status)&&(0,n.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-100 rounded",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("h4",{className:"font-medium text-blue-800 flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"})}),"Route to Pickup"]}),(0,n.jsxs)("button",{onClick:()=>H(g),className:"text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700",title:"Update route based on current location",children:[(0,n.jsx)("svg",{className:"w-3 h-3 inline-block mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Recalculate"]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2 mt-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-xs text-blue-600",children:"Distance"}),(0,n.jsx)("p",{className:"font-medium text-blue-800",children:M.formattedDistance})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-xs text-blue-600",children:"Estimated Time"}),(0,n.jsx)("p",{className:"font-medium text-blue-800",children:M.formattedDuration})]})]})]}),v&&(0,n.jsx)("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm",children:(0,n.jsx)("p",{className:"text-yellow-700",children:"Click on the map to select a new pickup location"})})]})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)(d,{currentLocation:b,height:"400px"}),(0,n.jsx)("p",{className:"text-center text-gray-600 mt-2",children:"Select a booking to see pickup location"})]})}),g&&(0,n.jsxs)("div",{className:"mb-8 bg-white border rounded-lg shadow-sm overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b",children:(0,n.jsx)("h2",{className:"text-lg font-medium",children:"Current Ride"})}),(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Pickup Location"}),(0,n.jsx)("p",{className:"font-medium",children:g.pickupLocation.address})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Destination"}),(0,n.jsx)("p",{className:"font-medium",children:g.airport.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Scheduled Time"}),(0,n.jsx)("p",{className:"font-medium",children:V(g.scheduledTime)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Fare"}),(0,n.jsxs)("p",{className:"font-medium",children:["$",g.fare]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:"Passengers"}),(0,n.jsxs)("p",{className:"font-medium",children:[g.passengers," ",1===g.passengers?"person":"people"]})]})]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2 mt-4",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(_(g.status)),children:g.status})}),(0,n.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2",children:["inProgress"===g.status&&(0,n.jsxs)("button",{onClick:()=>p(null),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back"]}),["accepted"].includes(g.status)&&(0,n.jsxs)("button",{onClick:()=>F(g,"cancelled"),className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),"Cancel Ride"]}),(0,n.jsxs)("button",{onClick:()=>O(g.id),className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Mark and Delete Booking"]})]})]})]}),c.length>0&&!g&&(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"My Bookings"}),(0,n.jsx)("div",{className:"space-y-4",children:c.map(e=>(0,n.jsx)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium",children:e.pickupLocation.address}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["To: ",e.airport.name]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Passengers: ",e.passengers," ",1===e.passengers?"person":"people"]}),(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)("span",{className:"inline-block px-2 py-1 text-xs font-medium rounded-full ".concat(_(e.status)),children:e.status})})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"font-medium",children:["$",e.fare]}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:V(e.scheduledTime)})]})]}),(0,n.jsxs)("div",{className:"mt-4 flex flex-col space-y-2",children:[(0,n.jsx)("button",{onClick:()=>J(e),className:"w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors",children:"View Details"}),(0,n.jsxs)("button",{onClick:()=>O(e.id),className:"w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center justify-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Mark and Delete Booking"]})]})]})},e.id))})]}),x&&o.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Available Bookings"}),(0,n.jsx)("div",{className:"space-y-4",children:o.map(e=>(0,n.jsx)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-medium",children:e.pickupLocation.address}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["To: ",e.airport.name]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Scheduled: ",V(e.scheduledTime)]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["Passengers: ",e.passengers," ",1===e.passengers?"person":"people"]})]}),(0,n.jsx)("div",{className:"text-right",children:(0,n.jsxs)("p",{className:"font-medium",children:["$",e.fare]})})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("button",{onClick:()=>R(e),className:"w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors",children:"Accept Booking"})})]})},e.id))})]}),x&&0===o.length&&0===c.length&&(0,n.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-600",children:"No bookings available at the moment"}),(0,n.jsx)("p",{className:"text-gray-500 mt-2",children:"New booking requests will appear here"})]}),!x&&(0,n.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-600",children:"You are currently offline"}),(0,n.jsx)("p",{className:"text-gray-500 mt-2",children:"Go online to receive booking requests"}),(0,n.jsx)("button",{onClick:B,className:"mt-4 bg-green-500 hover:bg-green-600 active:bg-green-700 text-white px-8 py-3 rounded-full transition-all duration-300 shadow-md font-medium text-lg touch-manipulation",style:{touchAction:"manipulation"},children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("span",{className:"inline-block w-3 h-3 rounded-full mr-2 bg-green-200"}),"Go Online"]})})]})]})})}function p(){return(0,n.jsx)(h.Z,{requiredRoles:["admin","driver"],children:(0,n.jsx)(g,{})})}},571:function(){}},function(e){e.O(0,[634,996,790,994,888,774,179],function(){return e(e.s=5031)}),_N_E=e.O()}]);