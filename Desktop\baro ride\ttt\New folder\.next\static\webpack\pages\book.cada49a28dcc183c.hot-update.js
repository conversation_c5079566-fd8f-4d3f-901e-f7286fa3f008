"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/book",{

/***/ "./src/utils/mobile-optimization.ts":
/*!******************************************!*\
  !*** ./src/utils/mobile-optimization.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   getDeviceInfo: function() { return /* binding */ getDeviceInfo; },\n/* harmony export */   handleMobileKeyboard: function() { return /* binding */ handleMobileKeyboard; },\n/* harmony export */   handleMobileViewport: function() { return /* binding */ handleMobileViewport; },\n/* harmony export */   initializeMobileOptimizations: function() { return /* binding */ initializeMobileOptimizations; },\n/* harmony export */   needsMobileOptimization: function() { return /* binding */ needsMobileOptimization; },\n/* harmony export */   optimizeFormForMobile: function() { return /* binding */ optimizeFormForMobile; },\n/* harmony export */   optimizeMapForMobile: function() { return /* binding */ optimizeMapForMobile; },\n/* harmony export */   optimizeScrolling: function() { return /* binding */ optimizeScrolling; },\n/* harmony export */   optimizeTouchInteraction: function() { return /* binding */ optimizeTouchInteraction; },\n/* harmony export */   preventIOSZoom: function() { return /* binding */ preventIOSZoom; }\n/* harmony export */ });\n// Universal device optimization utilities for BaroRide\n// Works consistently across mobile, tablet, and desktop devices\n// Detect device and browser information\nconst getDeviceInfo = ()=>{\n    if (false) {}\n    const userAgent = navigator.userAgent.toLowerCase();\n    const screenWidth = window.screen.width;\n    const screenHeight = window.screen.height;\n    const pixelRatio = window.devicePixelRatio || 1;\n    // Device detection\n    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;\n    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || screenWidth > 768 && screenWidth <= 1024;\n    const isDesktop = !isMobile && !isTablet;\n    // OS detection\n    const isIOS = /iphone|ipad|ipod/i.test(userAgent);\n    const isAndroid = /android/i.test(userAgent);\n    // Browser detection\n    const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent);\n    const isChrome = /chrome/i.test(userAgent);\n    // Touch support\n    const touchSupport = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n    // Orientation\n    const orientation = screenWidth > screenHeight ? \"landscape\" : \"portrait\";\n    return {\n        isMobile,\n        isTablet,\n        isDesktop,\n        isIOS,\n        isAndroid,\n        isSafari,\n        isChrome,\n        screenWidth,\n        screenHeight,\n        pixelRatio,\n        touchSupport,\n        orientation\n    };\n};\n// Optimize touch interactions\nconst optimizeTouchInteraction = (element)=>{\n    if (!element) return;\n    // Prevent iOS zoom on double tap\n    element.style.touchAction = \"manipulation\";\n    // Remove tap highlight\n    element.style.webkitTapHighlightColor = \"transparent\";\n    // Ensure minimum touch target size (44px)\n    const computedStyle = window.getComputedStyle(element);\n    const minSize = 44;\n    if (parseInt(computedStyle.height) < minSize) {\n        element.style.minHeight = \"\".concat(minSize, \"px\");\n    }\n    if (parseInt(computedStyle.width) < minSize) {\n        element.style.minWidth = \"\".concat(minSize, \"px\");\n    }\n};\n// Prevent iOS zoom on input focus\nconst preventIOSZoom = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isIOS) return;\n    // Set font size to 16px to prevent zoom\n    const inputs = document.querySelectorAll(\"input, select, textarea\");\n    inputs.forEach((input)=>{\n        const element = input;\n        if (element.style.fontSize === \"\" || parseInt(element.style.fontSize) < 16) {\n            element.style.fontSize = \"16px\";\n        }\n    });\n};\n// Handle viewport height issues on mobile\nconst handleMobileViewport = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Set CSS custom property for actual viewport height\n    const setVH = ()=>{\n        const vh = window.innerHeight * 0.01;\n        document.documentElement.style.setProperty(\"--vh\", \"\".concat(vh, \"px\"));\n    };\n    setVH();\n    window.addEventListener(\"resize\", setVH);\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(setVH, 100); // Delay to ensure orientation change is complete\n    });\n};\n// Optimize map interactions for mobile\nconst optimizeMapForMobile = (mapContainer)=>{\n    if (!mapContainer || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (deviceInfo.isMobile) {\n        // Prevent page scroll when interacting with map\n        mapContainer.style.touchAction = \"none\";\n        // Add mobile-specific event listeners\n        let isMapInteracting = false;\n        mapContainer.addEventListener(\"touchstart\", ()=>{\n            isMapInteracting = true;\n            document.body.style.overflow = \"hidden\";\n        }, {\n            passive: true\n        });\n        mapContainer.addEventListener(\"touchend\", ()=>{\n            isMapInteracting = false;\n            document.body.style.overflow = \"\";\n        }, {\n            passive: true\n        });\n        // Handle map container sizing\n        const resizeMap = ()=>{\n            const containerHeight = Math.min(window.innerHeight * 0.4, 400);\n            mapContainer.style.height = \"\".concat(containerHeight, \"px\");\n        };\n        resizeMap();\n        window.addEventListener(\"resize\", resizeMap);\n        window.addEventListener(\"orientationchange\", ()=>{\n            setTimeout(resizeMap, 100);\n        });\n    }\n};\n// Optimize form interactions for mobile\nconst optimizeFormForMobile = (form)=>{\n    if (!form || \"object\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Optimize all inputs in the form\n    const inputs = form.querySelectorAll(\"input, select, textarea, button\");\n    inputs.forEach((input)=>{\n        const element = input;\n        optimizeTouchInteraction(element);\n        // Add mobile-specific attributes\n        if (element.tagName === \"INPUT\") {\n            const inputElement = element;\n            // Prevent autocorrect and autocapitalize for certain input types\n            if (inputElement.type === \"email\" || inputElement.type === \"url\") {\n                inputElement.setAttribute(\"autocorrect\", \"off\");\n                inputElement.setAttribute(\"autocapitalize\", \"none\");\n                inputElement.setAttribute(\"spellcheck\", \"false\");\n            }\n            // Set appropriate input modes\n            if (inputElement.type === \"tel\") {\n                inputElement.setAttribute(\"inputmode\", \"tel\");\n            } else if (inputElement.type === \"email\") {\n                inputElement.setAttribute(\"inputmode\", \"email\");\n            } else if (inputElement.type === \"number\") {\n                inputElement.setAttribute(\"inputmode\", \"numeric\");\n            }\n        }\n    });\n};\n// Handle keyboard visibility on mobile\nconst handleMobileKeyboard = ()=>{\n    if (false) {}\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    let initialViewportHeight = window.innerHeight;\n    const handleResize = ()=>{\n        const currentHeight = window.innerHeight;\n        const heightDifference = initialViewportHeight - currentHeight;\n        // If height decreased significantly, keyboard is likely open\n        if (heightDifference > 150) {\n            document.body.classList.add(\"keyboard-open\");\n            // Scroll active input into view\n            const activeElement = document.activeElement;\n            if (activeElement && (activeElement.tagName === \"INPUT\" || activeElement.tagName === \"TEXTAREA\")) {\n                setTimeout(()=>{\n                    activeElement.scrollIntoView({\n                        behavior: \"smooth\",\n                        block: \"center\"\n                    });\n                }, 100);\n            }\n        } else {\n            document.body.classList.remove(\"keyboard-open\");\n        }\n    };\n    window.addEventListener(\"resize\", handleResize);\n    // Reset on orientation change\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(()=>{\n            initialViewportHeight = window.innerHeight;\n        }, 500);\n    });\n};\n// Optimize scrolling performance\nconst optimizeScrolling = ()=>{\n    if (false) {}\n    // Enable smooth scrolling\n    document.documentElement.style.scrollBehavior = \"smooth\";\n    // Enable smooth scrolling on iOS\n    document.documentElement.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Add momentum scrolling for iOS\n    document.body.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Optimize scroll containers\n    const scrollContainers = document.querySelectorAll(\".overflow-auto, .overflow-y-auto, .overflow-x-auto\");\n    scrollContainers.forEach((container)=>{\n        const element = container;\n        element.style[\"webkitOverflowScrolling\"] = \"touch\";\n    });\n};\n// Initialize all mobile optimizations\nconst initializeMobileOptimizations = ()=>{\n    if (false) {}\n    // Wait for DOM to be ready\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>{\n            initializeMobileOptimizations();\n        });\n        return;\n    }\n    const deviceInfo = getDeviceInfo();\n    // Add device classes to body (only in browser environment)\n    if (typeof document !== \"undefined\" && document.body) {\n        const classesToAdd = [\n            deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\",\n            deviceInfo.isTablet ? \"is-tablet\" : \"\",\n            deviceInfo.isIOS ? \"is-ios\" : \"\",\n            deviceInfo.isAndroid ? \"is-android\" : \"\",\n            deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\"\n        ].filter(Boolean); // Remove empty strings\n        document.body.classList.add(...classesToAdd);\n    }\n    // Apply optimizations\n    handleMobileViewport();\n    preventIOSZoom();\n    handleMobileKeyboard();\n    optimizeScrolling();\n    // Optimize existing forms (only in browser environment)\n    if (typeof document !== \"undefined\") {\n        const forms = document.querySelectorAll(\"form\");\n        forms.forEach(optimizeFormForMobile);\n        // Optimize existing maps\n        const mapContainers = document.querySelectorAll('.map-container, [id*=\"map\"]');\n        mapContainers.forEach((container)=>optimizeMapForMobile(container));\n    }\n    console.log(\"Mobile optimizations initialized for:\", deviceInfo);\n};\n// Utility to check if device needs mobile optimizations\nconst needsMobileOptimization = ()=>{\n    const deviceInfo = getDeviceInfo();\n    return deviceInfo.isMobile || deviceInfo.isTablet;\n};\n// Export device info for use in components\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/mobile-optimization.ts\n"));

/***/ })

});