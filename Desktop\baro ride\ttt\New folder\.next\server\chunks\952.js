exports.id=952,exports.ids=[952],exports.modules={6317:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{H:()=>u,a:()=>h});var s=r(997),l=r(6689),i=r(3462),n=r(401),o=r(1492),d=r(1163),c=e([i,n,o]);[i,n,o]=c.then?(await c)():c;let m=(0,l.createContext)({user:null,loading:!0,signOut:async()=>{}});function u({children:e}){let[t,r]=(0,l.useState)(null),[a,o]=(0,l.useState)(!0);(0,d.useRouter)();let c=async()=>{await (0,n.signOut)(i.auth),r(null)};return s.jsx(m.Provider,{value:{user:t,loading:a,signOut:c},children:e})}let h=()=>(0,l.useContext)(m);a()}catch(e){a(e)}})},1530:(e,t,r)=>{"use strict";r.d(t,{J:()=>n,l:()=>o});var a=r(997),s=r(6689);function l({message:e,type:t="info",duration:r=5e3,onClose:l,driverDetails:i}){let[n,o]=(0,s.useState)(!0);return n?a.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-md shadow-lg",children:(0,a.jsxs)("div",{className:`p-4 mb-4 text-sm rounded-lg border ${(()=>{switch(t){case"success":return"bg-green-100 border-green-500 text-green-700";case"warning":return"bg-yellow-100 border-yellow-500 text-yellow-700";case"error":return"bg-red-100 border-red-500 text-red-700";default:return"bg-blue-100 border-blue-500 text-blue-700"}})()}`,role:"alert",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"mr-2",children:(()=>{switch(t){case"success":return a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"warning":return a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"error":return a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}})()}),a.jsx("div",{className:"font-medium flex-grow whitespace-pre-line",children:e}),(0,a.jsxs)("button",{type:"button",className:"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500",onClick:()=>{o(!1),l&&l()},"aria-label":"Close",children:[a.jsx("span",{className:"sr-only",children:"Close"}),a.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),i&&a.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3",children:a.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-semibold",children:i.fullName}),(0,a.jsxs)("p",{className:"text-xs",children:[i.vehicleColor," ",i.vehicleMake," ",i.vehicleModel]}),(0,a.jsxs)("p",{className:"text-xs font-medium",children:["License Plate: ",i.licensePlate]}),(0,a.jsxs)("p",{className:"text-xs",children:["Phone: ",i.phoneNumber]})]})]})})]})}):null}let i=(0,s.createContext)({showNotification:()=>{}});function n({children:e}){let[t,r]=(0,s.useState)([]),n=e=>{r(t=>t.filter(t=>t.id!==e))};return(0,a.jsxs)(i.Provider,{value:{showNotification:(e,t="info",a=5e3,s)=>{let l=Date.now().toString();r(r=>[...r,{id:l,message:e,type:t,duration:a,driverDetails:s}])}},children:[e,t.map(e=>a.jsx(l,{message:e.message,type:e.type,duration:e.duration,driverDetails:e.driverDetails,onClose:()=>n(e.id)},e.id))]})}let o=()=>(0,s.useContext)(i)},2942:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{r:()=>c,s:()=>h});var s=r(997),l=r(6689),i=r(6317),n=r(1163),o=r(1530),d=e([i]);i=(d.then?(await d)():d)[0];let u=(0,l.createContext)({hasAccess:()=>!1,checkAccess:()=>!1,userRole:"guest",isAdmin:!1,isDriver:!1,isRider:!1}),m={"/":["admin","driver","rider","guest"],"/login":["admin","driver","rider","guest"],"/signup":["admin","driver","rider","guest"],"/forgot-password":["admin","driver","rider","guest"],"/book":["admin","rider"],"/driver":["admin","driver"],"/driver/dashboard":["admin","driver"],"/admin":["admin"],"/admin/dashboard":["admin"],"/admin/users":["admin"],"/admin/bookings":["admin"]};function c({children:e}){let{user:t,loading:r}=(0,i.a)();(0,n.useRouter)();let{showNotification:a}=(0,o.l)(),[d,c]=(0,l.useState)(!1),h=t?"admin"===t.role?"admin":"driver"===t.role?"driver":"rider":"guest",x="admin"===h,p="driver"===h,w="rider"===h;return s.jsx(u.Provider,{value:{hasAccess:e=>{let t=m[e];return t?t.includes(h):"admin"===h},checkAccess:e=>e.includes(h),userRole:h,isAdmin:x,isDriver:p,isRider:w},children:d?e:null})}let h=()=>(0,l.useContext)(u);a()}catch(e){a(e)}})},3462:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{auth:()=>c,db:()=>u});var s=r(3745),l=r(401),i=r(1492),n=r(3392),o=e([s,l,i,n]);[s,l,i,n]=o.then?(await o)():o;let d=(0,s.initializeApp)({apiKey:"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po",authDomain:"baroride.firebaseapp.com",projectId:"baroride",storageBucket:"baroride.firebasestorage.app",messagingSenderId:"191771619835",appId:"1:191771619835:web:2fc57d131cf64a35e2db5e"}),c=(0,l.getAuth)(d),u=(0,i.getFirestore)(d);(0,n.getStorage)(d),a()}catch(e){a(e)}})},3893:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>d});var s=r(997);r(108);var l=r(6317),i=r(1530),n=r(2942),o=e([l,n]);[l,n]=o.then?(await o)():o;let d=function({Component:e,pageProps:t}){return s.jsx(l.H,{children:s.jsx(i.J,{children:s.jsx(n.r,{children:s.jsx(e,{...t})})})})};a()}catch(e){a(e)}})},1070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(997),s=r(6859);function l(){return(0,a.jsxs)(s.Html,{lang:"en",children:[(0,a.jsxs)(s.Head,{children:[a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"}),a.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),a.jsx("meta",{name:"apple-mobile-web-app-title",content:"BaroRide"}),a.jsx("meta",{name:"format-detection",content:"telephone=no"}),a.jsx("meta",{name:"theme-color",content:"#1e3a5f"}),a.jsx("meta",{name:"msapplication-navbutton-color",content:"#1e3a5f"}),a.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,a.jsxs)("body",{className:"antialiased",children:[a.jsx(s.Main,{}),a.jsx(s.NextScript,{})]})]})}},6133:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{So:()=>m,XL:()=>d,xT:()=>c});var s=r(3462),l=r(401),i=r(1492),n=e([s,l,i]);[s,l,i]=n.then?(await n)():n;let o=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),d=e=>{let t=e.replace(/[^\d+]/g,"");return t=t.startsWith("+")?"+"+t.substring(1).replace(/\+/g,""):t.replace(/\+/g,"")},c=e=>{let t=d(e);return/^(\+\d{1,3})?\d{7,15}$/.test(t)||[/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,/^[+]?[0-9]{1,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}$/,/^[0-9]{10,15}$/,/^\+[0-9]{7,15}$/].some(t=>t.test(e))},u=async e=>{try{let t=(0,i.collection)(s.db,"users"),r=(0,i.query)(t,(0,i.where)("phoneNumber","==",e)),a=await (0,i.getDocs)(r);if(!a.empty){let e=a.docs[0];return{id:e.id,...e.data()}}let l=d(e),n=(0,i.query)(t);for(let e of(await (0,i.getDocs)(n)).docs){let t=e.data();if(t.phoneNumber&&d(t.phoneNumber)===l)return{id:e.id,...t}}return null}catch(e){return console.error("Error finding user by phone number:",e),null}},m=async(e,t)=>{try{let r,a;if(o(e)){r=await (0,l.signInWithEmailAndPassword)(s.auth,e,t);let n=await (0,i.getDoc)((0,i.doc)(s.db,"users",r.user.uid));a={id:n.id,...n.data()}}else if(c(e)){let i=await u(e);if(!i)throw Error("User not found with this phone number");r=await (0,l.signInWithEmailAndPassword)(s.auth,i.email,t),a=i}else throw Error("Invalid identifier format. Please enter a valid email or phone number.");return a}catch(e){throw console.error("Authentication error:",e),e}};a()}catch(e){a(e)}})},108:()=>{}};