(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{8312:function(e,o,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return a(7246)}])},7246:function(e,o,a){"use strict";a.r(o);var i=a(5893),t=a(1664),s=a.n(t),n=a(837),l=a(2151),r=a(6492),d=a(7294),c=a(1163);o.default=()=>{let{user:e}=(0,n.a)(),{showNotification:o}=(0,r.l)(),a=(0,c.useRouter)(),[t,h]=(0,d.useState)(!1);(0,d.useEffect)(()=>{e&&o("Welcome back, ".concat(e.fullName,"!"),"info",3e3)},[e,o]);let x=()=>{h(!0),o("Opening Driver Dashboard...","info",2e3),window.location.href="/driver/dashboard",a.push("/driver/dashboard")},m=()=>{h(!0),o("Opening Book a Ride...","info",2e3),window.location.href="/book",a.push("/book")};return(0,i.jsx)(l.Z,{title:"BaroRide - Book Your Ride",children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("div",{className:"flex justify-center mb-6",children:(0,i.jsx)("img",{src:"/logo.svg",alt:"BaroRide Logo",className:"h-32 w-auto sm:h-40 md:h-48"})}),(0,i.jsx)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4",children:"Welcome to BaroRide"}),(0,i.jsx)("p",{className:"text-xl sm:text-2xl text-gray-600 mb-8",children:"Your reliable ride-sharing service in Gambela, Ethiopia"})]}),e&&(0,i.jsx)("div",{className:"text-center mb-6",children:"driver"===e.role?(0,i.jsx)("button",{onClick:x,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation",style:{touchAction:"manipulation"},children:"Open Driver Dashboard"}):(0,i.jsx)("button",{onClick:m,className:"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation",style:{touchAction:"manipulation"},children:"Book a Ride Now"})}),(0,i.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-8 mb-8",children:[(0,i.jsx)("p",{className:"text-lg text-gray-700 mb-6 text-center",children:"Book your ride with fixed fares and professional drivers."}),e?(0,i.jsx)("div",{className:"text-center",children:"rider"===e.role?(0,i.jsx)("button",{className:"bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ".concat(t?"opacity-75 cursor-wait":""),onClick:m,disabled:t,style:{touchAction:"manipulation"},children:t?"Opening Booking...":"Book a Ride"}):(0,i.jsx)("button",{className:"bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ".concat(t?"opacity-75 cursor-wait":""),onClick:x,disabled:t,style:{touchAction:"manipulation"},children:t?"Opening Dashboard...":"Go to Driver Dashboard"})}):(0,i.jsxs)("div",{className:"text-center space-x-4",children:[(0,i.jsx)(s(),{href:"/login",className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-md hover:bg-gray-300 inline-block",children:"Login"}),(0,i.jsx)(s(),{href:"/signup",className:"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 inline-block",children:"Sign Up"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Fixed Fares"}),(0,i.jsx)("p",{className:"text-gray-700",children:"Know exactly what you'll pay before booking your ride."})]}),(0,i.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Professional Drivers"}),(0,i.jsx)("p",{className:"text-gray-700",children:"All our drivers are vetted and professionally trained."})]}),(0,i.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Easy Booking"}),(0,i.jsx)("p",{className:"text-gray-700",children:"Book your ride in just a few clicks, anytime, anywhere."})]})]})]})})})}}},function(e){e.O(0,[996,151,888,774,179],function(){return e(e.s=8312)}),_N_E=e.O()}]);