import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/firebase/config';
import { collection, query, where, onSnapshot, updateDoc, doc, getDoc, addDoc, runTransaction } from 'firebase/firestore';
import type { Booking } from '@/types/booking';
import type { Driver } from '@/types/user';
import MapboxMap from '@/components/MapboxMap';
import Layout from '@/components/Layout';
import { useNotification } from '@/contexts/NotificationContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useRBAC } from '@/contexts/RBACContext';

// Helper function to calculate distance between two points in meters (Haversine formula)
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

function DriverDashboard() {
  const { user } = useAuth();
  const { showNotification } = useNotification();

  // Booking states
  const [pendingBookings, setPendingBookings] = useState<Booking[]>([]);
  const [myBookings, setMyBookings] = useState<Booking[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);

  // Driver states
  const [isOnline, setIsOnline] = useState(false);
  const [currentLocation, setCurrentLocation] = useState({ lat: 0, lng: 0 });
  const [isSelectingLocation, setIsSelectingLocation] = useState(false);
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);
  const [locationAddress, setLocationAddress] = useState('');
  const [isTrackingEnabled, setIsTrackingEnabled] = useState(false);
  const [trackingWatchId, setTrackingWatchId] = useState<number | null>(null);

  // Route states
  const [routeInfo, setRouteInfo] = useState<{
    distance: number;
    duration: number;
    formattedDistance: string;
    formattedDuration: string;
  } | null>(null);

  // Rider information
  const [riderInfo, setRiderInfo] = useState<{
    fullName: string;
    phoneNumber: string;
  } | null>(null);

  // Load driver status when component mounts
  useEffect(() => {
    if (!user) return;

    // Get driver's online status and location
    const getDriverStatus = async () => {
      try {
        const driverDoc = doc(db, 'users', user.id);
        const unsubscribe = onSnapshot(driverDoc, (docSnapshot) => {
          if (docSnapshot.exists()) {
            const driverData = docSnapshot.data();
            setIsOnline(driverData.isOnline || false);

            // Get the stored location from the database
            if (driverData.currentLocation) {
              setCurrentLocation(driverData.currentLocation);
            }

            if (driverData.locationAddress) {
              setLocationAddress(driverData.locationAddress);
            }
          }
        });
        return unsubscribe;
      } catch (error) {
        console.error('Error getting driver status:', error);
        return () => {};
      }
    };

    const unsubscribeDriverStatus = getDriverStatus();

    // No automatic GPS watching - removed to prevent automatic updates

    return () => {
      unsubscribeDriverStatus.then(unsubscribe => unsubscribe());
    };
  }, [user]);

  // Listen for bookings
  useEffect(() => {
    if (!user) return;

    // Listen for pending bookings
    const pendingQuery = query(
      collection(db, 'bookings'),
      where('status', '==', 'pending')
    );

    const unsubscribePending = onSnapshot(pendingQuery, (snapshot) => {
      const bookings: Booking[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        // Convert Firestore timestamps to Date objects
        const booking = {
          id: doc.id,
          ...data,
          scheduledTime: data.scheduledTime?.toDate() || new Date(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Booking;
        bookings.push(booking);
      });
      setPendingBookings(bookings);
    });

    // Listen for bookings assigned to this driver
    const myBookingsQuery = query(
      collection(db, 'bookings'),
      where('driverId', '==', user.id),
      where('status', 'in', ['accepted', 'inProgress', 'completed'])
    );

    const unsubscribeMyBookings = onSnapshot(myBookingsQuery, (snapshot) => {
      const bookings: Booking[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        // Convert Firestore timestamps to Date objects
        const booking = {
          id: doc.id,
          ...data,
          scheduledTime: data.scheduledTime?.toDate() || new Date(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Booking;
        bookings.push(booking);
      });
      setMyBookings(bookings);
    });

    return () => {
      unsubscribePending();
      unsubscribeMyBookings();
    };
  }, [user]);

  // Toggle driver's online status
  const toggleOnlineStatus = async () => {
    if (!user) return;

    // Get confirmation from user
    const newStatus = !isOnline;
    const statusText = newStatus ? 'online' : 'offline';
    const confirmMessage = newStatus
      ? 'Going online will make you available to receive ride requests. Continue?'
      : 'Going offline will hide you from new ride requests. Continue?';

    if (window.confirm(confirmMessage)) {
      try {
        // Update local state first for immediate feedback
        setIsOnline(newStatus);

        // Show notification
        showNotification(`You are now ${statusText}`, newStatus ? 'success' : 'info');

        // Update in database
        await updateDoc(doc(db, 'users', user.id), {
          isOnline: newStatus,
          lastStatusChange: new Date()
        });

        console.log(`Driver status changed to ${statusText}`);
      } catch (error) {
        // Revert local state if update fails
        setIsOnline(!newStatus);
        console.error('Error updating online status:', error);
        showNotification('Failed to update status. Please try again.', 'error');
      }
    }
  };

  // Update driver's location manually
  const updateDriverLocation = async () => {
    if (!user) return;

    setIsUpdatingLocation(true);

    try {
      // Check if geolocation is available
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by your browser');
      }

      console.log('Getting current position...');

      // Get current position with high accuracy
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (pos) => {
            console.log('Position obtained:', pos.coords);
            resolve(pos);
          },
          (err) => {
            console.error('Geolocation error:', err.message, err.code);
            reject(new Error(`Geolocation error: ${err.message} (code: ${err.code})`));
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 0
          }
        );
      });

      const { latitude, longitude } = position.coords;
      console.log(`Coordinates: ${latitude}, ${longitude}`);

      // Create location object first to ensure we at least have coordinates
      const newLocation = {
        lat: latitude,
        lng: longitude
      };

      // Update state with new coordinates immediately
      setCurrentLocation(newLocation);

      // Recalculate route if a booking is selected and in progress
      if (selectedBooking && ['accepted', 'enRouteToPickup'].includes(selectedBooking.status)) {
        console.log('Recalculating route after GPS update...');
        // We'll let the useEffect handle this when currentLocation changes
      }

      let address = 'Current location';

      try {
        console.log('Fetching address from coordinates...');
        // Get address from coordinates using Mapbox Geocoding API
        const response = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A`
        );

        if (!response.ok) {
          throw new Error(`Geocoding API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Geocoding response:', data);

        if (data.features && data.features.length > 0) {
          address = data.features[0].place_name;
          console.log('Address found:', address);
        }
      } catch (geocodingError) {
        console.error('Error getting address:', geocodingError);
        // Continue with default address if geocoding fails
      }

      // Update state with address
      setLocationAddress(address);

      console.log('Updating database with new location...');
      // Update location in database
      await updateDoc(doc(db, 'users', user.id), {
        currentLocation: newLocation,
        locationAddress: address,
        locationUpdatedAt: new Date()
      });

      console.log('Location updated successfully');
      // Show success message
      showNotification('Location updated successfully', 'success');
    } catch (error) {
      console.error('Error updating location:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('User denied Geolocation')) {
          showNotification('Location access denied. Please enable location services in your browser settings and try again.', 'error');
        } else if (error.message.includes('timeout')) {
          showNotification('Location request timed out. Please try again in an area with better GPS signal.', 'error');
        } else {
          showNotification(`Failed to update location: ${error.message}`, 'error');
        }
      } else {
        showNotification('Failed to update location. Please try again.', 'error');
      }
    } finally {
      setIsUpdatingLocation(false);
    }
  };

  // Get rider information
  const getRiderInfo = async (riderId: string) => {
    try {
      console.log('Getting rider information for ID:', riderId);
      const riderDoc = await getDoc(doc(db, 'users', riderId));

      if (riderDoc.exists()) {
        const riderData = riderDoc.data();
        console.log('Rider data:', riderData);

        setRiderInfo({
          fullName: riderData.fullName || 'Unknown User',
          phoneNumber: riderData.phoneNumber || 'No phone number'
        });
      } else {
        console.error('Rider document not found');
        setRiderInfo({
          fullName: 'Unknown User',
          phoneNumber: 'No phone number'
        });
      }
    } catch (error) {
      console.error('Error getting rider information:', error);
      setRiderInfo({
        fullName: 'Unknown User',
        phoneNumber: 'No phone number'
      });
    }
  };

  // Accept a booking
  const acceptBooking = async (booking: Booking) => {
    if (!user) return;
    try {
      console.log('Accepting booking:', booking);

      // Use a transaction to ensure atomicity
      const bookingRef = doc(db, 'bookings', booking.id);
      await runTransaction(db, async (transaction) => {
        const bookingDoc = await transaction.get(bookingRef);
        if (!bookingDoc.exists()) {
          throw new Error('Booking no longer exists');
        }

        const bookingData = bookingDoc.data();
        if (bookingData.status !== 'pending') {
          throw new Error('This ride has already been accepted by another driver');
        }

        // Update booking in database - set status to 'inProgress'
        transaction.update(bookingRef, {
          driverId: user.id,
          status: 'inProgress',
          updatedAt: new Date(),
        });
      });

      // Update local state
      const acceptedBooking = {
        ...booking,
        driverId: user.id,
        status: 'inProgress' as Booking['status'],
        updatedAt: new Date()
      };

      console.log('Setting selected booking:', acceptedBooking);
      setSelectedBooking(acceptedBooking);

      // Get rider information
      await getRiderInfo(booking.riderId);

      // Add booking to rider's booking history
      try {
        // Get the rider's document
        const riderDoc = await getDoc(doc(db, 'users', booking.riderId));

        if (riderDoc.exists()) {
          const riderData = riderDoc.data();

          // Update the rider's booking history
          const bookingHistory = riderData.bookingHistory || [];

          // Only add the booking ID if it's not already in the history
          if (!bookingHistory.includes(booking.id)) {
            await updateDoc(doc(db, 'users', booking.riderId), {
              bookingHistory: [...bookingHistory, booking.id]
            });
            console.log('Added booking to rider history');
          }

          // Create a notification for the rider with driver details
          // Cast user to Driver type to access vehicleDetails
          const driver = user as unknown as Driver;

          const driverDetails = {
            fullName: driver.fullName,
            phoneNumber: driver.phoneNumber,
            licensePlate: driver.vehicleDetails?.licensePlate || 'N/A',
            vehicleColor: driver.vehicleDetails?.color || 'N/A',
            vehicleMake: driver.vehicleDetails?.make || 'N/A',
            vehicleModel: driver.vehicleDetails?.model || 'N/A',
          };

          // Create a detailed message for the rider
          const detailedMessage = `Your ride has been accepted by ${user.fullName}.
          Your ride is starting!
          Vehicle: ${driverDetails.vehicleColor} ${driverDetails.vehicleMake} ${driverDetails.vehicleModel}
          License Plate: ${driverDetails.licensePlate}
          Driver Phone: ${driverDetails.phoneNumber}
          Passengers: ${booking.passengers} ${booking.passengers === 1 ? 'person' : 'people'}`;

          await addDoc(collection(db, 'notifications'), {
            userId: booking.riderId,
            message: detailedMessage,
            type: 'success',
            read: false,
            relatedBookingId: booking.id,
            driverDetails: driverDetails, // Include driver details in the notification
            createdAt: new Date()
          });
          console.log('Created notification for rider');

          // Show a notification to the driver
          notifyBookingAccepted(booking);
        }
      } catch (error) {
        console.error('Error updating rider booking history:', error);
      }

      // Clear any existing route info
      setRouteInfo(null);

      // Calculate directions to pickup location with a small delay
      // to ensure the booking is set in state first
      console.log('Scheduling direction calculation...');
      setTimeout(() => {
        console.log('Calculating directions for accepted booking');
        calculateDirections(acceptedBooking);
      }, 500);
    } catch (error) {
      console.error('Error accepting booking:', error);
      // Show error notification to the driver
      if (error instanceof Error) {
        showNotification(error.message, 'error');
      } else {
        showNotification('Failed to accept booking. Please try again.', 'error');
      }
    }
  };

  // Driver action password
  const DRIVER_PASSWORD = '199612';

  // Prompt for driver password
  const promptForDriverPassword = (action: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const enteredPassword = prompt(`Please enter the driver password for ${action}:`);
      if (enteredPassword === DRIVER_PASSWORD) {
        resolve(true);
      } else {
        alert(`Incorrect password. ${action} not authorized. Please contact your administrator if you need assistance.`);
        resolve(false);
      }
    });
  };

  // Prompt for cancellation password (for backward compatibility)
  const promptForCancellationPassword = (): Promise<boolean> => {
    return promptForDriverPassword('ride cancellation');
  };

  // Delete booking history
  const deleteBookingHistory = async (bookingId: string) => {
    if (!user) return;

    // Require password for deletion
    const passwordCorrect = await promptForDriverPassword('booking deletion');
    if (!passwordCorrect) {
      return; // Exit if password is incorrect
    }

    try {
      // First, get the booking to check if it belongs to this driver
      const bookingDoc = await getDoc(doc(db, 'bookings', bookingId));

      if (!bookingDoc.exists()) {
        showNotification('Booking not found', 'error');
        return;
      }

      const bookingData = bookingDoc.data();

      // Verify this booking belongs to the current driver
      if (bookingData.driverId !== user.id) {
        showNotification('You can only delete your own bookings', 'error');
        return;
      }

      // Delete the booking
      await updateDoc(doc(db, 'bookings', bookingId), {
        status: 'deleted',
        updatedAt: new Date(),
      });

      // Remove from local state
      setMyBookings(prevBookings => prevBookings.filter(b => b.id !== bookingId));

      // If this was the selected booking, clear it
      if (selectedBooking && selectedBooking.id === bookingId) {
        setSelectedBooking(null);
        setRouteInfo(null);
        setRiderInfo(null);
      }

      showNotification('Booking has been marked as deleted', 'success');
    } catch (error) {
      console.error('Error deleting booking:', error);
      showNotification('Failed to delete booking', 'error');
    }
  };

  // Update booking status
  const updateBookingStatus = async (booking: Booking, status: Booking['status']) => {
    if (!user) return;

    // If trying to cancel, require password
    if (status === 'cancelled') {
      const passwordCorrect = await promptForCancellationPassword();
      if (!passwordCorrect) {
        return; // Exit if password is incorrect
      }
    }

    try {
      await updateDoc(doc(db, 'bookings', booking.id), {
        status,
        updatedAt: new Date(),
      });

      // Reset location selection mode
      setIsSelectingLocation(false);

      // Update the booking in state
      const updatedBooking = { ...booking, status };
      setSelectedBooking(updatedBooking);

      // Make sure we have rider information for accepted bookings
      if (status === 'accepted') {
        console.log('Booking accepted, getting rider information...');

        // Make sure we have rider information
        if (!riderInfo) {
          await getRiderInfo(booking.riderId);
        }
      }

      // We've removed the 'arrived' status, so we'll handle 'inProgress' here
      if (status === 'inProgress') {
        setRouteInfo(null);

        // Make sure we have rider information
        if (!riderInfo) {
          await getRiderInfo(booking.riderId);
        }
      }

      // If status is 'completed' or 'cancelled', clear selected booking and rider info
      if (status === 'completed' || status === 'cancelled') {
        // If cancelling, notify the rider
        if (status === 'cancelled') {
          try {
            // Create a notification for the rider
            await addDoc(collection(db, 'notifications'), {
              userId: booking.riderId,
              message: `Your ride has been cancelled by the driver. Please book a new ride if needed.`,
              type: 'warning',
              read: false,
              relatedBookingId: booking.id,
              createdAt: new Date()
            });

            // Show notification to the driver
            showNotification('Ride has been cancelled successfully.', 'info');
          } catch (error) {
            console.error('Error creating cancellation notification:', error);
          }
        }

        setSelectedBooking(null);
        setRouteInfo(null);
        setRiderInfo(null);
      }
    } catch (error) {
      console.error('Error updating booking status:', error);
    }
  };

  // Calculate directions between current location and pickup location
  const calculateDirections = (booking: Booking) => {
    console.log('Driver dashboard: calculating directions to pickup location');
    console.log('Current location:', currentLocation);
    console.log('Pickup location:', booking.pickupLocation);

    // Validate locations
    if (!currentLocation || currentLocation.lat === 0 || currentLocation.lng === 0) {
      console.error('Invalid current location for directions calculation');
      return;
    }

    if (!booking.pickupLocation || booking.pickupLocation.lat === 0 || booking.pickupLocation.lng === 0) {
      console.error('Invalid pickup location for directions calculation');
      return;
    }

    // The actual calculation is handled by the MapboxMap component
    // This function is just for logging and validation
    console.log('Locations valid, MapboxMap component will calculate directions');
  };

  // Recalculate route when current location changes
  useEffect(() => {
    if (selectedBooking && ['inProgress'].includes(selectedBooking.status)) {
      if (currentLocation && currentLocation.lat !== 0 && currentLocation.lng !== 0) {
        console.log('Current location changed, recalculating route...');
        calculateDirections(selectedBooking);
      }
    }
  }, [currentLocation, selectedBooking]);

  // Initial location setup - no automatic GPS fetch
  useEffect(() => {
    if (!user) return;

    // We've removed the automatic initial GPS location fetch
    // Drivers will need to click the "Update GPS" button to update their location
    console.log('Automatic GPS location fetch disabled - drivers must update location manually');

    // No notification shown to drivers about manual updates

  }, [user]);

  // Handle directions calculated by MapboxMap
  const handleDirectionsCalculated = (route: any) => {
    console.log('Route calculated:', route);

    if (route) {
      // Format distance and duration
      const distanceInKm = (route.distance / 1000).toFixed(2);
      const durationInMinutes = Math.round(route.duration / 60);

      // Store route information
      setRouteInfo({
        distance: route.distance,
        duration: route.duration,
        formattedDistance: `${distanceInKm} km`,
        formattedDuration: `${durationInMinutes} ${durationInMinutes === 1 ? 'minute' : 'minutes'}`
      });
    } else {
      setRouteInfo(null);
    }
  };

  // Handle location selection from map
  const handleLocationSelected = (location: { lat: number; lng: number; address?: string }) => {
    console.log('Location selected:', location);

    if (selectedBooking) {
      // Update the booking with the new pickup location
      updateDoc(doc(db, 'bookings', selectedBooking.id), {
        pickupLocation: {
          lat: location.lat,
          lng: location.lng,
          address: location.address || 'Selected location'
        },
        updatedAt: new Date()
      }).then(() => {
        // Update local state to reflect the change immediately
        setSelectedBooking({
          ...selectedBooking,
          pickupLocation: {
            lat: location.lat,
            lng: location.lng,
            address: location.address || 'Selected location'
          }
        });

        // Exit selection mode
        setIsSelectingLocation(false);
      }).catch(error => {
        console.error('Error updating pickup location:', error);
      });
    }
  };

  // Select a booking to view details
  const selectBooking = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsSelectingLocation(false); // Reset selection mode when changing bookings
    calculateDirections(booking);
  };

  // Helper function to format date
  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Not scheduled';
    return new Date(date).toLocaleString();
  };

  // Get status badge color
  const getStatusColor = (status: Booking['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'inProgress': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'deleted': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Show notification when a booking is accepted
  const notifyBookingAccepted = (booking: Booking) => {
    showNotification(`You've started a ride to ${booking.airport.name}`, 'success');
  };

  return (
    <Layout title="BaroRide - Driver Dashboard">
      <div className="container mx-auto p-2 sm:p-4 max-w-6xl">

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-3 sm:space-y-0">
        <div className="flex flex-col sm:flex-row sm:items-center">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">Driver Dashboard</h1>
          <div className={`mt-2 sm:mt-0 sm:ml-3 px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${
            isOnline
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center">
              <span className={`inline-block w-2 h-2 rounded-full mr-1 ${
                isOnline ? 'bg-green-500 animate-pulse' : 'bg-red-500'
              }`}></span>
              {isOnline ? 'Online' : 'Offline'}
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2">
          <button
            onClick={updateDriverLocation}
            disabled={isUpdatingLocation}
            className="flex items-center justify-center px-3 sm:px-4 py-2 rounded-lg bg-green-500 hover:bg-green-600 active:bg-green-700 text-white font-medium shadow-md transition-all duration-200 touch-manipulation text-sm sm:text-base"
            title="Update your current location using GPS"
            style={{ touchAction: 'manipulation' }}
          >
            {isUpdatingLocation ? (
              <>
                <svg className="animate-spin -ml-1 mr-1 sm:mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="hidden sm:inline">Updating...</span>
                <span className="sm:hidden">📍</span>
              </>
            ) : (
              <>
                📍 <span className="hidden sm:inline ml-1">Update Location</span>
              </>
            )}
          </button>

          {/* GPS Tracking button removed as requested */}
          <button
            onClick={toggleOnlineStatus}
            className={`flex items-center justify-center px-4 sm:px-5 py-2 rounded-full shadow-md transition-all duration-300 touch-manipulation text-sm sm:text-base font-medium ${
              isOnline
                ? 'bg-green-500 hover:bg-green-600 active:bg-green-700'
                : 'bg-red-500 hover:bg-red-600 active:bg-red-700'
            } text-white`}
            style={{ touchAction: 'manipulation' }}
          >
            <span className={`inline-block w-2 sm:w-3 h-2 sm:h-3 rounded-full mr-1 sm:mr-2 ${
              isOnline ? 'bg-green-200 animate-pulse' : 'bg-red-200'
            }`}></span>
            <span className="hidden sm:inline">{isOnline ? 'Online' : 'Offline'}</span>
            <span className="sm:hidden">{isOnline ? '🟢' : '🔴'}</span>
          </button>
        </div>
      </div>

      {/* Location info */}
      {locationAddress && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-lg flex items-center">
          <svg className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          <div>
            <p className="text-sm font-medium text-blue-700">Your current location</p>
            <p className="text-sm text-blue-600">{locationAddress}</p>
          </div>
        </div>
      )}

      {/* Route Banner */}
      {selectedBooking && selectedBooking.status === 'inProgress' && routeInfo && (
        <div className="mb-4 p-4 bg-green-100 border border-green-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-green-500 rounded-full p-2 mr-3">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                </svg>
              </div>
              <div>
                <h3 className="font-bold text-green-800">Ride in Progress</h3>
                <p className="text-green-700">Destination: {selectedBooking.airport.name}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium text-green-800">{routeInfo.formattedDistance}</div>
              <div className="text-sm font-medium text-green-800">{routeInfo.formattedDuration}</div>
            </div>
          </div>
        </div>
      )}

      {/* Back button when a booking is selected */}
      {selectedBooking && (
        <div className="mb-4">
          <button
            onClick={() => setSelectedBooking(null)}
            className="flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded shadow-md transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
          </button>
        </div>
      )}

      <div className="mb-8 border rounded overflow-hidden p-4 bg-gray-50">
        {selectedBooking ? (
          <div>
            <div className="relative">
              <MapboxMap
                currentLocation={currentLocation}
                pickupLocation={selectedBooking.pickupLocation}
                height="400px"
                selectable={isSelectingLocation}
                onDirectionsCalculated={handleDirectionsCalculated}
                onLocationSelected={handleLocationSelected}
              />
              <div className="absolute top-2 right-2 z-20">
                {isSelectingLocation ? (
                  <button
                    onClick={() => setIsSelectingLocation(false)}
                    className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded shadow-sm text-sm"
                  >
                    Cancel
                  </button>
                ) : (
                  // Only show the change location button for bookings that haven't started yet
                  ['pending', 'accepted', 'enRouteToPickup'].includes(selectedBooking.status) && (
                    <button
                      onClick={() => setIsSelectingLocation(true)}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded shadow-sm text-sm"
                    >
                      Change Pickup Location
                    </button>
                  )
                )}
              </div>
            </div>
            <div className="mt-4 p-4 border rounded bg-white">
              <h3 className="font-bold">Selected Booking</h3>
              <p>Pickup: {selectedBooking.pickupLocation.address}</p>
              <p>Airport: {selectedBooking.airport.name}</p>
              <p>Status: {selectedBooking.status}</p>

              {/* Rider information */}
              {riderInfo && ['accepted', 'inProgress'].includes(selectedBooking.status) && (
                <div className="mt-3 p-3 bg-green-50 border border-green-100 rounded">
                  <h4 className="font-medium text-green-800 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Rider Information
                  </h4>
                  <div className="mt-2">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      <p className="text-green-800">{riderInfo.fullName}</p>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                      </svg>
                      <a href={`tel:${riderInfo.phoneNumber}`} className="text-green-800 underline">{riderInfo.phoneNumber}</a>
                    </div>
                    <div className="flex items-center mt-2">
                      <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                      <p className="text-green-800">Passengers: {selectedBooking.passengers} {selectedBooking.passengers === 1 ? 'person' : 'people'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Route information */}
              {routeInfo && ['accepted'].includes(selectedBooking.status) && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-100 rounded">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium text-blue-800 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                      </svg>
                      Route to Pickup
                    </h4>
                    <button
                      onClick={() => calculateDirections(selectedBooking)}
                      className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                      title="Update route based on current location"
                    >
                      <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                      </svg>
                      Recalculate
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <p className="text-xs text-blue-600">Distance</p>
                      <p className="font-medium text-blue-800">{routeInfo.formattedDistance}</p>
                    </div>
                    <div>
                      <p className="text-xs text-blue-600">Estimated Time</p>
                      <p className="font-medium text-blue-800">{routeInfo.formattedDuration}</p>
                    </div>
                  </div>
                </div>
              )}

              {isSelectingLocation && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                  <p className="text-yellow-700">Click on the map to select a new pickup location</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div>
            <MapboxMap
              currentLocation={currentLocation}
              height="400px"
            />
            <p className="text-center text-gray-600 mt-2">Select a booking to see pickup location</p>
          </div>
        )}
      </div>

      {/* Active booking details */}
      {selectedBooking && (
        <div className="mb-8 bg-white border rounded-lg shadow-sm overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b">
            <h2 className="text-lg font-medium">Current Ride</h2>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Pickup Location</p>
                <p className="font-medium">{selectedBooking.pickupLocation.address}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Destination</p>
                <p className="font-medium">{selectedBooking.airport.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Scheduled Time</p>
                <p className="font-medium">{formatDate(selectedBooking.scheduledTime)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Fare</p>
                <p className="font-medium">${selectedBooking.fare}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Passengers</p>
                <p className="font-medium">{selectedBooking.passengers} {selectedBooking.passengers === 1 ? 'person' : 'people'}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedBooking.status)}`}>
                {selectedBooking.status}
              </span>
            </div>

            <div className="mt-6 flex flex-wrap gap-2">

              {selectedBooking.status === 'inProgress' && (
                <button
                  onClick={() => setSelectedBooking(null)}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                  </svg>
                  Back
                </button>
              )}

              {['accepted'].includes(selectedBooking.status) && (
                <button
                  onClick={() => updateBookingStatus(selectedBooking, 'cancelled')}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                  Cancel Ride
                </button>
              )}

              {/* Delete Booking button - available for all statuses */}
              <button
                onClick={() => deleteBookingHistory(selectedBooking.id)}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Mark and Delete Booking
              </button>
            </div>
          </div>
        </div>
      )}

      {/* My Bookings Section */}
      {myBookings.length > 0 && !selectedBooking && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">My Bookings</h2>
          <div className="space-y-4">
            {myBookings.map((booking) => (
              <div key={booking.id} className="border rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{booking.pickupLocation.address}</h3>
                      <p className="text-sm text-gray-500">To: {booking.airport.name}</p>
                      <p className="text-sm text-gray-500">Passengers: {booking.passengers} {booking.passengers === 1 ? 'person' : 'people'}</p>
                      <div className="mt-2">
                        <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${booking.fare}</p>
                      <p className="text-sm text-gray-500">{formatDate(booking.scheduledTime)}</p>
                    </div>
                  </div>
                  <div className="mt-4 flex flex-col space-y-2">
                    <button
                      onClick={() => selectBooking(booking)}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
                    >
                      View Details
                    </button>
                    <button
                      onClick={() => deleteBookingHistory(booking.id)}
                      className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                      Mark and Delete Booking
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Available Bookings Section */}
      {isOnline && pendingBookings.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Available Bookings</h2>
          <div className="space-y-4">
            {pendingBookings.map((booking) => (
              <div key={booking.id} className="border rounded-lg overflow-hidden bg-white shadow-sm">
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{booking.pickupLocation.address}</h3>
                      <p className="text-sm text-gray-500">To: {booking.airport.name}</p>
                      <p className="text-sm text-gray-500">Scheduled: {formatDate(booking.scheduledTime)}</p>
                      <p className="text-sm text-gray-500">Passengers: {booking.passengers} {booking.passengers === 1 ? 'person' : 'people'}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${booking.fare}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={() => acceptBooking(booking)}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors"
                    >
                      Accept Booking
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No bookings message */}
      {isOnline && pendingBookings.length === 0 && myBookings.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium text-gray-600">No bookings available at the moment</h3>
          <p className="text-gray-500 mt-2">New booking requests will appear here</p>
        </div>
      )}

      {/* Offline message */}
      {!isOnline && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium text-gray-600">You are currently offline</h3>
          <p className="text-gray-500 mt-2">Go online to receive booking requests</p>
          <button
            onClick={toggleOnlineStatus}
            className="mt-4 bg-green-500 hover:bg-green-600 active:bg-green-700 text-white px-8 py-3 rounded-full transition-all duration-300 shadow-md font-medium text-lg touch-manipulation"
            style={{ touchAction: 'manipulation' }}
          >
            <div className="flex items-center">
              <span className="inline-block w-3 h-3 rounded-full mr-2 bg-green-200"></span>
              Go Online
            </div>
          </button>
        </div>
      )}
    </div>
    </Layout>
  );
}

// Wrap the component with ProtectedRoute
export default function DriverDashboardPage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'driver']}>
      <DriverDashboard />
    </ProtectedRoute>
  );
}
