(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[964],{4830:function(e,n,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin",function(){return r(2921)}])},2921:function(e,n,r){"use strict";r.r(n),r.d(n,{default:function(){return a}});var u=r(5893),t=r(7294),i=r(1163),s=r(837),c=r(7339);function a(){let e=(0,i.useRouter)(),{user:n,loading:r}=(0,s.a)(),{isAdmin:a}=(0,c.s)();return(0,t.useEffect)(()=>{r||(n&&a?e.replace("/admin/dashboard"):e.replace("/login"))},[n,r,e,a]),(0,u.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,u.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}}},function(e){e.O(0,[888,774,179],function(){return e(e.s=4830)}),_N_E=e.O()}]);