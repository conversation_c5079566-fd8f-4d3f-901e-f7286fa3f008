/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/book"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cbook%5Cindex.tsx&page=%2Fbook!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cbook%5Cindex.tsx&page=%2Fbook! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/book\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/book/index.tsx */ \"./src/pages/book/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/book\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNidXJhayU1Q0Rlc2t0b3AlNUNiYXJvJTIwcmlkZSU1Q3R0dCU1Q05ldyUyMGZvbGRlciU1Q3NyYyU1Q3BhZ2VzJTVDYm9vayU1Q2luZGV4LnRzeCZwYWdlPSUyRmJvb2shIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsOERBQTRCO0FBQ25EO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz85NWZmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvYm9va1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2Jvb2svaW5kZXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9ib29rXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cbook%5Cindex.tsx&page=%2Fbook!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlhQSxxQkFBbUI7ZUFBbkJBOztJQUpBQyxpQkFBZTtlQUFmQTs7SUFHQUMsaUJBQWU7ZUFBZkE7O0lBSkFDLGdCQUFjO2VBQWRBOztJQUVBQyxnQkFBYztlQUFkQTs7SUFJQUMsc0JBQW9CO2VBQXBCQTs7SUFIQUMscUJBQW1CO2VBQW5CQTs7Ozs7Ozs7SUF1UUdDLFlBQVU7ZUFBVkE7OztBQTFRVCxNQUFNSixpQkFBaUI7QUFDdkIsTUFBTUYsa0JBQWtCO0FBQ3hCLE1BQU1HLGlCQUFpQjtBQUN2QixNQUFNRSxzQkFBc0I7QUFDNUIsTUFBTUosa0JBQWtCO0FBQ3hCLE1BQU1GLHNCQUFzQjtBQUM1QixNQUFNSyx1QkFBdUI7O1VBdUl4QkcsWUFBQUE7Ozs7R0FBQUEsZ0JBQUFBLENBQUFBLGVBQUFBLENBQUFBLENBQUFBOztVQThEQUMsd0JBQUFBOzs7OztHQUFBQSw0QkFBQUEsQ0FBQUEsMkJBQUFBLENBQUFBLENBQUFBO0FBK0RMLFNBQVNGLFdBQVdHLEtBQVU7SUFDbkMsNkVBQTZFO0lBQzdFLDZFQUE2RTtJQUM3RSx1RUFBdUU7SUFDdkUsZ0JBQWdCO0lBQ2hCLE9BQ0VBLFNBQ0MsUUFBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsZUFDL0MsT0FBT0EsTUFBTUMsSUFBSSxLQUFLO0FBRTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMudHM/ZWYxYyJdLCJuYW1lcyI6WyJBQ1RJT05fRkFTVF9SRUZSRVNIIiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1BSRUZFVENIIiwiQUNUSU9OX1JFRlJFU0giLCJBQ1RJT05fUkVTVE9SRSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiQUNUSU9OX1NFUlZFUl9QQVRDSCIsImlzVGhlbmFibGUiLCJQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJ2YWx1ZSIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/components/BasicMap.tsx":
/*!*************************************!*\
  !*** ./src/components/BasicMap.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BasicMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nfunction BasicMap(param) {\n    let { height = \"300px\", selectable = false, onLocationSelected, initialLocation } = param;\n    _s();\n    const mapContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Method to update the map with a new location\n    const updateMapLocation = (location)=>{\n        if (!mapRef.current || !location || location.lat === 0 || location.lng === 0) return;\n        // Remove existing markers\n        const markers = document.querySelectorAll(\".mapboxgl-marker\");\n        markers.forEach((marker)=>marker.remove());\n        // Add a new marker\n        new window.mapboxgl.Marker({\n            color: \"#3b82f6\"\n        }).setLngLat([\n            location.lng,\n            location.lat\n        ]).addTo(mapRef.current);\n        // Fly to the new location\n        mapRef.current.flyTo({\n            center: [\n                location.lng,\n                location.lat\n            ],\n            zoom: 14,\n            essential: true\n        });\n        // Add popup with address if available\n        if (location.address) {\n            new window.mapboxgl.Popup({\n                offset: 25,\n                closeButton: false\n            }).setLngLat([\n                location.lng,\n                location.lat\n            ]).setHTML('<p style=\"margin: 0;\">'.concat(location.address, \"</p>\")).addTo(mapRef.current);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load Mapbox script dynamically\n        const loadMapbox = ()=>{\n            // Check if Mapbox is already loaded\n            if (window.mapboxgl) {\n                initializeMap();\n                return;\n            }\n            // Create script element\n            const script = document.createElement(\"script\");\n            script.src = \"https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js\";\n            script.async = true;\n            script.onload = ()=>{\n                // Initialize map after script loads\n                initializeMap();\n            };\n            document.head.appendChild(script);\n            // Add CSS\n            const link = document.createElement(\"link\");\n            link.href = \"https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css\";\n            link.rel = \"stylesheet\";\n            document.head.appendChild(link);\n        };\n        // Initialize the map\n        const initializeMap = ()=>{\n            if (!mapContainerRef.current || !window.mapboxgl) return;\n            // Set access token\n            window.mapboxgl.accessToken = \"pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A\";\n            // Determine center coordinates\n            let centerLng = 34.5925; // Default to Gambela, Ethiopia\n            let centerLat = 8.2483;\n            // Use initialLocation if provided\n            if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n                centerLng = initialLocation.lng;\n                centerLat = initialLocation.lat;\n            }\n            // No automatic geolocation - we're making location selection fully manual\n            console.log(\"Using manual location selection only - automatic geolocation disabled\");\n            // Create map\n            const map = new window.mapboxgl.Map({\n                container: mapContainerRef.current,\n                style: \"mapbox://styles/mapbox/streets-v11\",\n                center: [\n                    centerLng,\n                    centerLat\n                ],\n                zoom: 13\n            });\n            // Store map reference\n            mapRef.current = map;\n            // Add navigation controls\n            map.addControl(new window.mapboxgl.NavigationControl());\n            // Add a marker for the initial location if provided\n            if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n                // Create a marker with a popup showing the address\n                new window.mapboxgl.Marker({\n                    color: \"#3b82f6\"\n                }).setLngLat([\n                    initialLocation.lng,\n                    initialLocation.lat\n                ]).addTo(map);\n                // Add popup with address if available\n                if (initialLocation.address) {\n                    new window.mapboxgl.Popup({\n                        offset: 25,\n                        closeButton: false\n                    }).setLngLat([\n                        initialLocation.lng,\n                        initialLocation.lat\n                    ]).setHTML('<p style=\"margin: 0;\">'.concat(initialLocation.address, \"</p>\")).addTo(map);\n                }\n            } else {\n                // Add a default marker if no initial location\n                new window.mapboxgl.Marker().setLngLat([\n                    centerLng,\n                    centerLat\n                ]).addTo(map);\n            }\n            // Add click handler for location selection if selectable is true\n            if (selectable && onLocationSelected) {\n                // Add cursor style for selectable mode\n                map.getCanvas().style.cursor = \"crosshair\";\n                // Handle both click and touchend events for better mobile support\n                const handleLocationSelect = async (e)=>{\n                    const { lng, lat } = e.lngLat;\n                    console.log(\"Location selected:\", {\n                        lng,\n                        lat\n                    });\n                    // Remove existing markers and popups\n                    const markers = document.querySelectorAll(\".mapboxgl-marker\");\n                    markers.forEach((marker)=>marker.remove());\n                    const popups = document.querySelectorAll(\".mapboxgl-popup\");\n                    popups.forEach((popup)=>popup.remove());\n                    // Create a new marker at the clicked location\n                    const marker = new window.mapboxgl.Marker({\n                        color: \"#3b82f6\",\n                        scale: 1.2 // Make marker slightly larger for better visibility\n                    }).setLngLat([\n                        lng,\n                        lat\n                    ]).addTo(map);\n                    setIsLoading(true);\n                    try {\n                        // Get address from coordinates using Mapbox Geocoding API\n                        const response = await fetch(\"https://api.mapbox.com/geocoding/v5/mapbox.places/\".concat(lng, \",\").concat(lat, \".json?access_token=\").concat(window.mapboxgl.accessToken), {\n                            method: \"GET\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Geocoding failed: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        console.log(\"Geocoding response:\", data);\n                        let address = \"Selected location\";\n                        if (data.features && data.features.length > 0) {\n                            address = data.features[0].place_name;\n                        }\n                        // Create popup with address and action buttons\n                        const popup = new window.mapboxgl.Popup({\n                            offset: 25,\n                            closeButton: true,\n                            closeOnClick: false,\n                            maxWidth: \"300px\"\n                        }).setLngLat([\n                            lng,\n                            lat\n                        ]).setHTML('\\n                <div style=\"text-align: center; padding: 8px;\">\\n                  <p style=\"margin: 0 0 12px 0; font-weight: 500; color: #374151;\">'.concat(address, '</p>\\n                  <div style=\"display: flex; gap: 8px; justify-content: center;\">\\n                    <button id=\"select-location\" style=\"\\n                      background-color: #3b82f6;\\n                      color: white;\\n                      border: none;\\n                      padding: 8px 16px;\\n                      border-radius: 6px;\\n                      cursor: pointer;\\n                      font-size: 14px;\\n                      font-weight: 500;\\n                      min-height: 44px;\\n                      min-width: 100px;\\n                      touch-action: manipulation;\\n                    \">\\n                      ✓ Select\\n                    </button>\\n                    <button id=\"cancel-location\" style=\"\\n                      background-color: #6b7280;\\n                      color: white;\\n                      border: none;\\n                      padding: 8px 16px;\\n                      border-radius: 6px;\\n                      cursor: pointer;\\n                      font-size: 14px;\\n                      font-weight: 500;\\n                      min-height: 44px;\\n                      min-width: 80px;\\n                      touch-action: manipulation;\\n                    \">\\n                      Cancel\\n                    </button>\\n                  </div>\\n                </div>\\n              ')).addTo(map);\n                        // Add event listeners to the buttons\n                        setTimeout(()=>{\n                            const selectButton = document.getElementById(\"select-location\");\n                            const cancelButton = document.getElementById(\"cancel-location\");\n                            if (selectButton) {\n                                selectButton.addEventListener(\"click\", (event)=>{\n                                    event.preventDefault();\n                                    event.stopPropagation();\n                                    console.log(\"Select button clicked, calling onLocationSelected\");\n                                    onLocationSelected({\n                                        lat,\n                                        lng,\n                                        address\n                                    });\n                                    popup.remove();\n                                });\n                                // Add touch event for mobile\n                                selectButton.addEventListener(\"touchend\", (event)=>{\n                                    event.preventDefault();\n                                    event.stopPropagation();\n                                    console.log(\"Select button touched, calling onLocationSelected\");\n                                    onLocationSelected({\n                                        lat,\n                                        lng,\n                                        address\n                                    });\n                                    popup.remove();\n                                });\n                            }\n                            if (cancelButton) {\n                                cancelButton.addEventListener(\"click\", (event)=>{\n                                    event.preventDefault();\n                                    event.stopPropagation();\n                                    popup.remove();\n                                    marker.remove();\n                                });\n                                // Add touch event for mobile\n                                cancelButton.addEventListener(\"touchend\", (event)=>{\n                                    event.preventDefault();\n                                    event.stopPropagation();\n                                    popup.remove();\n                                    marker.remove();\n                                });\n                            }\n                        }, 100);\n                    } catch (error) {\n                        console.error(\"Error geocoding location:\", error);\n                        // Still allow selection even if geocoding fails\n                        const popup = new window.mapboxgl.Popup({\n                            offset: 25,\n                            closeButton: true,\n                            closeOnClick: false\n                        }).setLngLat([\n                            lng,\n                            lat\n                        ]).setHTML('\\n                <div style=\"text-align: center; padding: 8px;\">\\n                  <p style=\"margin: 0 0 12px 0; color: #374151;\">Selected location</p>\\n                  <p style=\"margin: 0 0 12px 0; font-size: 12px; color: #6b7280;\">Coordinates: '.concat(lat.toFixed(6), \", \").concat(lng.toFixed(6), '</p>\\n                  <div style=\"display: flex; gap: 8px; justify-content: center;\">\\n                    <button id=\"select-location-fallback\" style=\"\\n                      background-color: #3b82f6;\\n                      color: white;\\n                      border: none;\\n                      padding: 8px 16px;\\n                      border-radius: 6px;\\n                      cursor: pointer;\\n                      font-size: 14px;\\n                      min-height: 44px;\\n                      touch-action: manipulation;\\n                    \">\\n                      ✓ Select\\n                    </button>\\n                  </div>\\n                </div>\\n              ')).addTo(map);\n                        setTimeout(()=>{\n                            const selectButton = document.getElementById(\"select-location-fallback\");\n                            if (selectButton) {\n                                const handleSelect = (event)=>{\n                                    event.preventDefault();\n                                    event.stopPropagation();\n                                    onLocationSelected({\n                                        lat,\n                                        lng,\n                                        address: \"Selected location\"\n                                    });\n                                    popup.remove();\n                                };\n                                selectButton.addEventListener(\"click\", handleSelect);\n                                selectButton.addEventListener(\"touchend\", handleSelect);\n                            }\n                        }, 100);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                };\n                // Add both click and touchend event listeners\n                map.on(\"click\", handleLocationSelect);\n                map.on(\"touchend\", handleLocationSelect);\n            } else {\n                // Reset cursor for non-selectable mode\n                map.getCanvas().style.cursor = \"\";\n            }\n            // Clean up on unmount\n            return ()=>map.remove();\n        };\n        loadMapbox();\n    }, [\n        selectable,\n        onLocationSelected\n    ]);\n    // Update map when initialLocation changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {\n            // Wait for map to be initialized\n            const checkMapInterval = setInterval(()=>{\n                if (mapRef.current) {\n                    updateMapLocation(initialLocation);\n                    clearInterval(checkMapInterval);\n                }\n            }, 100);\n            // Clear interval after 5 seconds to prevent memory leaks\n            setTimeout(()=>clearInterval(checkMapInterval), 5000);\n        }\n    }, [\n        initialLocation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapContainerRef,\n                style: {\n                    width: \"100%\",\n                    height,\n                    position: \"relative\",\n                    border: \"1px solid #ddd\",\n                    borderRadius: \"4px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: \"rgba(0, 0, 0, 0.2)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 10\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: \"white\",\n                        padding: \"12px\",\n                        borderRadius: \"50%\",\n                        boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                fill: \"none\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: \"4\",\n                                opacity: \"0.25\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                fill: \"none\",\n                                stroke: \"#3b82f6\",\n                                strokeWidth: \"4\",\n                                strokeDasharray: \"60 30\",\n                                style: {\n                                    animation: \"spin 1s linear infinite\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                                children: \"\\n                @keyframes spin {\\n                  0% { transform: rotate(0deg); }\\n                  100% { transform: rotate(360deg); }\\n                }\\n              \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this),\n            selectable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    left: \"10px\",\n                    right: \"10px\",\n                    backgroundColor: \"#3b82f6\",\n                    color: \"white\",\n                    padding: \"12px 16px\",\n                    borderRadius: \"8px\",\n                    boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n                    zIndex: 5,\n                    fontSize: \"14px\",\n                    fontWeight: \"500\",\n                    textAlign: \"center\",\n                    border: \"2px solid #1e40af\"\n                },\n                children: \"\\uD83D\\uDCCD Tap anywhere on the map to select your pickup location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n                lineNumber: 435,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\BasicMap.tsx\",\n        lineNumber: 374,\n        columnNumber: 5\n    }, this);\n}\n_s(BasicMap, \"XvKi6C8x3P2fg+Jt4jsN79Dnvl8=\");\n_c = BasicMap;\nvar _c;\n$RefreshReg$(_c, \"BasicMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/BasicMap.tsx\n"));

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Layout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\n\n\n\nfunction Layout(param) {\n    let { children, title = \"BaroRide\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Book your ride with BaroRide - fixed fares and reliable service\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white text-gray-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-100 border-t border-gray-200 py-4 mt-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center text-gray-600 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" BaroRide. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDNkI7QUFDQztBQU9mLFNBQVNFLE9BQU8sS0FBNkM7UUFBN0MsRUFBRUMsUUFBUSxFQUFFQyxRQUFRLFVBQVUsRUFBZSxHQUE3QztJQUM3QixxQkFDRTs7MEJBQ0UsOERBQUNKLGtEQUFJQTs7a0NBQ0gsOERBQUNJO2tDQUFPQTs7Ozs7O2tDQUNSLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBK0JDLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUF3Q0MsU0FBUTs7Ozs7O2tDQUMzRCw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQTZCQyxTQUFROzs7Ozs7a0NBQ2hELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBeUJDLFNBQVE7Ozs7OztrQ0FDNUMsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFnQkMsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUVqQyw4REFBQ0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDWCwrQ0FBTUE7Ozs7O2tDQUNQLDhEQUFDWTt3QkFBS0QsV0FBVTtrQ0FBc0RUOzs7Ozs7a0NBQ3RFLDhEQUFDVzt3QkFBT0YsV0FBVTtrQ0FDaEIsNEVBQUNEOzRCQUFJQyxXQUFVOztnQ0FBMkQ7Z0NBQ2hFLElBQUlHLE9BQU9DLFdBQVc7Z0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTdDO0tBM0J3QmQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0LnRzeD9kZThiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgTmF2YmFyIGZyb20gJy4vTmF2YmFyJztcblxuaW50ZXJmYWNlIExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuLCB0aXRsZSA9ICdCYXJvUmlkZScgfTogTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT57dGl0bGV9PC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkJvb2sgeW91ciByaWRlIHdpdGggQmFyb1JpZGUgLSBmaXhlZCBmYXJlcyBhbmQgcmVsaWFibGUgc2VydmljZVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MS4wXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiMyNTYzZWJcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJkZWZhdWx0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXRpdGxlXCIgY29udGVudD1cIkJhcm9SaWRlXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvbG9nby1pY29uLnN2Z1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2xvZ28taWNvbi5zdmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJzaG9ydGN1dCBpY29uXCIgaHJlZj1cIi9sb2dvLWljb24uc3ZnXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggZmxleC1jb2wgYmctd2hpdGUgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICA8TmF2YmFyIC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtZ3JvdyB3LWZ1bGwgcHgtNCBzbTpweC02IGxnOnB4LTggcHktNCBzbTpweS02XCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHktNCBtdC1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHRleHQtY2VudGVyIHRleHQtZ3JheS02MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgJmNvcHk7IHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IEJhcm9SaWRlLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJOYXZiYXIiLCJMYXlvdXQiLCJjaGlsZHJlbiIsInRpdGxlIiwibWV0YSIsIm5hbWUiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIiwiZm9vdGVyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n"));

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NotificationBell */ \"./src/components/NotificationBell.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Navbar() {\n    _s();\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBackButton, setShowBackButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Determine if back button should be shown based on current route\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Show back button on all pages except home page\n        setShowBackButton(router.pathname !== \"/\");\n    }, [\n        router.pathname\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleBack = ()=>{\n        router.back();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-2 sm:px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors\",\n                                    \"aria-label\": \"Go back\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 sm:w-7 sm:h-7 text-gray-700\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo-icon.svg\",\n                                            alt: \"BaroRide Logo\",\n                                            className: \"h-8 w-8 sm:h-10 sm:w-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600 font-bold text-lg sm:text-xl\",\n                                            children: \"BaroRide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/book\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Book a Ride\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/driver/dashboard\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: user.fullName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-1\",\n                            children: [\n                                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 22\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 border-b border-gray-100 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 capitalize\",\n                                        children: user.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this),\n                            user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/book\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDCF1 Book a Ride\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 19\n                            }, this) : user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/driver/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDE97 Driver Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"⚙️ Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    signOut();\n                                    setIsMenuOpen(false);\n                                },\n                                className: \"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                children: \"\\uD83D\\uDEAA Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDD11 Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"✨ Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"1HJpKgE8Q5BRrDJDJ3D6t162MHQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n"));

/***/ }),

/***/ "./src/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationBell; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) return;\n        // Listen for user notifications\n        // Note: We're not using orderBy to avoid Firestore index issues\n        const notificationsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", user.id));\n        const unsubscribe = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.onSnapshot)(notificationsQuery, (snapshot)=>{\n            const userNotifications = [];\n            let count = 0;\n            snapshot.forEach((doc)=>{\n                var _data_createdAt;\n                const data = doc.data();\n                const notification = {\n                    id: doc.id,\n                    ...data,\n                    createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date()\n                };\n                userNotifications.push(notification);\n                if (!notification.read) {\n                    count++;\n                }\n            });\n            // Sort notifications by createdAt in descending order (newest first)\n            userNotifications.sort((a, b)=>{\n                return b.createdAt.getTime() - a.createdAt.getTime();\n            });\n            setNotifications(userNotifications);\n            setUnreadCount(count);\n        });\n        return ()=>unsubscribe();\n    }, [\n        user\n    ]);\n    const markAsRead = async (notificationId)=>{\n        if (!user) return;\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notificationId), {\n                read: true\n            });\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n        }\n    };\n    const markAllAsRead = async ()=>{\n        if (!user) return;\n        try {\n            const promises = notifications.filter((notification)=>!notification.read).map((notification)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notification.id), {\n                    read: true\n                }));\n            await Promise.all(promises);\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n        }\n    };\n    const toggleDropdown = ()=>{\n        setShowDropdown(!showDropdown);\n    };\n    const getNotificationTypeStyles = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 text-blue-800\";\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleDropdown,\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                \"aria-label\": \"Notifications\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\",\n                        children: unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-100 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: markAllAsRead,\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                children: \"No notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-3 border-b border-gray-100 \".concat(!notification.read ? \"bg-blue-50\" : \"\"),\n                                    onClick: ()=>markAsRead(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 rounded-full p-1 \".concat(getNotificationTypeStyles(notification.type)),\n                                                children: [\n                                                    notification.type === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"warning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-900 whitespace-pre-line\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-500\",\n                                                        children: new Date(notification.createdAt).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    notification.driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold\",\n                                                                            children: notification.driverDetails.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                notification.driverDetails.vehicleColor,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleMake,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleModel\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium\",\n                                                                            children: [\n                                                                                \"License: \",\n                                                                                notification.driverDetails.licensePlate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"H72KrDXf27US4HLmmm+hWQjccvU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/NotificationBell.tsx\n"));

/***/ }),

/***/ "./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProtectedRoute; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/RBACContext */ \"./src/contexts/RBACContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProtectedRoute(param) {\n    let { children, requiredRoles, redirectTo = \"/login\" } = param;\n    _s();\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { checkAccess } = (0,_contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.useRBAC)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__.useNotification)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait until auth is loaded\n        if (loading) return;\n        // If no user is logged in, redirect to login\n        if (!user) {\n            router.push(redirectTo);\n            showNotification(\"Please log in to access this page\", \"warning\");\n            return;\n        }\n        // Check if user has the required role\n        const hasAccess = checkAccess(requiredRoles);\n        if (!hasAccess) {\n            // Determine where to redirect based on user's role\n            let redirectPath = \"/\";\n            let message = \"Access denied. You do not have permission to view this page.\";\n            if (user.role === \"driver\") {\n                redirectPath = \"/driver/dashboard\";\n                message = \"Access denied. Redirected to driver dashboard.\";\n            } else if (user.role === \"rider\") {\n                redirectPath = \"/\";\n                message = \"Access denied. Redirected to home page.\";\n            }\n            router.push(redirectPath);\n            showNotification(message, \"warning\");\n        }\n    }, [\n        user,\n        loading,\n        requiredRoles,\n        router,\n        redirectTo,\n        checkAccess\n    ]);\n    // Show nothing while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated or doesn't have required role, don't render children\n    if (!user || !checkAccess(requiredRoles)) {\n        return null;\n    }\n    // If authenticated and has required role, render children\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"INJoXagqzFpYZzaRpV0l8cwc1ZQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.useRBAC,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_5__.useNotification\n    ];\n});\n_c = ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ProtectedRoute.tsx\n"));

/***/ }),

/***/ "./src/pages/book/index.tsx":
/*!**********************************!*\
  !*** ./src/pages/book/index.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BookRidePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _components_BasicMap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BasicMap */ \"./src/components/BasicMap.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/firebase-helpers */ \"./src/utils/firebase-helpers.ts\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"./src/components/ProtectedRoute.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst AIRPORTS = [\n    {\n        code: \"GMB\",\n        name: \"Gambela International Airport\"\n    }\n];\nfunction BookRide() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    const [pickupLocation, setPickupLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        address: \"\",\n        lat: 0,\n        lng: 0\n    });\n    const [selectedAirport, setSelectedAirport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(AIRPORTS[0]);\n    const [passengers, setPassengers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isSelectingOnMap, setIsSelectingOnMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [estimatedFare, setEstimatedFare] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [previousBookings, setPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingBookings, setIsLoadingBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPreviousBookings, setShowPreviousBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedBookingId, setSelectedBookingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingGps, setIsLoadingGps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch previous bookings when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Fetch previous bookings if user is logged in\n        if (user) {\n            fetchPreviousBookings();\n        }\n    }, [\n        user\n    ]);\n    // Fetch user's previous bookings\n    const fetchPreviousBookings = async ()=>{\n        if (!user) return;\n        setIsLoadingBookings(true);\n        try {\n            // Log for debugging in production\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                console.log(\"Fetching previous bookings in Firebase hosting environment\");\n            }\n            // Use our safe query function if we're in production, otherwise use regular query\n            let bookings = [];\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use the safe query function with retry logic\n                const constraints = [\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"),\n                    (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5)\n                ];\n                const results = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeQueryDocs)(\"bookings\", constraints);\n                bookings = results.map((data)=>({\n                        id: data.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    }));\n            } else {\n                // Regular query for development environment\n                const bookingsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"riderId\", \"==\", user.id), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"status\", \"==\", \"completed\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.orderBy)(\"updatedAt\", \"desc\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.limit)(5) // Limit to 5 most recent bookings\n                );\n                const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDocs)(bookingsQuery);\n                querySnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    // Convert Firestore timestamps to Date objects\n                    const booking = {\n                        id: doc.id,\n                        ...data,\n                        scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),\n                        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),\n                        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),\n                        passengers: data.passengers || 1\n                    };\n                    bookings.push(booking);\n                });\n            }\n            setPreviousBookings(bookings);\n            console.log(\"Fetched \".concat(bookings.length, \" previous bookings\"));\n        } catch (error) {\n            console.error(\"Error fetching previous bookings:\", error);\n            showNotification(\"Could not load your previous bookings. Please try again later.\", \"warning\");\n        } finally{\n            setIsLoadingBookings(false);\n        }\n    };\n    // Clear selected booking and pickup location\n    const clearSelection = ()=>{\n        setPickupLocation({\n            address: \"\",\n            lat: 0,\n            lng: 0\n        });\n        setSelectedBookingId(null);\n    };\n    // Toggle between map selection and manual entry\n    const toggleMapSelection = ()=>{\n        setIsSelectingOnMap(!isSelectingOnMap);\n    };\n    // Handle location selection from map\n    const handleLocationSelected = (location)=>{\n        // Validate the location\n        if (!location || typeof location.lat !== \"number\" || typeof location.lng !== \"number\") {\n            showNotification(\"Invalid location selected. Please try again.\", \"error\");\n            return;\n        }\n        // Validate coordinates are reasonable (not 0,0 or extreme values)\n        if (location.lat === 0 && location.lng === 0) {\n            showNotification(\"Invalid coordinates selected. Please try again.\", \"error\");\n            return;\n        }\n        // Create a valid location object\n        const newLocation = {\n            lat: location.lat,\n            lng: location.lng,\n            address: location.address || \"Selected location\"\n        };\n        console.log(\"Location selected from map:\", newLocation);\n        // Update state\n        setPickupLocation(newLocation);\n        // Clear selected booking since we're selecting a new location\n        setSelectedBookingId(null);\n        setIsSelectingOnMap(false);\n        // Calculate fare for the new location\n        const fare = calculateFare(newLocation);\n        setEstimatedFare(fare);\n        // Show confirmation to the user\n        showNotification(\"Pickup location selected successfully!\", \"success\");\n    };\n    // Validate and geocode manual address entry with debouncing\n    const validateAndGeocodeAddress = async (address)=>{\n        if (!address || address.trim().length < 3) {\n            return;\n        }\n        try {\n            // Use Mapbox Geocoding API to get coordinates for the address\n            const response = await fetch(\"https://api.mapbox.com/geocoding/v5/mapbox.places/\".concat(encodeURIComponent(address), \".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A&limit=1\"));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.features && data.features.length > 0) {\n                    const feature = data.features[0];\n                    const [lng, lat] = feature.center;\n                    // Update location with geocoded coordinates\n                    setPickupLocation((prev)=>({\n                            ...prev,\n                            lat,\n                            lng,\n                            address: feature.place_name || address\n                        }));\n                    // Calculate fare for the new location\n                    const fare = calculateFare({\n                        lat,\n                        lng,\n                        address\n                    });\n                    setEstimatedFare(fare);\n                    console.log(\"Address geocoded successfully:\", {\n                        lat,\n                        lng,\n                        address: feature.place_name\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error geocoding address:\", error);\n        // Don't show error to user for geocoding failures during typing\n        }\n    };\n    // Debounced address validation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            if (pickupLocation.address && pickupLocation.address.length > 3 && (pickupLocation.lat === 0 || pickupLocation.lng === 0)) {\n                validateAndGeocodeAddress(pickupLocation.address);\n            }\n        }, 1000); // Wait 1 second after user stops typing\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        pickupLocation.address\n    ]);\n    // Toggle showing previous bookings\n    const togglePreviousBookings = ()=>{\n        setShowPreviousBookings(!showPreviousBookings);\n    };\n    // Get user's current location using GPS - manual action\n    const getUserLocation = async ()=>{\n        if (!navigator.geolocation) {\n            showNotification(\"Geolocation is not supported by your browser. Please enter your location manually.\", \"error\");\n            return;\n        }\n        setIsLoadingGps(true);\n        showNotification(\"Getting your current location...\", \"info\");\n        try {\n            // Get current position with better timeout and error handling\n            const position = await new Promise((resolve, reject)=>{\n                navigator.geolocation.getCurrentPosition((position)=>{\n                    console.log(\"GPS position obtained:\", position.coords);\n                    resolve(position);\n                }, (error)=>{\n                    console.error(\"GPS error:\", error.code, error.message);\n                    let errorMessage = \"Unable to retrieve your location.\";\n                    // Provide more specific error messages\n                    if (error.code === 1) {\n                        errorMessage = \"Location access denied. Please enable location services in your browser settings.\";\n                    } else if (error.code === 2) {\n                        errorMessage = \"Your current position is unavailable. Please try again later.\";\n                    } else if (error.code === 3) {\n                        errorMessage = \"Location request timed out. Please try again.\";\n                    }\n                    reject(new Error(errorMessage));\n                }, {\n                    enableHighAccuracy: true,\n                    timeout: 10000,\n                    maximumAge: 0\n                });\n            });\n            const { latitude, longitude } = position.coords;\n            console.log(\"GPS coordinates: \".concat(latitude, \", \").concat(longitude));\n            try {\n                // Get address from coordinates using Mapbox Geocoding API\n                const response = await fetch(\"https://api.mapbox.com/geocoding/v5/mapbox.places/\".concat(longitude, \",\").concat(latitude, \".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A\"));\n                if (!response.ok) {\n                    throw new Error(\"Geocoding API error: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"Geocoding response:\", data);\n                let address = \"Your current location\";\n                if (data.features && data.features.length > 0) {\n                    address = data.features[0].place_name;\n                    console.log(\"Address found:\", address);\n                }\n                // Create location object\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address\n                };\n                // Update pickup location\n                setPickupLocation(location);\n                // Clear selected booking\n                setSelectedBookingId(null);\n                // Calculate fare for the new location\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                // Show success notification\n                showNotification(\"Your current location has been set as the pickup point.\", \"success\");\n            } catch (geocodingError) {\n                console.error(\"Error getting address:\", geocodingError);\n                // Still set the location even if geocoding fails\n                const location = {\n                    lat: latitude,\n                    lng: longitude,\n                    address: \"Your current location\"\n                };\n                setPickupLocation(location);\n                setSelectedBookingId(null);\n                const fare = calculateFare(location);\n                setEstimatedFare(fare);\n                showNotification(\"Location set, but we couldn't get your exact address. You can edit it manually.\", \"warning\");\n            }\n        } catch (error) {\n            console.error(\"Error getting location:\", error);\n            showNotification(error instanceof Error ? error.message : \"Unable to retrieve your location. Please try again or select manually.\", \"error\");\n        } finally{\n            setIsLoadingGps(false);\n        }\n    };\n    // Select a previous booking\n    const selectPreviousBooking = (booking)=>{\n        // Update pickup location\n        setPickupLocation(booking.pickupLocation);\n        // Update airport if it exists in our list\n        const airport = AIRPORTS.find((a)=>a.code === booking.airport.code);\n        if (airport) {\n            setSelectedAirport(airport);\n        }\n        // Set the selected booking ID\n        setSelectedBookingId(booking.id);\n        // Hide the previous bookings dropdown\n        setShowPreviousBookings(false);\n    };\n    // Calculate fare based on distance and number of passengers\n    const calculateFare = function() {\n        let location = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : pickupLocation;\n        // In a real app, you would calculate based on distance between pickup and airport\n        // For this example, we'll use a base fare plus a random amount based on coordinates\n        if (location.lat === 0 || location.lng === 0) {\n            return 0;\n        }\n        const baseFare = 35;\n        // Use the coordinates to create a somewhat realistic variable fare\n        // This is just for demonstration - in a real app you'd use actual distance calculation\n        const seed = location.lat * location.lng % 100;\n        const distanceFare = Math.floor(10 + seed / 5);\n        // Add $5 per additional passenger\n        const passengerFare = (passengers - 1) * 5;\n        return baseFare + distanceFare + passengerFare;\n    };\n    // Update estimated fare when pickup location or passengers count changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pickupLocation.lat !== 0 && pickupLocation.lng !== 0) {\n            const fare = calculateFare(pickupLocation);\n            setEstimatedFare(fare);\n        }\n    }, [\n        pickupLocation,\n        passengers\n    ]);\n    // Create booking directly (no payment required)\n    const createBooking = async ()=>{\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        try {\n            // Show loading notification\n            showNotification(\"Creating your booking...\", \"info\");\n            // Create timestamp objects for Firestore\n            const now = new Date();\n            // Create the booking object with all required fields\n            const booking = {\n                riderId: user.id,\n                pickupLocation: {\n                    address: pickupLocation.address,\n                    lat: pickupLocation.lat,\n                    lng: pickupLocation.lng\n                },\n                airport: {\n                    name: selectedAirport.name,\n                    code: selectedAirport.code\n                },\n                status: \"pending\",\n                fare: estimatedFare > 0 ? estimatedFare : calculateFare(),\n                passengers: passengers,\n                createdAt: now,\n                updatedAt: now\n            };\n            console.log(\"Creating booking with data:\", booking);\n            // Add the booking to Firestore with our safe function if in production\n            let bookingId;\n            if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                // Use safe function with built-in retry logic\n                console.log(\"Using safe function to create booking in production\");\n                bookingId = await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"bookings\", booking);\n                console.log(\"Booking created with ID:\", bookingId);\n            } else {\n                // Use standard function in development\n                try {\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created with ID:\", bookingId);\n                } catch (error) {\n                    console.error(\"Error creating booking:\", error);\n                    showNotification(\"Retrying booking creation...\", \"info\");\n                    // Second attempt after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    const bookingRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"bookings\"), booking);\n                    bookingId = bookingRef.id;\n                    console.log(\"Booking created on second attempt with ID:\", bookingId);\n                }\n            }\n            // Update the user's booking history\n            try {\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id));\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeUpdateDoc)(\"users\", user.id, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                } else {\n                    // Use standard function in development\n                    const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"users\", user.id);\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)(userRef);\n                    if (userDoc.exists()) {\n                        const userData = userDoc.data();\n                        const bookingHistory = userData.bookingHistory || [];\n                        // Only add the booking ID if it's not already in the history\n                        if (!bookingHistory.includes(bookingId)) {\n                            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)(userRef, {\n                                bookingHistory: [\n                                    ...bookingHistory,\n                                    bookingId\n                                ]\n                            });\n                            console.log(\"Updated user booking history\");\n                        }\n                    }\n                }\n            } catch (historyError) {\n                // Non-critical error, log but continue\n                console.error(\"Error updating booking history:\", historyError);\n            }\n            // Create a notification for the user\n            try {\n                const notificationData = {\n                    userId: user.id,\n                    message: \"Your ride has been booked successfully. Waiting for a driver to accept.\",\n                    type: \"info\",\n                    read: false,\n                    relatedBookingId: bookingId,\n                    createdAt: now\n                };\n                if ((0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.isFirebaseHosting)()) {\n                    // Use safe function in production\n                    await (0,_utils_firebase_helpers__WEBPACK_IMPORTED_MODULE_8__.safeAddDoc)(\"notifications\", notificationData);\n                } else {\n                    // Use standard function in development\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), notificationData);\n                }\n                console.log(\"Created notification for user\");\n            } catch (notificationError) {\n                // Non-critical error, log but continue\n                console.error(\"Error creating notification:\", notificationError);\n            }\n            // Show success notification\n            showNotification(\"Booking created successfully! Waiting for a driver to accept.\", \"success\");\n            // Reset form after successful booking\n            setPickupLocation({\n                address: \"\",\n                lat: 0,\n                lng: 0\n            });\n            setPassengers(1);\n            setEstimatedFare(0);\n            setSelectedBookingId(null);\n        } catch (error) {\n            console.error(\"Error creating booking:\", error);\n            // Provide more detailed error messages\n            let errorMessage = \"Failed to create booking. Please try again.\";\n            if (error instanceof Error) {\n                if (error.message.includes(\"network\")) {\n                    errorMessage = \"Network error. Please check your internet connection and try again.\";\n                } else if (error.message.includes(\"permission-denied\")) {\n                    errorMessage = \"Permission denied. Please log out and log back in.\";\n                } else if (error.message.includes(\"not-found\")) {\n                    errorMessage = \"Database connection error. Please refresh the page and try again.\";\n                }\n            }\n            showNotification(errorMessage, \"error\");\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!user) {\n            showNotification(\"Please log in to book a ride.\", \"error\");\n            return;\n        }\n        // Validate pickup location\n        if (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) {\n            showNotification(\"Please select a valid pickup location.\", \"error\");\n            return;\n        }\n        // Create booking directly (no payment required)\n        await createBooking();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        title: \"BaroRide - Book a Ride\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-2 sm:p-4 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900\",\n                            children: \"Book Your Ride\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm sm:text-base text-gray-600 mt-1\",\n                            children: \"Quick and easy airport transportation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 sm:mb-0\",\n                                            children: \"Pickup Location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: toggleMapSelection,\n                                                    className: \"text-xs sm:text-sm px-3 py-1 rounded-full transition-colors touch-manipulation font-medium \".concat(isSelectingOnMap ? \"text-white bg-blue-600 hover:bg-blue-700 border border-blue-600\" : \"text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 border border-blue-200\"),\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    children: isSelectingOnMap ? \"\\uD83D\\uDCDD Manual Entry\" : \"\\uD83D\\uDDFA️ Select on Map\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: getUserLocation,\n                                                    className: \"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center\",\n                                                    style: {\n                                                        touchAction: \"manipulation\"\n                                                    },\n                                                    disabled: isLoadingGps,\n                                                    children: isLoadingGps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Getting...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: \"\\uD83D\\uDCCD My Location\"\n                                                    }, void 0, false)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, this),\n                                                previousBookings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePreviousBookings,\n                                                    className: \"text-sm text-green-500 hover:text-green-700\",\n                                                    children: showPreviousBookings ? \"Hide Previous\" : \"Use Previous\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                !isSelectingOnMap ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Enter your pickup address manually\",\n                                                    className: \"w-full p-2 border rounded\",\n                                                    value: pickupLocation.address,\n                                                    onChange: (e)=>{\n                                                        setPickupLocation((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            }));\n                                                        // Clear selected booking when manually editing\n                                                        if (selectedBookingId) {\n                                                            setSelectedBookingId(null);\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pickupLocation.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: clearSelection,\n                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-4 w-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3 inline-block mr-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 19\n                                                }, this),\n                                                'Enter your address manually, click \"Use My Location\", or select it on the map'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-blue-600 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-blue-800\",\n                                                        children: \"Map Selection Mode Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-600\",\n                                                        children: \"Tap anywhere on the map below to select your pickup location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this),\n                                showPreviousBookings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 border rounded shadow-sm overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 px-3 py-2 border-b\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-700\",\n                                                children: \"Previous Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-60 overflow-y-auto\",\n                                            children: isLoadingBookings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Loading...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 21\n                                            }, this) : previousBookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-sm text-gray-600\",\n                                                children: \"No previous bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: previousBookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"p-3 hover:bg-gray-50 cursor-pointer transition-colors \".concat(selectedBookingId === booking.id ? \"bg-blue-50 border-l-4 border-blue-500\" : \"\"),\n                                                        onClick: ()=>selectPreviousBooking(booking),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: booking.pickupLocation.address\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"To: \",\n                                                                                booking.airport.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 mr-2\",\n                                                                            children: new Date(booking.updatedAt).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        selectedBookingId === booking.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-500 font-medium\",\n                                                                            children: \"Selected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, booking.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Pickup Location Map\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BasicMap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    height: \"300px\",\n                                    selectable: isSelectingOnMap,\n                                    onLocationSelected: handleLocationSelected,\n                                    initialLocation: pickupLocation.lat !== 0 ? pickupLocation : undefined\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this),\n                                pickupLocation.lat !== 0 && pickupLocation.lng !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-blue-50 border border-blue-100 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            \"Selected Pickup: \",\n                                                            pickupLocation.address\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedBookingId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                        children: \"From Previous Booking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: clearSelection,\n                                                className: \"ml-2 text-xs text-red-500 hover:text-red-700\",\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Select Airport\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedAirport.code,\n                                            onChange: (e)=>{\n                                                const airport = AIRPORTS.find((a)=>a.code === e.target.value);\n                                                if (airport) setSelectedAirport(airport);\n                                            },\n                                            className: \"w-full p-2 border rounded\",\n                                            children: AIRPORTS.map((airport)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: airport.code,\n                                                    children: airport.name\n                                                }, airport.code, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Number of Passengers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.max(1, prev - 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-l hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M20 12H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"8\",\n                                                    value: passengers,\n                                                    onChange: (e)=>setPassengers(Math.max(1, Math.min(8, parseInt(e.target.value) || 1))),\n                                                    className: \"w-full p-2 border-t border-b text-center\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setPassengers((prev)=>Math.min(8, prev + 1)),\n                                                    className: \"p-2 bg-gray-100 border rounded-r hover:bg-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M12 6v12M6 12h12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Maximum 8 passengers per ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 11\n                        }, this),\n                        estimatedFare > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-800 mb-2\",\n                                    children: \"Fare Estimate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Base fare\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 800,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"Additional passengers (\",\n                                                        passengers - 1,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"$35.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        Math.floor(10 + pickupLocation.lat * pickupLocation.lng % 100 / 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 19\n                                                }, this),\n                                                passengers > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: [\n                                                        \"$\",\n                                                        ((passengers - 1) * 5).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: \"Total estimated fare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-800\",\n                                            children: [\n                                                \"$\",\n                                                estimatedFare.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-blue-600 mt-2\",\n                                    children: \"* Actual fare may vary based on traffic, weather, and other factors.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"w-full \".concat(!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0 ? \"bg-gray-400 cursor-not-allowed\" : \"bg-blue-500 hover:bg-blue-600\", \" text-white p-3 rounded font-medium transition-colors\"),\n                                    disabled: !pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0,\n                                    children: !user ? \"Please Log In to Book a Ride\" : \"Book Ride Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 13\n                                }, this),\n                                (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-red-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm\",\n                                            children: \"Please enter or select a pickup location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 15\n                                }, this),\n                                !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-yellow-500 mr-1\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-500 text-sm\",\n                                            children: \"You need to be logged in to book a ride\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                            lineNumber: 824,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 555,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 553,\n        columnNumber: 5\n    }, this);\n}\n_s(BookRide, \"XktRhB0uZAyb6WBU6XF7HfoHKZM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification\n    ];\n});\n_c = BookRide;\n// Wrap the component with ProtectedRoute\nfunction BookRidePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        requiredRoles: [\n            \"admin\",\n            \"rider\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BookRide, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n            lineNumber: 865,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\book\\\\index.tsx\",\n        lineNumber: 864,\n        columnNumber: 5\n    }, this);\n}\n_c1 = BookRidePage;\nvar _c, _c1;\n$RefreshReg$(_c, \"BookRide\");\n$RefreshReg$(_c1, \"BookRidePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvYm9vay9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0s7QUFDVjtBQUNnRjtBQUUxRTtBQUNKO0FBQ3dCO0FBQ3NDO0FBQzlDO0FBRXpELE1BQU1zQixXQUFXO0lBQ2Y7UUFBRUMsTUFBTTtRQUFPQyxNQUFNO0lBQWdDO0NBRXREO0FBRUQsU0FBU0M7O0lBQ1AsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR3hCLDhEQUFPQTtJQUN4QixNQUFNLEVBQUV5QixnQkFBZ0IsRUFBRSxHQUFHWCw4RUFBZUE7SUFDNUMsTUFBTSxDQUFDWSxnQkFBZ0JDLGtCQUFrQixHQUFHN0IsK0NBQVFBLENBQUM7UUFDbkQ4QixTQUFTO1FBQ1RDLEtBQUs7UUFDTEMsS0FBSztJQUNQO0lBQ0EsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQUNzQixRQUFRLENBQUMsRUFBRTtJQUNsRSxNQUFNLENBQUNhLFlBQVlDLGNBQWMsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FDLGtCQUFrQkMsb0JBQW9CLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN1QyxlQUFlQyxpQkFBaUIsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3lDLGtCQUFrQkMsb0JBQW9CLEdBQUcxQywrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RFLE1BQU0sQ0FBQzJDLG1CQUFtQkMscUJBQXFCLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUM2QyxzQkFBc0JDLHdCQUF3QixHQUFHOUMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDK0MsbUJBQW1CQyxxQkFBcUIsR0FBR2hELCtDQUFRQSxDQUFnQjtJQUMxRSxNQUFNLENBQUNpRCxjQUFjQyxnQkFBZ0IsR0FBR2xELCtDQUFRQSxDQUFDO0lBRWpELGdEQUFnRDtJQUNoREMsZ0RBQVNBLENBQUM7UUFDUiwrQ0FBK0M7UUFDL0MsSUFBSXlCLE1BQU07WUFDUnlCO1FBQ0Y7SUFDRixHQUFHO1FBQUN6QjtLQUFLO0lBRVQsaUNBQWlDO0lBQ2pDLE1BQU15Qix3QkFBd0I7UUFDNUIsSUFBSSxDQUFDekIsTUFBTTtRQUVYa0IscUJBQXFCO1FBQ3JCLElBQUk7WUFDRixrQ0FBa0M7WUFDbEMsSUFBSXhCLDBFQUFpQkEsSUFBSTtnQkFDdkJnQyxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLGtGQUFrRjtZQUNsRixJQUFJQyxXQUFzQixFQUFFO1lBRTVCLElBQUlsQywwRUFBaUJBLElBQUk7Z0JBQ3ZCLCtDQUErQztnQkFDL0MsTUFBTW1DLGNBQWM7b0JBQ2xCaEQseURBQUtBLENBQUMsV0FBVyxNQUFNbUIsS0FBSzhCLEVBQUU7b0JBQzlCakQseURBQUtBLENBQUMsVUFBVSxNQUFNO29CQUN0QkUsMkRBQU9BLENBQUMsYUFBYTtvQkFDckJDLHlEQUFLQSxDQUFDO2lCQUNQO2dCQUVELE1BQU0rQyxVQUFVLE1BQU10QyxzRUFBYUEsQ0FBQyxZQUFZb0M7Z0JBRWhERCxXQUFXRyxRQUFRQyxHQUFHLENBQUNDLENBQUFBLE9BQVM7d0JBQzlCSCxJQUFJRyxLQUFLSCxFQUFFO3dCQUNYLEdBQUdHLElBQUk7d0JBQ1BDLGVBQWVELEtBQUtDLGFBQWEsR0FBRyxJQUFJQyxLQUFLRixLQUFLQyxhQUFhLElBQUksSUFBSUM7d0JBQ3ZFQyxXQUFXSCxLQUFLRyxTQUFTLEdBQUcsSUFBSUQsS0FBS0YsS0FBS0csU0FBUyxJQUFJLElBQUlEO3dCQUMzREUsV0FBV0osS0FBS0ksU0FBUyxHQUFHLElBQUlGLEtBQUtGLEtBQUtJLFNBQVMsSUFBSSxJQUFJRjt3QkFDM0QxQixZQUFZd0IsS0FBS3hCLFVBQVUsSUFBSTtvQkFDakM7WUFDRixPQUFPO2dCQUNMLDRDQUE0QztnQkFDNUMsTUFBTTZCLGdCQUFnQjFELHlEQUFLQSxDQUN6QkYsOERBQVVBLENBQUNELGdEQUFFQSxFQUFFLGFBQ2ZJLHlEQUFLQSxDQUFDLFdBQVcsTUFBTW1CLEtBQUs4QixFQUFFLEdBQzlCakQseURBQUtBLENBQUMsVUFBVSxNQUFNLGNBQ3RCRSwyREFBT0EsQ0FBQyxhQUFhLFNBQ3JCQyx5REFBS0EsQ0FBQyxHQUFHLGtDQUFrQzs7Z0JBRzdDLE1BQU11RCxnQkFBZ0IsTUFBTXpELDJEQUFPQSxDQUFDd0Q7Z0JBRXBDQyxjQUFjQyxPQUFPLENBQUMsQ0FBQ3ZEO29CQUNyQixNQUFNZ0QsT0FBT2hELElBQUlnRCxJQUFJO29CQUNyQiwrQ0FBK0M7b0JBQy9DLE1BQU1RLFVBQVU7d0JBQ2RYLElBQUk3QyxJQUFJNkMsRUFBRTt3QkFDVixHQUFHRyxJQUFJO3dCQUNQQyxlQUFlRCxLQUFLQyxhQUFhLEdBQUcsSUFBSUMsS0FBS0YsS0FBS0MsYUFBYSxJQUFJLElBQUlDO3dCQUN2RUMsV0FBV0gsS0FBS0csU0FBUyxHQUFHLElBQUlELEtBQUtGLEtBQUtHLFNBQVMsSUFBSSxJQUFJRDt3QkFDM0RFLFdBQVdKLEtBQUtJLFNBQVMsR0FBRyxJQUFJRixLQUFLRixLQUFLSSxTQUFTLElBQUksSUFBSUY7d0JBQzNEMUIsWUFBWXdCLEtBQUt4QixVQUFVLElBQUk7b0JBQ2pDO29CQUNBbUIsU0FBU2MsSUFBSSxDQUFDRDtnQkFDaEI7WUFDRjtZQUVBekIsb0JBQW9CWTtZQUNwQkYsUUFBUUMsR0FBRyxDQUFDLFdBQTJCLE9BQWhCQyxTQUFTZSxNQUFNLEVBQUM7UUFDekMsRUFBRSxPQUFPQyxPQUFPO1lBQ2RsQixRQUFRa0IsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQzQyxpQkFBaUIsa0VBQWtFO1FBQ3JGLFNBQVU7WUFDUmlCLHFCQUFxQjtRQUN2QjtJQUNGO0lBRUEsNkNBQTZDO0lBQzdDLE1BQU0yQixpQkFBaUI7UUFDckIxQyxrQkFBa0I7WUFDaEJDLFNBQVM7WUFDVEMsS0FBSztZQUNMQyxLQUFLO1FBQ1A7UUFDQWdCLHFCQUFxQjtJQUN2QjtJQUVBLGdEQUFnRDtJQUNoRCxNQUFNd0IscUJBQXFCO1FBQ3pCbEMsb0JBQW9CLENBQUNEO0lBQ3ZCO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU1vQyx5QkFBeUIsQ0FBQ0M7UUFDOUIsd0JBQXdCO1FBQ3hCLElBQUksQ0FBQ0EsWUFBWSxPQUFPQSxTQUFTM0MsR0FBRyxLQUFLLFlBQVksT0FBTzJDLFNBQVMxQyxHQUFHLEtBQUssVUFBVTtZQUNyRkwsaUJBQWlCLGdEQUFnRDtZQUNqRTtRQUNGO1FBRUEsa0VBQWtFO1FBQ2xFLElBQUkrQyxTQUFTM0MsR0FBRyxLQUFLLEtBQUsyQyxTQUFTMUMsR0FBRyxLQUFLLEdBQUc7WUFDNUNMLGlCQUFpQixtREFBbUQ7WUFDcEU7UUFDRjtRQUVBLGlDQUFpQztRQUNqQyxNQUFNZ0QsY0FBYztZQUNsQjVDLEtBQUsyQyxTQUFTM0MsR0FBRztZQUNqQkMsS0FBSzBDLFNBQVMxQyxHQUFHO1lBQ2pCRixTQUFTNEMsU0FBUzVDLE9BQU8sSUFBSTtRQUMvQjtRQUVBc0IsUUFBUUMsR0FBRyxDQUFDLCtCQUErQnNCO1FBRTNDLGVBQWU7UUFDZjlDLGtCQUFrQjhDO1FBRWxCLDhEQUE4RDtRQUM5RDNCLHFCQUFxQjtRQUNyQlYsb0JBQW9CO1FBRXBCLHNDQUFzQztRQUN0QyxNQUFNc0MsT0FBT0MsY0FBY0Y7UUFDM0JuQyxpQkFBaUJvQztRQUVqQixnQ0FBZ0M7UUFDaENqRCxpQkFBaUIsMENBQTBDO0lBQzdEO0lBRUEsNERBQTREO0lBQzVELE1BQU1tRCw0QkFBNEIsT0FBT2hEO1FBQ3ZDLElBQUksQ0FBQ0EsV0FBV0EsUUFBUWlELElBQUksR0FBR1YsTUFBTSxHQUFHLEdBQUc7WUFDekM7UUFDRjtRQUVBLElBQUk7WUFDRiw4REFBOEQ7WUFDOUQsTUFBTVcsV0FBVyxNQUFNQyxNQUNyQixxREFBaUYsT0FBNUJDLG1CQUFtQnBELFVBQVM7WUFHbkYsSUFBSWtELFNBQVNHLEVBQUUsRUFBRTtnQkFDZixNQUFNeEIsT0FBTyxNQUFNcUIsU0FBU0ksSUFBSTtnQkFDaEMsSUFBSXpCLEtBQUswQixRQUFRLElBQUkxQixLQUFLMEIsUUFBUSxDQUFDaEIsTUFBTSxHQUFHLEdBQUc7b0JBQzdDLE1BQU1pQixVQUFVM0IsS0FBSzBCLFFBQVEsQ0FBQyxFQUFFO29CQUNoQyxNQUFNLENBQUNyRCxLQUFLRCxJQUFJLEdBQUd1RCxRQUFRQyxNQUFNO29CQUVqQyw0Q0FBNEM7b0JBQzVDMUQsa0JBQWtCMkQsQ0FBQUEsT0FBUzs0QkFDekIsR0FBR0EsSUFBSTs0QkFDUHpEOzRCQUNBQzs0QkFDQUYsU0FBU3dELFFBQVFHLFVBQVUsSUFBSTNEO3dCQUNqQztvQkFFQSxzQ0FBc0M7b0JBQ3RDLE1BQU04QyxPQUFPQyxjQUFjO3dCQUFFOUM7d0JBQUtDO3dCQUFLRjtvQkFBUTtvQkFDL0NVLGlCQUFpQm9DO29CQUVqQnhCLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7d0JBQUV0Qjt3QkFBS0M7d0JBQUtGLFNBQVN3RCxRQUFRRyxVQUFVO29CQUFDO2dCQUN4RjtZQUNGO1FBQ0YsRUFBRSxPQUFPbkIsT0FBTztZQUNkbEIsUUFBUWtCLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLGdFQUFnRTtRQUNsRTtJQUNGO0lBRUEsK0JBQStCO0lBQy9CckUsZ0RBQVNBLENBQUM7UUFDUixNQUFNeUYsWUFBWUMsV0FBVztZQUMzQixJQUFJL0QsZUFBZUUsT0FBTyxJQUFJRixlQUFlRSxPQUFPLENBQUN1QyxNQUFNLEdBQUcsS0FDekR6QyxDQUFBQSxlQUFlRyxHQUFHLEtBQUssS0FBS0gsZUFBZUksR0FBRyxLQUFLLElBQUk7Z0JBQzFEOEMsMEJBQTBCbEQsZUFBZUUsT0FBTztZQUNsRDtRQUNGLEdBQUcsT0FBTyx3Q0FBd0M7UUFFbEQsT0FBTyxJQUFNOEQsYUFBYUY7SUFDNUIsR0FBRztRQUFDOUQsZUFBZUUsT0FBTztLQUFDO0lBRTNCLG1DQUFtQztJQUNuQyxNQUFNK0QseUJBQXlCO1FBQzdCL0Msd0JBQXdCLENBQUNEO0lBQzNCO0lBRUEsd0RBQXdEO0lBQ3hELE1BQU1pRCxrQkFBa0I7UUFDdEIsSUFBSSxDQUFDQyxVQUFVQyxXQUFXLEVBQUU7WUFDMUJyRSxpQkFBaUIsc0ZBQXNGO1lBQ3ZHO1FBQ0Y7UUFFQXVCLGdCQUFnQjtRQUNoQnZCLGlCQUFpQixvQ0FBb0M7UUFFckQsSUFBSTtZQUNGLDhEQUE4RDtZQUM5RCxNQUFNc0UsV0FBVyxNQUFNLElBQUlDLFFBQTZCLENBQUNDLFNBQVNDO2dCQUNoRUwsVUFBVUMsV0FBVyxDQUFDSyxrQkFBa0IsQ0FDdEMsQ0FBQ0o7b0JBQ0M3QyxRQUFRQyxHQUFHLENBQUMsMEJBQTBCNEMsU0FBU0ssTUFBTTtvQkFDckRILFFBQVFGO2dCQUNWLEdBQ0EsQ0FBQzNCO29CQUNDbEIsUUFBUWtCLEtBQUssQ0FBQyxjQUFjQSxNQUFNL0MsSUFBSSxFQUFFK0MsTUFBTWlDLE9BQU87b0JBQ3JELElBQUlDLGVBQWU7b0JBRW5CLHVDQUF1QztvQkFDdkMsSUFBSWxDLE1BQU0vQyxJQUFJLEtBQUssR0FBRzt3QkFDcEJpRixlQUFlO29CQUNqQixPQUFPLElBQUlsQyxNQUFNL0MsSUFBSSxLQUFLLEdBQUc7d0JBQzNCaUYsZUFBZTtvQkFDakIsT0FBTyxJQUFJbEMsTUFBTS9DLElBQUksS0FBSyxHQUFHO3dCQUMzQmlGLGVBQWU7b0JBQ2pCO29CQUVBSixPQUFPLElBQUlLLE1BQU1EO2dCQUNuQixHQUNBO29CQUNFRSxvQkFBb0I7b0JBQ3BCQyxTQUFTO29CQUNUQyxZQUFZO2dCQUNkO1lBRUo7WUFFQSxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdiLFNBQVNLLE1BQU07WUFDL0NsRCxRQUFRQyxHQUFHLENBQUMsb0JBQWlDeUQsT0FBYkQsVUFBUyxNQUFjLE9BQVZDO1lBRTdDLElBQUk7Z0JBQ0YsMERBQTBEO2dCQUMxRCxNQUFNOUIsV0FBVyxNQUFNQyxNQUNyQixxREFBa0U0QixPQUFiQyxXQUFVLEtBQVksT0FBVEQsVUFBUztnQkFHN0UsSUFBSSxDQUFDN0IsU0FBU0csRUFBRSxFQUFFO29CQUNoQixNQUFNLElBQUlzQixNQUFNLHdCQUF3QyxPQUFoQnpCLFNBQVMrQixNQUFNO2dCQUN6RDtnQkFFQSxNQUFNcEQsT0FBTyxNQUFNcUIsU0FBU0ksSUFBSTtnQkFDaENoQyxRQUFRQyxHQUFHLENBQUMsdUJBQXVCTTtnQkFFbkMsSUFBSTdCLFVBQVU7Z0JBQ2QsSUFBSTZCLEtBQUswQixRQUFRLElBQUkxQixLQUFLMEIsUUFBUSxDQUFDaEIsTUFBTSxHQUFHLEdBQUc7b0JBQzdDdkMsVUFBVTZCLEtBQUswQixRQUFRLENBQUMsRUFBRSxDQUFDSSxVQUFVO29CQUNyQ3JDLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0J2QjtnQkFDaEM7Z0JBRUEseUJBQXlCO2dCQUN6QixNQUFNNEMsV0FBVztvQkFDZjNDLEtBQUs4RTtvQkFDTDdFLEtBQUs4RTtvQkFDTGhGO2dCQUNGO2dCQUVBLHlCQUF5QjtnQkFDekJELGtCQUFrQjZDO2dCQUVsQix5QkFBeUI7Z0JBQ3pCMUIscUJBQXFCO2dCQUVyQixzQ0FBc0M7Z0JBQ3RDLE1BQU00QixPQUFPQyxjQUFjSDtnQkFDM0JsQyxpQkFBaUJvQztnQkFFakIsNEJBQTRCO2dCQUM1QmpELGlCQUFpQiwyREFBMkQ7WUFDOUUsRUFBRSxPQUFPcUYsZ0JBQWdCO2dCQUN2QjVELFFBQVFrQixLQUFLLENBQUMsMEJBQTBCMEM7Z0JBRXhDLGlEQUFpRDtnQkFDakQsTUFBTXRDLFdBQVc7b0JBQ2YzQyxLQUFLOEU7b0JBQ0w3RSxLQUFLOEU7b0JBQ0xoRixTQUFTO2dCQUNYO2dCQUVBRCxrQkFBa0I2QztnQkFDbEIxQixxQkFBcUI7Z0JBRXJCLE1BQU00QixPQUFPQyxjQUFjSDtnQkFDM0JsQyxpQkFBaUJvQztnQkFFakJqRCxpQkFBaUIsbUZBQW9GO1lBQ3ZHO1FBQ0YsRUFBRSxPQUFPMkMsT0FBTztZQUNkbEIsUUFBUWtCLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDM0MsaUJBQWlCMkMsaUJBQWlCbUMsUUFBUW5DLE1BQU1pQyxPQUFPLEdBQUcsMEVBQTBFO1FBQ3RJLFNBQVU7WUFDUnJELGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU0rRCx3QkFBd0IsQ0FBQzlDO1FBQzdCLHlCQUF5QjtRQUN6QnRDLGtCQUFrQnNDLFFBQVF2QyxjQUFjO1FBRXhDLDBDQUEwQztRQUMxQyxNQUFNc0YsVUFBVTVGLFNBQVM2RixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUU3RixJQUFJLEtBQUs0QyxRQUFRK0MsT0FBTyxDQUFDM0YsSUFBSTtRQUNsRSxJQUFJMkYsU0FBUztZQUNYaEYsbUJBQW1CZ0Y7UUFDckI7UUFFQSw4QkFBOEI7UUFDOUJsRSxxQkFBcUJtQixRQUFRWCxFQUFFO1FBRS9CLHNDQUFzQztRQUN0Q1Ysd0JBQXdCO0lBQzFCO0lBRUEsNERBQTREO0lBQzVELE1BQU0rQixnQkFBZ0I7WUFBQ0gsNEVBQVc5QztRQUNoQyxrRkFBa0Y7UUFDbEYsb0ZBQW9GO1FBQ3BGLElBQUk4QyxTQUFTM0MsR0FBRyxLQUFLLEtBQUsyQyxTQUFTMUMsR0FBRyxLQUFLLEdBQUc7WUFDNUMsT0FBTztRQUNUO1FBRUEsTUFBTXFGLFdBQVc7UUFDakIsbUVBQW1FO1FBQ25FLHVGQUF1RjtRQUN2RixNQUFNQyxPQUFPLFNBQVV2RixHQUFHLEdBQUcyQyxTQUFTMUMsR0FBRyxHQUFJO1FBQzdDLE1BQU11RixlQUFlQyxLQUFLQyxLQUFLLENBQUMsS0FBS0gsT0FBTztRQUU1QyxrQ0FBa0M7UUFDbEMsTUFBTUksZ0JBQWdCLENBQUN2RixhQUFhLEtBQUs7UUFFekMsT0FBT2tGLFdBQVdFLGVBQWVHO0lBQ25DO0lBRUEseUVBQXlFO0lBQ3pFekgsZ0RBQVNBLENBQUM7UUFDUixJQUFJMkIsZUFBZUcsR0FBRyxLQUFLLEtBQUtILGVBQWVJLEdBQUcsS0FBSyxHQUFHO1lBQ3hELE1BQU00QyxPQUFPQyxjQUFjakQ7WUFDM0JZLGlCQUFpQm9DO1FBQ25CO0lBQ0YsR0FBRztRQUFDaEQ7UUFBZ0JPO0tBQVc7SUFFL0IsZ0RBQWdEO0lBQ2hELE1BQU13RixnQkFBZ0I7UUFDcEIsSUFBSSxDQUFDakcsTUFBTTtZQUNUQyxpQkFBaUIsaUNBQWlDO1lBQ2xEO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsNEJBQTRCO1lBQzVCQSxpQkFBaUIsNEJBQTRCO1lBRTdDLHlDQUF5QztZQUN6QyxNQUFNaUcsTUFBTSxJQUFJL0Q7WUFFaEIscURBQXFEO1lBQ3JELE1BQU1NLFVBQTRCO2dCQUNoQzBELFNBQVNuRyxLQUFLOEIsRUFBRTtnQkFDaEI1QixnQkFBZ0I7b0JBQ2RFLFNBQVNGLGVBQWVFLE9BQU87b0JBQy9CQyxLQUFLSCxlQUFlRyxHQUFHO29CQUN2QkMsS0FBS0osZUFBZUksR0FBRztnQkFDekI7Z0JBQ0FrRixTQUFTO29CQUNQMUYsTUFBTVMsZ0JBQWdCVCxJQUFJO29CQUMxQkQsTUFBTVUsZ0JBQWdCVixJQUFJO2dCQUM1QjtnQkFDQXdGLFFBQVE7Z0JBQ1JuQyxNQUFNckMsZ0JBQWdCLElBQUlBLGdCQUFnQnNDO2dCQUMxQzFDLFlBQVlBO2dCQUNaMkIsV0FBVzhEO2dCQUNYN0QsV0FBVzZEO1lBQ2I7WUFFQXhFLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JjO1lBRTNDLHVFQUF1RTtZQUN2RSxJQUFJMkQ7WUFFSixJQUFJMUcsMEVBQWlCQSxJQUFJO2dCQUN2Qiw4Q0FBOEM7Z0JBQzlDZ0MsUUFBUUMsR0FBRyxDQUFDO2dCQUNaeUUsWUFBWSxNQUFNN0csbUVBQVVBLENBQUMsWUFBWWtEO2dCQUN6Q2YsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnlFO1lBQzFDLE9BQU87Z0JBQ0wsdUNBQXVDO2dCQUN2QyxJQUFJO29CQUNGLE1BQU1DLGFBQWEsTUFBTTFILDBEQUFNQSxDQUFDRCw4REFBVUEsQ0FBQ0QsZ0RBQUVBLEVBQUUsYUFBYWdFO29CQUM1RDJELFlBQVlDLFdBQVd2RSxFQUFFO29CQUN6QkosUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnlFO2dCQUMxQyxFQUFFLE9BQU94RCxPQUFPO29CQUNkbEIsUUFBUWtCLEtBQUssQ0FBQywyQkFBMkJBO29CQUN6QzNDLGlCQUFpQixnQ0FBZ0M7b0JBRWpELHFDQUFxQztvQkFDckMsTUFBTSxJQUFJdUUsUUFBUUMsQ0FBQUEsVUFBV1IsV0FBV1EsU0FBUztvQkFDakQsTUFBTTRCLGFBQWEsTUFBTTFILDBEQUFNQSxDQUFDRCw4REFBVUEsQ0FBQ0QsZ0RBQUVBLEVBQUUsYUFBYWdFO29CQUM1RDJELFlBQVlDLFdBQVd2RSxFQUFFO29CQUN6QkosUUFBUUMsR0FBRyxDQUFDLDhDQUE4Q3lFO2dCQUM1RDtZQUNGO1lBRUEsb0NBQW9DO1lBQ3BDLElBQUk7Z0JBQ0YsSUFBSTFHLDBFQUFpQkEsSUFBSTtvQkFDdkIsa0NBQWtDO29CQUNsQyxNQUFNNEcsVUFBVSxNQUFNcEgsMERBQU1BLENBQUNELHVEQUFHQSxDQUFDUixnREFBRUEsRUFBRSxTQUFTdUIsS0FBSzhCLEVBQUU7b0JBRXJELElBQUl3RSxRQUFRQyxNQUFNLElBQUk7d0JBQ3BCLE1BQU1DLFdBQVdGLFFBQVFyRSxJQUFJO3dCQUM3QixNQUFNd0UsaUJBQWlCRCxTQUFTQyxjQUFjLElBQUksRUFBRTt3QkFFcEQsNkRBQTZEO3dCQUM3RCxJQUFJLENBQUNBLGVBQWVDLFFBQVEsQ0FBQ04sWUFBWTs0QkFDdkMsTUFBTTVHLHNFQUFhQSxDQUFDLFNBQVNRLEtBQUs4QixFQUFFLEVBQUU7Z0NBQ3BDMkUsZ0JBQWdCO3VDQUFJQTtvQ0FBZ0JMO2lDQUFVOzRCQUNoRDs0QkFDQTFFLFFBQVFDLEdBQUcsQ0FBQzt3QkFDZDtvQkFDRjtnQkFDRixPQUFPO29CQUNMLHVDQUF1QztvQkFDdkMsTUFBTWdGLFVBQVUxSCx1REFBR0EsQ0FBQ1IsZ0RBQUVBLEVBQUUsU0FBU3VCLEtBQUs4QixFQUFFO29CQUN4QyxNQUFNd0UsVUFBVSxNQUFNcEgsMERBQU1BLENBQUN5SDtvQkFFN0IsSUFBSUwsUUFBUUMsTUFBTSxJQUFJO3dCQUNwQixNQUFNQyxXQUFXRixRQUFRckUsSUFBSTt3QkFDN0IsTUFBTXdFLGlCQUFpQkQsU0FBU0MsY0FBYyxJQUFJLEVBQUU7d0JBRXBELDZEQUE2RDt3QkFDN0QsSUFBSSxDQUFDQSxlQUFlQyxRQUFRLENBQUNOLFlBQVk7NEJBQ3ZDLE1BQU1qSCw2REFBU0EsQ0FBQ3dILFNBQVM7Z0NBQ3ZCRixnQkFBZ0I7dUNBQUlBO29DQUFnQkw7aUNBQVU7NEJBQ2hEOzRCQUNBMUUsUUFBUUMsR0FBRyxDQUFDO3dCQUNkO29CQUNGO2dCQUNGO1lBQ0YsRUFBRSxPQUFPaUYsY0FBYztnQkFDckIsdUNBQXVDO2dCQUN2Q2xGLFFBQVFrQixLQUFLLENBQUMsbUNBQW1DZ0U7WUFDbkQ7WUFFQSxxQ0FBcUM7WUFDckMsSUFBSTtnQkFDRixNQUFNQyxtQkFBbUI7b0JBQ3ZCQyxRQUFROUcsS0FBSzhCLEVBQUU7b0JBQ2YrQyxTQUFTO29CQUNUa0MsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsa0JBQWtCYjtvQkFDbEJoRSxXQUFXOEQ7Z0JBQ2I7Z0JBRUEsSUFBSXhHLDBFQUFpQkEsSUFBSTtvQkFDdkIsa0NBQWtDO29CQUNsQyxNQUFNSCxtRUFBVUEsQ0FBQyxpQkFBaUJzSDtnQkFDcEMsT0FBTztvQkFDTCx1Q0FBdUM7b0JBQ3ZDLE1BQU1sSSwwREFBTUEsQ0FBQ0QsOERBQVVBLENBQUNELGdEQUFFQSxFQUFFLGtCQUFrQm9JO2dCQUNoRDtnQkFDQW5GLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT3VGLG1CQUFtQjtnQkFDMUIsdUNBQXVDO2dCQUN2Q3hGLFFBQVFrQixLQUFLLENBQUMsZ0NBQWdDc0U7WUFDaEQ7WUFFQSw0QkFBNEI7WUFDNUJqSCxpQkFBaUIsaUVBQWlFO1lBRWxGLHNDQUFzQztZQUN0Q0Usa0JBQWtCO2dCQUNoQkMsU0FBUztnQkFDVEMsS0FBSztnQkFDTEMsS0FBSztZQUNQO1lBQ0FJLGNBQWM7WUFDZEksaUJBQWlCO1lBQ2pCUSxxQkFBcUI7UUFFdkIsRUFBRSxPQUFPc0IsT0FBTztZQUNkbEIsUUFBUWtCLEtBQUssQ0FBQywyQkFBMkJBO1lBRXpDLHVDQUF1QztZQUN2QyxJQUFJa0MsZUFBZTtZQUVuQixJQUFJbEMsaUJBQWlCbUMsT0FBTztnQkFDMUIsSUFBSW5DLE1BQU1pQyxPQUFPLENBQUM2QixRQUFRLENBQUMsWUFBWTtvQkFDckM1QixlQUFlO2dCQUNqQixPQUFPLElBQUlsQyxNQUFNaUMsT0FBTyxDQUFDNkIsUUFBUSxDQUFDLHNCQUFzQjtvQkFDdEQ1QixlQUFlO2dCQUNqQixPQUFPLElBQUlsQyxNQUFNaUMsT0FBTyxDQUFDNkIsUUFBUSxDQUFDLGNBQWM7b0JBQzlDNUIsZUFBZTtnQkFDakI7WUFDRjtZQUVBN0UsaUJBQWlCNkUsY0FBYztRQUNqQztJQUNGO0lBRUEsTUFBTXFDLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFDaEIsSUFBSSxDQUFDckgsTUFBTTtZQUNUQyxpQkFBaUIsaUNBQWlDO1lBQ2xEO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSSxDQUFDQyxlQUFlRSxPQUFPLElBQUlGLGVBQWVHLEdBQUcsS0FBSyxLQUFLSCxlQUFlSSxHQUFHLEtBQUssR0FBRztZQUNuRkwsaUJBQWlCLDBDQUEwQztZQUMzRDtRQUNGO1FBRUEsZ0RBQWdEO1FBQ2hELE1BQU1nRztJQUNSO0lBRUEscUJBQ0UsOERBQUM1RywwREFBTUE7UUFBQ2lJLE9BQU07a0JBRVosNEVBQUNDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUEwRDs7Ozs7O3NDQUN4RSw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQTBDOzs7Ozs7Ozs7Ozs7OEJBR3pELDhEQUFDRztvQkFBS0MsVUFBVVQ7b0JBQWNLLFdBQVU7O3NDQUN0Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUFNTCxXQUFVO3NEQUF1RDs7Ozs7O3NEQUN4RSw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFDQ2YsTUFBSztvREFDTGdCLFNBQVNqRjtvREFDVDBFLFdBQVcsOEZBSVYsT0FIQzdHLG1CQUNJLG9FQUNBO29EQUVOcUgsT0FBTzt3REFBRUMsYUFBYTtvREFBZTs4REFFcEN0SCxtQkFBbUIsOEJBQW9COzs7Ozs7OERBRTFDLDhEQUFDbUg7b0RBQ0NmLE1BQUs7b0RBQ0xnQixTQUFTM0Q7b0RBQ1RvRCxXQUFVO29EQUNWUSxPQUFPO3dEQUFFQyxhQUFhO29EQUFlO29EQUNyQ0MsVUFBVTNHOzhEQUVUQSw2QkFDQzs7MEVBQ0UsOERBQUM0RztnRUFBSVgsV0FBVTtnRUFBZ0VZLE9BQU07Z0VBQTZCQyxNQUFLO2dFQUFPQyxTQUFROztrRkFDcEksOERBQUNDO3dFQUFPZixXQUFVO3dFQUFhZ0IsSUFBRzt3RUFBS0MsSUFBRzt3RUFBS0MsR0FBRTt3RUFBS0MsUUFBTzt3RUFBZUMsYUFBWTs7Ozs7O2tGQUN4Riw4REFBQ0M7d0VBQUtyQixXQUFVO3dFQUFhYSxNQUFLO3dFQUFlUyxHQUFFOzs7Ozs7Ozs7Ozs7NERBQy9DOztxRkFJUjtrRUFBRTs7Ozs7OztnREFLTC9ILGlCQUFpQjRCLE1BQU0sR0FBRyxtQkFDekIsOERBQUNtRjtvREFDQ2YsTUFBSztvREFDTGdCLFNBQVM1RDtvREFDVHFELFdBQVU7OERBRVRyRyx1QkFBdUIsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTWpELENBQUNSLGlDQUNBLDhEQUFDNEc7O3NEQUNDLDhEQUFDQTs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN1QjtvREFDQ2hDLE1BQUs7b0RBQ0xpQyxhQUFZO29EQUNaeEIsV0FBVTtvREFDVnlCLE9BQU8vSSxlQUFlRSxPQUFPO29EQUM3QjhJLFVBQVUsQ0FBQzlCO3dEQUNUakgsa0JBQWtCMkQsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFMUQsU0FBU2dILEVBQUUrQixNQUFNLENBQUNGLEtBQUs7NERBQUM7d0RBQzlELCtDQUErQzt3REFDL0MsSUFBSTVILG1CQUFtQjs0REFDckJDLHFCQUFxQjt3REFDdkI7b0RBQ0Y7Ozs7OztnREFFRHBCLGVBQWVFLE9BQU8sa0JBQ3JCLDhEQUFDMEg7b0RBQ0NmLE1BQUs7b0RBQ0xnQixTQUFTbEY7b0RBQ1QyRSxXQUFVOzhEQUVWLDRFQUFDVzt3REFBSUMsT0FBTTt3REFBNkJaLFdBQVU7d0RBQVVhLE1BQUs7d0RBQU9DLFNBQVE7d0RBQVlLLFFBQU87a0VBQ2pHLDRFQUFDRTs0REFBS08sZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFULGFBQWE7NERBQUdFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSzdFLDhEQUFDcEI7NENBQUVGLFdBQVU7OzhEQUNYLDhEQUFDVztvREFBSVgsV0FBVTtvREFBNEJhLE1BQUs7b0RBQU9NLFFBQU87b0RBQWVMLFNBQVE7b0RBQVlGLE9BQU07OERBQ3JHLDRFQUFDUzt3REFBS08sZUFBYzt3REFBUUMsZ0JBQWU7d0RBQVFULGFBQVk7d0RBQUlFLEdBQUU7Ozs7Ozs7Ozs7O2dEQUNqRTs7Ozs7Ozs7Ozs7O3lEQUtWLDhEQUFDdkI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1c7Z0RBQUlYLFdBQVU7Z0RBQTZCYSxNQUFLO2dEQUFPTSxRQUFPO2dEQUFlTCxTQUFRO2dEQUFZRixPQUFNOztrRUFDdEcsOERBQUNTO3dEQUFLTyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUVQsYUFBWTt3REFBSUUsR0FBRTs7Ozs7O2tFQUNyRSw4REFBQ0Q7d0RBQUtPLGVBQWM7d0RBQVFDLGdCQUFlO3dEQUFRVCxhQUFZO3dEQUFJRSxHQUFFOzs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDdkI7O2tFQUNDLDhEQUFDRzt3REFBRUYsV0FBVTtrRUFBb0M7Ozs7OztrRUFDakQsOERBQUNFO3dEQUFFRixXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTzVDckcsc0NBQ0MsOERBQUNvRztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDOEI7Z0RBQUc5QixXQUFVOzBEQUFvQzs7Ozs7Ozs7Ozs7c0RBRXBELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWnZHLGtDQUNDLDhEQUFDc0c7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDK0I7d0RBQUsvQixXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7dURBRXhDekcsaUJBQWlCNEIsTUFBTSxLQUFLLGtCQUM5Qiw4REFBQzRFO2dEQUFJQyxXQUFVOzBEQUF3Qzs7Ozs7cUVBSXZELDhEQUFDZ0M7Z0RBQUdoQyxXQUFVOzBEQUNYekcsaUJBQWlCaUIsR0FBRyxDQUFDLENBQUNTLHdCQUNyQiw4REFBQ2dIO3dEQUVDakMsV0FBVyx5REFFVixPQURDbkcsc0JBQXNCb0IsUUFBUVgsRUFBRSxHQUFHLDBDQUEwQzt3REFFL0VpRyxTQUFTLElBQU14QyxzQkFBc0I5QztrRUFFckMsNEVBQUM4RTs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEOztzRkFDQyw4REFBQ0c7NEVBQUVGLFdBQVU7c0ZBQXFDL0UsUUFBUXZDLGNBQWMsQ0FBQ0UsT0FBTzs7Ozs7O3NGQUNoRiw4REFBQ3NIOzRFQUFFRixXQUFVOztnRkFBd0I7Z0ZBQUsvRSxRQUFRK0MsT0FBTyxDQUFDMUYsSUFBSTs7Ozs7Ozs7Ozs7Ozs4RUFFaEUsOERBQUN5SDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUNaLElBQUlyRixLQUFLTSxRQUFRSixTQUFTLEVBQUVxSCxrQkFBa0I7Ozs7Ozt3RUFFaERySSxzQkFBc0JvQixRQUFRWCxFQUFFLGtCQUMvQiw4REFBQ3lIOzRFQUFLL0IsV0FBVTtzRkFBb0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REFoQnJEL0UsUUFBUVgsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQTZCL0IsOERBQUN5Rjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM4QjtvQ0FBRzlCLFdBQVU7OENBQTJCOzs7Ozs7OENBQ3pDLDhEQUFDcEksNERBQVFBO29DQUNQdUssUUFBTztvQ0FDUEMsWUFBWWpKO29DQUNaa0osb0JBQW9COUc7b0NBQ3BCK0csaUJBQWlCNUosZUFBZUcsR0FBRyxLQUFLLElBQUlILGlCQUFpQjZKOzs7Ozs7Z0NBRTlEN0osZUFBZUcsR0FBRyxLQUFLLEtBQUtILGVBQWVJLEdBQUcsS0FBSyxtQkFDbEQsOERBQUNpSDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFO3dEQUFFRixXQUFVOzs0REFBc0I7NERBQWtCdEgsZUFBZUUsT0FBTzs7Ozs7OztvREFDMUVpQixtQ0FDQyw4REFBQ2tJO3dEQUFLL0IsV0FBVTtrRUFBMkQ7Ozs7Ozs7Ozs7OzswREFLL0UsOERBQUNNO2dEQUNDZixNQUFLO2dEQUNMZ0IsU0FBU2xGO2dEQUNUMkUsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUVQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7O3NEQUNDLDhEQUFDTTs0Q0FBTUwsV0FBVTtzREFBK0M7Ozs7OztzREFDaEUsOERBQUN3Qzs0Q0FDQ2YsT0FBTzFJLGdCQUFnQlYsSUFBSTs0Q0FDM0JxSixVQUFVLENBQUM5QjtnREFDVCxNQUFNNUIsVUFBVTVGLFNBQVM2RixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUU3RixJQUFJLEtBQUt1SCxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO2dEQUM1RCxJQUFJekQsU0FBU2hGLG1CQUFtQmdGOzRDQUNsQzs0Q0FDQWdDLFdBQVU7c0RBRVQ1SCxTQUFTb0MsR0FBRyxDQUFDd0QsQ0FBQUEsd0JBQ1osOERBQUN5RTtvREFBMEJoQixPQUFPekQsUUFBUTNGLElBQUk7OERBQzNDMkYsUUFBUTFGLElBQUk7bURBREYwRixRQUFRM0YsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPL0IsOERBQUMwSDs7c0RBQ0MsOERBQUNNOzRDQUFNTCxXQUFVO3NEQUErQzs7Ozs7O3NEQUNoRSw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTtvREFDQ2YsTUFBSztvREFDTGdCLFNBQVMsSUFBTXJILGNBQWNvRCxDQUFBQSxPQUFRZ0MsS0FBS29FLEdBQUcsQ0FBQyxHQUFHcEcsT0FBTztvREFDeEQwRCxXQUFVOzhEQUVWLDRFQUFDVzt3REFBSVgsV0FBVTt3REFBd0JhLE1BQUs7d0RBQU9NLFFBQU87d0RBQWVMLFNBQVE7d0RBQVlGLE9BQU07a0VBQ2pHLDRFQUFDUzs0REFBS08sZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFULGFBQVk7NERBQUlFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR3pFLDhEQUFDQztvREFDQ2hDLE1BQUs7b0RBQ0xvRCxLQUFJO29EQUNKRCxLQUFJO29EQUNKakIsT0FBT3hJO29EQUNQeUksVUFBVSxDQUFDOUIsSUFBTTFHLGNBQWNvRixLQUFLb0UsR0FBRyxDQUFDLEdBQUdwRSxLQUFLcUUsR0FBRyxDQUFDLEdBQUdDLFNBQVNoRCxFQUFFK0IsTUFBTSxDQUFDRixLQUFLLEtBQUs7b0RBQ25GekIsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDTTtvREFDQ2YsTUFBSztvREFDTGdCLFNBQVMsSUFBTXJILGNBQWNvRCxDQUFBQSxPQUFRZ0MsS0FBS3FFLEdBQUcsQ0FBQyxHQUFHckcsT0FBTztvREFDeEQwRCxXQUFVOzhEQUVWLDRFQUFDVzt3REFBSVgsV0FBVTt3REFBd0JhLE1BQUs7d0RBQU9NLFFBQU87d0RBQWVMLFNBQVE7d0RBQVlGLE9BQU07a0VBQ2pHLDRFQUFDUzs0REFBS08sZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFULGFBQVk7NERBQUlFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSTNFLDhEQUFDcEI7NENBQUVGLFdBQVU7c0RBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBSzdDM0csZ0JBQWdCLG1CQUNmLDhEQUFDMEc7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDOEI7b0NBQUc5QixXQUFVOzhDQUF5Qzs7Ozs7OzhDQUN2RCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNHO29EQUFFRixXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQXdCOzs7Ozs7Z0RBQ3BDL0csYUFBYSxtQkFDWiw4REFBQ2lIO29EQUFFRixXQUFVOzt3REFBd0I7d0RBQXdCL0csYUFBYTt3REFBRTs7Ozs7Ozs7Ozs7OztzREFHaEYsOERBQUM4Rzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFFRixXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ0U7b0RBQUVGLFdBQVU7O3dEQUF3Qjt3REFBRTFCLEtBQUtDLEtBQUssQ0FBQyxLQUFLLGVBQWlCMUYsR0FBRyxHQUFHSCxlQUFlSSxHQUFHLEdBQUksTUFBTyxHQUFHK0osT0FBTyxDQUFDOzs7Ozs7O2dEQUNySDVKLGFBQWEsbUJBQ1osOERBQUNpSDtvREFBRUYsV0FBVTs7d0RBQXdCO3dEQUFHLEVBQUMvRyxhQUFhLEtBQUssR0FBRzRKLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJNUUsOERBQUM5QztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUFFRixXQUFVO3NEQUE0Qjs7Ozs7O3NEQUN6Qyw4REFBQ0U7NENBQUVGLFdBQVU7O2dEQUE0QjtnREFBRTNHLGNBQWN3SixPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7OENBRW5FLDhEQUFDM0M7b0NBQUVGLFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7c0NBTTlDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNNO29DQUNDZixNQUFLO29DQUNMUyxXQUFXLFVBSVYsT0FIQyxDQUFDdEgsZUFBZUUsT0FBTyxJQUFJRixlQUFlRyxHQUFHLEtBQUssS0FBS0gsZUFBZUksR0FBRyxLQUFLLElBQzFFLG1DQUNBLGlDQUNMO29DQUNENEgsVUFBVSxDQUFDaEksZUFBZUUsT0FBTyxJQUFJRixlQUFlRyxHQUFHLEtBQUssS0FBS0gsZUFBZUksR0FBRyxLQUFLOzhDQUV2RixDQUFDTixPQUFPLGlDQUFpQzs7Ozs7O2dDQUcxQyxFQUFDRSxlQUFlRSxPQUFPLElBQUlGLGVBQWVHLEdBQUcsS0FBSyxLQUFLSCxlQUFlSSxHQUFHLEtBQUssb0JBQzlFLDhEQUFDaUg7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDVzs0Q0FBSVgsV0FBVTs0Q0FBNEJhLE1BQUs7NENBQU9NLFFBQU87NENBQWVMLFNBQVE7NENBQVlGLE9BQU07c0RBQ3JHLDRFQUFDUztnREFBS08sZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFULGFBQVk7Z0RBQUlFLEdBQUU7Ozs7Ozs7Ozs7O3NEQUV2RSw4REFBQ3BCOzRDQUFFRixXQUFVO3NEQUF1Qjs7Ozs7Ozs7Ozs7O2dDQUl2QyxDQUFDeEgsc0JBQ0EsOERBQUN1SDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNXOzRDQUFJWCxXQUFVOzRDQUErQmEsTUFBSzs0Q0FBT00sUUFBTzs0Q0FBZUwsU0FBUTs0Q0FBWUYsT0FBTTtzREFDeEcsNEVBQUNTO2dEQUFLTyxlQUFjO2dEQUFRQyxnQkFBZTtnREFBUVQsYUFBWTtnREFBSUUsR0FBRTs7Ozs7Ozs7Ozs7c0RBRXZFLDhEQUFDcEI7NENBQUVGLFdBQVU7c0RBQTBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF2RDtHQTEwQlN6SDs7UUFDVXZCLDBEQUFPQTtRQUNLYywwRUFBZUE7OztLQUZyQ1M7QUE0MEJULHlDQUF5QztBQUMxQixTQUFTdUs7SUFDdEIscUJBQ0UsOERBQUMzSyxrRUFBY0E7UUFBQzRLLGVBQWU7WUFBQztZQUFTO1NBQVE7a0JBQy9DLDRFQUFDeEs7Ozs7Ozs7Ozs7QUFHUDtNQU53QnVLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9ib29rL2luZGV4LnRzeD9kM2EwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBkYiB9IGZyb20gJ0AvZmlyZWJhc2UvY29uZmlnJztcbmltcG9ydCB7IGNvbGxlY3Rpb24sIGFkZERvYywgcXVlcnksIHdoZXJlLCBnZXREb2NzLCBvcmRlckJ5LCBsaW1pdCwgZG9jLCBnZXREb2MsIHVwZGF0ZURvYyB9IGZyb20gJ2ZpcmViYXNlL2ZpcmVzdG9yZSc7XG5pbXBvcnQgdHlwZSB7IEJvb2tpbmcgfSBmcm9tICdAL3R5cGVzL2Jvb2tpbmcnO1xuaW1wb3J0IEJhc2ljTWFwIGZyb20gJ0AvY29tcG9uZW50cy9CYXNpY01hcCc7XG5pbXBvcnQgTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9MYXlvdXQnO1xuaW1wb3J0IHsgdXNlTm90aWZpY2F0aW9uIH0gZnJvbSAnQC9jb250ZXh0cy9Ob3RpZmljYXRpb25Db250ZXh0JztcbmltcG9ydCB7IHNhZmVBZGREb2MsIHNhZmVVcGRhdGVEb2MsIHNhZmVRdWVyeURvY3MsIGlzRmlyZWJhc2VIb3N0aW5nIH0gZnJvbSAnQC91dGlscy9maXJlYmFzZS1oZWxwZXJzJztcbmltcG9ydCBQcm90ZWN0ZWRSb3V0ZSBmcm9tICdAL2NvbXBvbmVudHMvUHJvdGVjdGVkUm91dGUnO1xuXG5jb25zdCBBSVJQT1JUUyA9IFtcbiAgeyBjb2RlOiAnR01CJywgbmFtZTogJ0dhbWJlbGEgSW50ZXJuYXRpb25hbCBBaXJwb3J0JyB9LFxuICAvLyBBZGQgbW9yZSBhaXJwb3J0cyBhcyBuZWVkZWRcbl07XG5cbmZ1bmN0aW9uIEJvb2tSaWRlKCkge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgeyBzaG93Tm90aWZpY2F0aW9uIH0gPSB1c2VOb3RpZmljYXRpb24oKTtcbiAgY29uc3QgW3BpY2t1cExvY2F0aW9uLCBzZXRQaWNrdXBMb2NhdGlvbl0gPSB1c2VTdGF0ZSh7XG4gICAgYWRkcmVzczogJycsXG4gICAgbGF0OiAwLFxuICAgIGxuZzogMCxcbiAgfSk7XG4gIGNvbnN0IFtzZWxlY3RlZEFpcnBvcnQsIHNldFNlbGVjdGVkQWlycG9ydF0gPSB1c2VTdGF0ZShBSVJQT1JUU1swXSk7XG4gIGNvbnN0IFtwYXNzZW5nZXJzLCBzZXRQYXNzZW5nZXJzXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbaXNTZWxlY3RpbmdPbk1hcCwgc2V0SXNTZWxlY3RpbmdPbk1hcF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlc3RpbWF0ZWRGYXJlLCBzZXRFc3RpbWF0ZWRGYXJlXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbcHJldmlvdXNCb29raW5ncywgc2V0UHJldmlvdXNCb29raW5nc10gPSB1c2VTdGF0ZTxCb29raW5nW10+KFtdKTtcbiAgY29uc3QgW2lzTG9hZGluZ0Jvb2tpbmdzLCBzZXRJc0xvYWRpbmdCb29raW5nc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93UHJldmlvdXNCb29raW5ncywgc2V0U2hvd1ByZXZpb3VzQm9va2luZ3NdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VsZWN0ZWRCb29raW5nSWQsIHNldFNlbGVjdGVkQm9va2luZ0lkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nR3BzLCBzZXRJc0xvYWRpbmdHcHNdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEZldGNoIHByZXZpb3VzIGJvb2tpbmdzIHdoZW4gY29tcG9uZW50IG1vdW50c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEZldGNoIHByZXZpb3VzIGJvb2tpbmdzIGlmIHVzZXIgaXMgbG9nZ2VkIGluXG4gICAgaWYgKHVzZXIpIHtcbiAgICAgIGZldGNoUHJldmlvdXNCb29raW5ncygpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICAvLyBGZXRjaCB1c2VyJ3MgcHJldmlvdXMgYm9va2luZ3NcbiAgY29uc3QgZmV0Y2hQcmV2aW91c0Jvb2tpbmdzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuXG4gICAgc2V0SXNMb2FkaW5nQm9va2luZ3ModHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIExvZyBmb3IgZGVidWdnaW5nIGluIHByb2R1Y3Rpb25cbiAgICAgIGlmIChpc0ZpcmViYXNlSG9zdGluZygpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBwcmV2aW91cyBib29raW5ncyBpbiBGaXJlYmFzZSBob3N0aW5nIGVudmlyb25tZW50Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFVzZSBvdXIgc2FmZSBxdWVyeSBmdW5jdGlvbiBpZiB3ZSdyZSBpbiBwcm9kdWN0aW9uLCBvdGhlcndpc2UgdXNlIHJlZ3VsYXIgcXVlcnlcbiAgICAgIGxldCBib29raW5nczogQm9va2luZ1tdID0gW107XG5cbiAgICAgIGlmIChpc0ZpcmViYXNlSG9zdGluZygpKSB7XG4gICAgICAgIC8vIFVzZSB0aGUgc2FmZSBxdWVyeSBmdW5jdGlvbiB3aXRoIHJldHJ5IGxvZ2ljXG4gICAgICAgIGNvbnN0IGNvbnN0cmFpbnRzID0gW1xuICAgICAgICAgIHdoZXJlKCdyaWRlcklkJywgJz09JywgdXNlci5pZCksXG4gICAgICAgICAgd2hlcmUoJ3N0YXR1cycsICc9PScsICdjb21wbGV0ZWQnKSxcbiAgICAgICAgICBvcmRlckJ5KCd1cGRhdGVkQXQnLCAnZGVzYycpLFxuICAgICAgICAgIGxpbWl0KDUpXG4gICAgICAgIF07XG5cbiAgICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IHNhZmVRdWVyeURvY3MoJ2Jvb2tpbmdzJywgY29uc3RyYWludHMpO1xuXG4gICAgICAgIGJvb2tpbmdzID0gcmVzdWx0cy5tYXAoZGF0YSA9PiAoe1xuICAgICAgICAgIGlkOiBkYXRhLmlkLFxuICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgc2NoZWR1bGVkVGltZTogZGF0YS5zY2hlZHVsZWRUaW1lID8gbmV3IERhdGUoZGF0YS5zY2hlZHVsZWRUaW1lKSA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgY3JlYXRlZEF0OiBkYXRhLmNyZWF0ZWRBdCA/IG5ldyBEYXRlKGRhdGEuY3JlYXRlZEF0KSA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgdXBkYXRlZEF0OiBkYXRhLnVwZGF0ZWRBdCA/IG5ldyBEYXRlKGRhdGEudXBkYXRlZEF0KSA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgcGFzc2VuZ2VyczogZGF0YS5wYXNzZW5nZXJzIHx8IDEsIC8vIERlZmF1bHQgdG8gMSBwYXNzZW5nZXIgZm9yIG9sZGVyIGJvb2tpbmdzXG4gICAgICAgIH0gYXMgQm9va2luZykpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gUmVndWxhciBxdWVyeSBmb3IgZGV2ZWxvcG1lbnQgZW52aXJvbm1lbnRcbiAgICAgICAgY29uc3QgYm9va2luZ3NRdWVyeSA9IHF1ZXJ5KFxuICAgICAgICAgIGNvbGxlY3Rpb24oZGIsICdib29raW5ncycpLFxuICAgICAgICAgIHdoZXJlKCdyaWRlcklkJywgJz09JywgdXNlci5pZCksXG4gICAgICAgICAgd2hlcmUoJ3N0YXR1cycsICc9PScsICdjb21wbGV0ZWQnKSxcbiAgICAgICAgICBvcmRlckJ5KCd1cGRhdGVkQXQnLCAnZGVzYycpLFxuICAgICAgICAgIGxpbWl0KDUpIC8vIExpbWl0IHRvIDUgbW9zdCByZWNlbnQgYm9va2luZ3NcbiAgICAgICAgKTtcblxuICAgICAgICBjb25zdCBxdWVyeVNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhib29raW5nc1F1ZXJ5KTtcblxuICAgICAgICBxdWVyeVNuYXBzaG90LmZvckVhY2goKGRvYykgPT4ge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBkb2MuZGF0YSgpO1xuICAgICAgICAgIC8vIENvbnZlcnQgRmlyZXN0b3JlIHRpbWVzdGFtcHMgdG8gRGF0ZSBvYmplY3RzXG4gICAgICAgICAgY29uc3QgYm9va2luZyA9IHtcbiAgICAgICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgICAgICAuLi5kYXRhLFxuICAgICAgICAgICAgc2NoZWR1bGVkVGltZTogZGF0YS5zY2hlZHVsZWRUaW1lID8gbmV3IERhdGUoZGF0YS5zY2hlZHVsZWRUaW1lKSA6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IGRhdGEuY3JlYXRlZEF0ID8gbmV3IERhdGUoZGF0YS5jcmVhdGVkQXQpIDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogZGF0YS51cGRhdGVkQXQgPyBuZXcgRGF0ZShkYXRhLnVwZGF0ZWRBdCkgOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgcGFzc2VuZ2VyczogZGF0YS5wYXNzZW5nZXJzIHx8IDEsIC8vIERlZmF1bHQgdG8gMSBwYXNzZW5nZXIgZm9yIG9sZGVyIGJvb2tpbmdzXG4gICAgICAgICAgfSBhcyBCb29raW5nO1xuICAgICAgICAgIGJvb2tpbmdzLnB1c2goYm9va2luZyk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBzZXRQcmV2aW91c0Jvb2tpbmdzKGJvb2tpbmdzKTtcbiAgICAgIGNvbnNvbGUubG9nKGBGZXRjaGVkICR7Ym9va2luZ3MubGVuZ3RofSBwcmV2aW91cyBib29raW5nc2ApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcmV2aW91cyBib29raW5nczonLCBlcnJvcik7XG4gICAgICBzaG93Tm90aWZpY2F0aW9uKCdDb3VsZCBub3QgbG9hZCB5b3VyIHByZXZpb3VzIGJvb2tpbmdzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicsICd3YXJuaW5nJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZ0Jvb2tpbmdzKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ2xlYXIgc2VsZWN0ZWQgYm9va2luZyBhbmQgcGlja3VwIGxvY2F0aW9uXG4gIGNvbnN0IGNsZWFyU2VsZWN0aW9uID0gKCkgPT4ge1xuICAgIHNldFBpY2t1cExvY2F0aW9uKHtcbiAgICAgIGFkZHJlc3M6ICcnLFxuICAgICAgbGF0OiAwLFxuICAgICAgbG5nOiAwLFxuICAgIH0pO1xuICAgIHNldFNlbGVjdGVkQm9va2luZ0lkKG51bGwpO1xuICB9O1xuXG4gIC8vIFRvZ2dsZSBiZXR3ZWVuIG1hcCBzZWxlY3Rpb24gYW5kIG1hbnVhbCBlbnRyeVxuICBjb25zdCB0b2dnbGVNYXBTZWxlY3Rpb24gPSAoKSA9PiB7XG4gICAgc2V0SXNTZWxlY3RpbmdPbk1hcCghaXNTZWxlY3RpbmdPbk1hcCk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIGxvY2F0aW9uIHNlbGVjdGlvbiBmcm9tIG1hcFxuICBjb25zdCBoYW5kbGVMb2NhdGlvblNlbGVjdGVkID0gKGxvY2F0aW9uOiB7IGxhdDogbnVtYmVyOyBsbmc6IG51bWJlcjsgYWRkcmVzcz86IHN0cmluZyB9KSA9PiB7XG4gICAgLy8gVmFsaWRhdGUgdGhlIGxvY2F0aW9uXG4gICAgaWYgKCFsb2NhdGlvbiB8fCB0eXBlb2YgbG9jYXRpb24ubGF0ICE9PSAnbnVtYmVyJyB8fCB0eXBlb2YgbG9jYXRpb24ubG5nICE9PSAnbnVtYmVyJykge1xuICAgICAgc2hvd05vdGlmaWNhdGlvbignSW52YWxpZCBsb2NhdGlvbiBzZWxlY3RlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nLCAnZXJyb3InKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBjb29yZGluYXRlcyBhcmUgcmVhc29uYWJsZSAobm90IDAsMCBvciBleHRyZW1lIHZhbHVlcylcbiAgICBpZiAobG9jYXRpb24ubGF0ID09PSAwICYmIGxvY2F0aW9uLmxuZyA9PT0gMCkge1xuICAgICAgc2hvd05vdGlmaWNhdGlvbignSW52YWxpZCBjb29yZGluYXRlcyBzZWxlY3RlZC4gUGxlYXNlIHRyeSBhZ2Fpbi4nLCAnZXJyb3InKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgYSB2YWxpZCBsb2NhdGlvbiBvYmplY3RcbiAgICBjb25zdCBuZXdMb2NhdGlvbiA9IHtcbiAgICAgIGxhdDogbG9jYXRpb24ubGF0LFxuICAgICAgbG5nOiBsb2NhdGlvbi5sbmcsXG4gICAgICBhZGRyZXNzOiBsb2NhdGlvbi5hZGRyZXNzIHx8ICdTZWxlY3RlZCBsb2NhdGlvbidcbiAgICB9O1xuXG4gICAgY29uc29sZS5sb2coJ0xvY2F0aW9uIHNlbGVjdGVkIGZyb20gbWFwOicsIG5ld0xvY2F0aW9uKTtcblxuICAgIC8vIFVwZGF0ZSBzdGF0ZVxuICAgIHNldFBpY2t1cExvY2F0aW9uKG5ld0xvY2F0aW9uKTtcblxuICAgIC8vIENsZWFyIHNlbGVjdGVkIGJvb2tpbmcgc2luY2Ugd2UncmUgc2VsZWN0aW5nIGEgbmV3IGxvY2F0aW9uXG4gICAgc2V0U2VsZWN0ZWRCb29raW5nSWQobnVsbCk7XG4gICAgc2V0SXNTZWxlY3RpbmdPbk1hcChmYWxzZSk7XG5cbiAgICAvLyBDYWxjdWxhdGUgZmFyZSBmb3IgdGhlIG5ldyBsb2NhdGlvblxuICAgIGNvbnN0IGZhcmUgPSBjYWxjdWxhdGVGYXJlKG5ld0xvY2F0aW9uKTtcbiAgICBzZXRFc3RpbWF0ZWRGYXJlKGZhcmUpO1xuXG4gICAgLy8gU2hvdyBjb25maXJtYXRpb24gdG8gdGhlIHVzZXJcbiAgICBzaG93Tm90aWZpY2F0aW9uKCdQaWNrdXAgbG9jYXRpb24gc2VsZWN0ZWQgc3VjY2Vzc2Z1bGx5IScsICdzdWNjZXNzJyk7XG4gIH07XG5cbiAgLy8gVmFsaWRhdGUgYW5kIGdlb2NvZGUgbWFudWFsIGFkZHJlc3MgZW50cnkgd2l0aCBkZWJvdW5jaW5nXG4gIGNvbnN0IHZhbGlkYXRlQW5kR2VvY29kZUFkZHJlc3MgPSBhc3luYyAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFhZGRyZXNzIHx8IGFkZHJlc3MudHJpbSgpLmxlbmd0aCA8IDMpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gVXNlIE1hcGJveCBHZW9jb2RpbmcgQVBJIHRvIGdldCBjb29yZGluYXRlcyBmb3IgdGhlIGFkZHJlc3NcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXG4gICAgICAgIGBodHRwczovL2FwaS5tYXBib3guY29tL2dlb2NvZGluZy92NS9tYXBib3gucGxhY2VzLyR7ZW5jb2RlVVJJQ29tcG9uZW50KGFkZHJlc3MpfS5qc29uP2FjY2Vzc190b2tlbj1way5leUoxSWpvaU9USTBOREV6T0RJNUlpd2lZU0k2SW1OdE9YWmlOSE4zZVRCbWNtVXlhbkl5ZVdGeGFuaGlaVzhpZlEuUThOb2NrQlpGN0k3LXNiN1ROMk84QSZsaW1pdD0xYFxuICAgICAgKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGlmIChkYXRhLmZlYXR1cmVzICYmIGRhdGEuZmVhdHVyZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IGZlYXR1cmUgPSBkYXRhLmZlYXR1cmVzWzBdO1xuICAgICAgICAgIGNvbnN0IFtsbmcsIGxhdF0gPSBmZWF0dXJlLmNlbnRlcjtcblxuICAgICAgICAgIC8vIFVwZGF0ZSBsb2NhdGlvbiB3aXRoIGdlb2NvZGVkIGNvb3JkaW5hdGVzXG4gICAgICAgICAgc2V0UGlja3VwTG9jYXRpb24ocHJldiA9PiAoe1xuICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgIGxhdCxcbiAgICAgICAgICAgIGxuZyxcbiAgICAgICAgICAgIGFkZHJlc3M6IGZlYXR1cmUucGxhY2VfbmFtZSB8fCBhZGRyZXNzXG4gICAgICAgICAgfSkpO1xuXG4gICAgICAgICAgLy8gQ2FsY3VsYXRlIGZhcmUgZm9yIHRoZSBuZXcgbG9jYXRpb25cbiAgICAgICAgICBjb25zdCBmYXJlID0gY2FsY3VsYXRlRmFyZSh7IGxhdCwgbG5nLCBhZGRyZXNzIH0pO1xuICAgICAgICAgIHNldEVzdGltYXRlZEZhcmUoZmFyZSk7XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygnQWRkcmVzcyBnZW9jb2RlZCBzdWNjZXNzZnVsbHk6JywgeyBsYXQsIGxuZywgYWRkcmVzczogZmVhdHVyZS5wbGFjZV9uYW1lIH0pO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlb2NvZGluZyBhZGRyZXNzOicsIGVycm9yKTtcbiAgICAgIC8vIERvbid0IHNob3cgZXJyb3IgdG8gdXNlciBmb3IgZ2VvY29kaW5nIGZhaWx1cmVzIGR1cmluZyB0eXBpbmdcbiAgICB9XG4gIH07XG5cbiAgLy8gRGVib3VuY2VkIGFkZHJlc3MgdmFsaWRhdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKHBpY2t1cExvY2F0aW9uLmFkZHJlc3MgJiYgcGlja3VwTG9jYXRpb24uYWRkcmVzcy5sZW5ndGggPiAzICYmXG4gICAgICAgICAgKHBpY2t1cExvY2F0aW9uLmxhdCA9PT0gMCB8fCBwaWNrdXBMb2NhdGlvbi5sbmcgPT09IDApKSB7XG4gICAgICAgIHZhbGlkYXRlQW5kR2VvY29kZUFkZHJlc3MocGlja3VwTG9jYXRpb24uYWRkcmVzcyk7XG4gICAgICB9XG4gICAgfSwgMTAwMCk7IC8vIFdhaXQgMSBzZWNvbmQgYWZ0ZXIgdXNlciBzdG9wcyB0eXBpbmdcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgfSwgW3BpY2t1cExvY2F0aW9uLmFkZHJlc3NdKTtcblxuICAvLyBUb2dnbGUgc2hvd2luZyBwcmV2aW91cyBib29raW5nc1xuICBjb25zdCB0b2dnbGVQcmV2aW91c0Jvb2tpbmdzID0gKCkgPT4ge1xuICAgIHNldFNob3dQcmV2aW91c0Jvb2tpbmdzKCFzaG93UHJldmlvdXNCb29raW5ncyk7XG4gIH07XG5cbiAgLy8gR2V0IHVzZXIncyBjdXJyZW50IGxvY2F0aW9uIHVzaW5nIEdQUyAtIG1hbnVhbCBhY3Rpb25cbiAgY29uc3QgZ2V0VXNlckxvY2F0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghbmF2aWdhdG9yLmdlb2xvY2F0aW9uKSB7XG4gICAgICBzaG93Tm90aWZpY2F0aW9uKCdHZW9sb2NhdGlvbiBpcyBub3Qgc3VwcG9ydGVkIGJ5IHlvdXIgYnJvd3Nlci4gUGxlYXNlIGVudGVyIHlvdXIgbG9jYXRpb24gbWFudWFsbHkuJywgJ2Vycm9yJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNMb2FkaW5nR3BzKHRydWUpO1xuICAgIHNob3dOb3RpZmljYXRpb24oJ0dldHRpbmcgeW91ciBjdXJyZW50IGxvY2F0aW9uLi4uJywgJ2luZm8nKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgY3VycmVudCBwb3NpdGlvbiB3aXRoIGJldHRlciB0aW1lb3V0IGFuZCBlcnJvciBoYW5kbGluZ1xuICAgICAgY29uc3QgcG9zaXRpb24gPSBhd2FpdCBuZXcgUHJvbWlzZTxHZW9sb2NhdGlvblBvc2l0aW9uPigocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIG5hdmlnYXRvci5nZW9sb2NhdGlvbi5nZXRDdXJyZW50UG9zaXRpb24oXG4gICAgICAgICAgKHBvc2l0aW9uKSA9PiB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnR1BTIHBvc2l0aW9uIG9idGFpbmVkOicsIHBvc2l0aW9uLmNvb3Jkcyk7XG4gICAgICAgICAgICByZXNvbHZlKHBvc2l0aW9uKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIChlcnJvcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignR1BTIGVycm9yOicsIGVycm9yLmNvZGUsIGVycm9yLm1lc3NhZ2UpO1xuICAgICAgICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICdVbmFibGUgdG8gcmV0cmlldmUgeW91ciBsb2NhdGlvbi4nO1xuXG4gICAgICAgICAgICAvLyBQcm92aWRlIG1vcmUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZXNcbiAgICAgICAgICAgIGlmIChlcnJvci5jb2RlID09PSAxKSB7IC8vIFBFUk1JU1NJT05fREVOSUVEXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdMb2NhdGlvbiBhY2Nlc3MgZGVuaWVkLiBQbGVhc2UgZW5hYmxlIGxvY2F0aW9uIHNlcnZpY2VzIGluIHlvdXIgYnJvd3NlciBzZXR0aW5ncy4nO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChlcnJvci5jb2RlID09PSAyKSB7IC8vIFBPU0lUSU9OX1VOQVZBSUxBQkxFXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdZb3VyIGN1cnJlbnQgcG9zaXRpb24gaXMgdW5hdmFpbGFibGUuIFBsZWFzZSB0cnkgYWdhaW4gbGF0ZXIuJztcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZXJyb3IuY29kZSA9PT0gMykgeyAvLyBUSU1FT1VUXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdMb2NhdGlvbiByZXF1ZXN0IHRpbWVkIG91dC4gUGxlYXNlIHRyeSBhZ2Fpbi4nO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZWplY3QobmV3IEVycm9yKGVycm9yTWVzc2FnZSkpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgZW5hYmxlSGlnaEFjY3VyYWN5OiB0cnVlLFxuICAgICAgICAgICAgdGltZW91dDogMTAwMDAsIC8vIEluY3JlYXNlZCB0aW1lb3V0IGZvciBiZXR0ZXIgcmVsaWFiaWxpdHlcbiAgICAgICAgICAgIG1heGltdW1BZ2U6IDBcbiAgICAgICAgICB9XG4gICAgICAgICk7XG4gICAgICB9KTtcblxuICAgICAgY29uc3QgeyBsYXRpdHVkZSwgbG9uZ2l0dWRlIH0gPSBwb3NpdGlvbi5jb29yZHM7XG4gICAgICBjb25zb2xlLmxvZyhgR1BTIGNvb3JkaW5hdGVzOiAke2xhdGl0dWRlfSwgJHtsb25naXR1ZGV9YCk7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIEdldCBhZGRyZXNzIGZyb20gY29vcmRpbmF0ZXMgdXNpbmcgTWFwYm94IEdlb2NvZGluZyBBUElcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcbiAgICAgICAgICBgaHR0cHM6Ly9hcGkubWFwYm94LmNvbS9nZW9jb2RpbmcvdjUvbWFwYm94LnBsYWNlcy8ke2xvbmdpdHVkZX0sJHtsYXRpdHVkZX0uanNvbj9hY2Nlc3NfdG9rZW49cGsuZXlKMUlqb2lPVEkwTkRFek9ESTVJaXdpWVNJNkltTnRPWFppTkhOM2VUQm1jbVV5YW5JeWVXRnhhbmhpWlc4aWZRLlE4Tm9ja0JaRjdJNy1zYjdUTjJPOEFgXG4gICAgICAgICk7XG5cbiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgR2VvY29kaW5nIEFQSSBlcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zb2xlLmxvZygnR2VvY29kaW5nIHJlc3BvbnNlOicsIGRhdGEpO1xuXG4gICAgICAgIGxldCBhZGRyZXNzID0gJ1lvdXIgY3VycmVudCBsb2NhdGlvbic7XG4gICAgICAgIGlmIChkYXRhLmZlYXR1cmVzICYmIGRhdGEuZmVhdHVyZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGFkZHJlc3MgPSBkYXRhLmZlYXR1cmVzWzBdLnBsYWNlX25hbWU7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0FkZHJlc3MgZm91bmQ6JywgYWRkcmVzcyk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBDcmVhdGUgbG9jYXRpb24gb2JqZWN0XG4gICAgICAgIGNvbnN0IGxvY2F0aW9uID0ge1xuICAgICAgICAgIGxhdDogbGF0aXR1ZGUsXG4gICAgICAgICAgbG5nOiBsb25naXR1ZGUsXG4gICAgICAgICAgYWRkcmVzc1xuICAgICAgICB9O1xuXG4gICAgICAgIC8vIFVwZGF0ZSBwaWNrdXAgbG9jYXRpb25cbiAgICAgICAgc2V0UGlja3VwTG9jYXRpb24obG9jYXRpb24pO1xuXG4gICAgICAgIC8vIENsZWFyIHNlbGVjdGVkIGJvb2tpbmdcbiAgICAgICAgc2V0U2VsZWN0ZWRCb29raW5nSWQobnVsbCk7XG5cbiAgICAgICAgLy8gQ2FsY3VsYXRlIGZhcmUgZm9yIHRoZSBuZXcgbG9jYXRpb25cbiAgICAgICAgY29uc3QgZmFyZSA9IGNhbGN1bGF0ZUZhcmUobG9jYXRpb24pO1xuICAgICAgICBzZXRFc3RpbWF0ZWRGYXJlKGZhcmUpO1xuXG4gICAgICAgIC8vIFNob3cgc3VjY2VzcyBub3RpZmljYXRpb25cbiAgICAgICAgc2hvd05vdGlmaWNhdGlvbignWW91ciBjdXJyZW50IGxvY2F0aW9uIGhhcyBiZWVuIHNldCBhcyB0aGUgcGlja3VwIHBvaW50LicsICdzdWNjZXNzJyk7XG4gICAgICB9IGNhdGNoIChnZW9jb2RpbmdFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGFkZHJlc3M6JywgZ2VvY29kaW5nRXJyb3IpO1xuXG4gICAgICAgIC8vIFN0aWxsIHNldCB0aGUgbG9jYXRpb24gZXZlbiBpZiBnZW9jb2RpbmcgZmFpbHNcbiAgICAgICAgY29uc3QgbG9jYXRpb24gPSB7XG4gICAgICAgICAgbGF0OiBsYXRpdHVkZSxcbiAgICAgICAgICBsbmc6IGxvbmdpdHVkZSxcbiAgICAgICAgICBhZGRyZXNzOiAnWW91ciBjdXJyZW50IGxvY2F0aW9uJ1xuICAgICAgICB9O1xuXG4gICAgICAgIHNldFBpY2t1cExvY2F0aW9uKGxvY2F0aW9uKTtcbiAgICAgICAgc2V0U2VsZWN0ZWRCb29raW5nSWQobnVsbCk7XG5cbiAgICAgICAgY29uc3QgZmFyZSA9IGNhbGN1bGF0ZUZhcmUobG9jYXRpb24pO1xuICAgICAgICBzZXRFc3RpbWF0ZWRGYXJlKGZhcmUpO1xuXG4gICAgICAgIHNob3dOb3RpZmljYXRpb24oJ0xvY2F0aW9uIHNldCwgYnV0IHdlIGNvdWxkblxcJ3QgZ2V0IHlvdXIgZXhhY3QgYWRkcmVzcy4gWW91IGNhbiBlZGl0IGl0IG1hbnVhbGx5LicsICd3YXJuaW5nJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgbG9jYXRpb246JywgZXJyb3IpO1xuICAgICAgc2hvd05vdGlmaWNhdGlvbihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmFibGUgdG8gcmV0cmlldmUgeW91ciBsb2NhdGlvbi4gUGxlYXNlIHRyeSBhZ2FpbiBvciBzZWxlY3QgbWFudWFsbHkuJywgJ2Vycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZ0dwcyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIFNlbGVjdCBhIHByZXZpb3VzIGJvb2tpbmdcbiAgY29uc3Qgc2VsZWN0UHJldmlvdXNCb29raW5nID0gKGJvb2tpbmc6IEJvb2tpbmcpID0+IHtcbiAgICAvLyBVcGRhdGUgcGlja3VwIGxvY2F0aW9uXG4gICAgc2V0UGlja3VwTG9jYXRpb24oYm9va2luZy5waWNrdXBMb2NhdGlvbik7XG5cbiAgICAvLyBVcGRhdGUgYWlycG9ydCBpZiBpdCBleGlzdHMgaW4gb3VyIGxpc3RcbiAgICBjb25zdCBhaXJwb3J0ID0gQUlSUE9SVFMuZmluZChhID0+IGEuY29kZSA9PT0gYm9va2luZy5haXJwb3J0LmNvZGUpO1xuICAgIGlmIChhaXJwb3J0KSB7XG4gICAgICBzZXRTZWxlY3RlZEFpcnBvcnQoYWlycG9ydCk7XG4gICAgfVxuXG4gICAgLy8gU2V0IHRoZSBzZWxlY3RlZCBib29raW5nIElEXG4gICAgc2V0U2VsZWN0ZWRCb29raW5nSWQoYm9va2luZy5pZCk7XG5cbiAgICAvLyBIaWRlIHRoZSBwcmV2aW91cyBib29raW5ncyBkcm9wZG93blxuICAgIHNldFNob3dQcmV2aW91c0Jvb2tpbmdzKGZhbHNlKTtcbiAgfTtcblxuICAvLyBDYWxjdWxhdGUgZmFyZSBiYXNlZCBvbiBkaXN0YW5jZSBhbmQgbnVtYmVyIG9mIHBhc3NlbmdlcnNcbiAgY29uc3QgY2FsY3VsYXRlRmFyZSA9IChsb2NhdGlvbiA9IHBpY2t1cExvY2F0aW9uKSA9PiB7XG4gICAgLy8gSW4gYSByZWFsIGFwcCwgeW91IHdvdWxkIGNhbGN1bGF0ZSBiYXNlZCBvbiBkaXN0YW5jZSBiZXR3ZWVuIHBpY2t1cCBhbmQgYWlycG9ydFxuICAgIC8vIEZvciB0aGlzIGV4YW1wbGUsIHdlJ2xsIHVzZSBhIGJhc2UgZmFyZSBwbHVzIGEgcmFuZG9tIGFtb3VudCBiYXNlZCBvbiBjb29yZGluYXRlc1xuICAgIGlmIChsb2NhdGlvbi5sYXQgPT09IDAgfHwgbG9jYXRpb24ubG5nID09PSAwKSB7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG5cbiAgICBjb25zdCBiYXNlRmFyZSA9IDM1O1xuICAgIC8vIFVzZSB0aGUgY29vcmRpbmF0ZXMgdG8gY3JlYXRlIGEgc29tZXdoYXQgcmVhbGlzdGljIHZhcmlhYmxlIGZhcmVcbiAgICAvLyBUaGlzIGlzIGp1c3QgZm9yIGRlbW9uc3RyYXRpb24gLSBpbiBhIHJlYWwgYXBwIHlvdSdkIHVzZSBhY3R1YWwgZGlzdGFuY2UgY2FsY3VsYXRpb25cbiAgICBjb25zdCBzZWVkID0gKGxvY2F0aW9uLmxhdCAqIGxvY2F0aW9uLmxuZykgJSAxMDA7XG4gICAgY29uc3QgZGlzdGFuY2VGYXJlID0gTWF0aC5mbG9vcigxMCArIHNlZWQgLyA1KTtcblxuICAgIC8vIEFkZCAkNSBwZXIgYWRkaXRpb25hbCBwYXNzZW5nZXJcbiAgICBjb25zdCBwYXNzZW5nZXJGYXJlID0gKHBhc3NlbmdlcnMgLSAxKSAqIDU7XG5cbiAgICByZXR1cm4gYmFzZUZhcmUgKyBkaXN0YW5jZUZhcmUgKyBwYXNzZW5nZXJGYXJlO1xuICB9O1xuXG4gIC8vIFVwZGF0ZSBlc3RpbWF0ZWQgZmFyZSB3aGVuIHBpY2t1cCBsb2NhdGlvbiBvciBwYXNzZW5nZXJzIGNvdW50IGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocGlja3VwTG9jYXRpb24ubGF0ICE9PSAwICYmIHBpY2t1cExvY2F0aW9uLmxuZyAhPT0gMCkge1xuICAgICAgY29uc3QgZmFyZSA9IGNhbGN1bGF0ZUZhcmUocGlja3VwTG9jYXRpb24pO1xuICAgICAgc2V0RXN0aW1hdGVkRmFyZShmYXJlKTtcbiAgICB9XG4gIH0sIFtwaWNrdXBMb2NhdGlvbiwgcGFzc2VuZ2Vyc10pO1xuXG4gIC8vIENyZWF0ZSBib29raW5nIGRpcmVjdGx5IChubyBwYXltZW50IHJlcXVpcmVkKVxuICBjb25zdCBjcmVhdGVCb29raW5nID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikge1xuICAgICAgc2hvd05vdGlmaWNhdGlvbignUGxlYXNlIGxvZyBpbiB0byBib29rIGEgcmlkZS4nLCAnZXJyb3InKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gU2hvdyBsb2FkaW5nIG5vdGlmaWNhdGlvblxuICAgICAgc2hvd05vdGlmaWNhdGlvbignQ3JlYXRpbmcgeW91ciBib29raW5nLi4uJywgJ2luZm8nKTtcblxuICAgICAgLy8gQ3JlYXRlIHRpbWVzdGFtcCBvYmplY3RzIGZvciBGaXJlc3RvcmVcbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG5cbiAgICAgIC8vIENyZWF0ZSB0aGUgYm9va2luZyBvYmplY3Qgd2l0aCBhbGwgcmVxdWlyZWQgZmllbGRzXG4gICAgICBjb25zdCBib29raW5nOiBQYXJ0aWFsPEJvb2tpbmc+ID0ge1xuICAgICAgICByaWRlcklkOiB1c2VyLmlkLFxuICAgICAgICBwaWNrdXBMb2NhdGlvbjoge1xuICAgICAgICAgIGFkZHJlc3M6IHBpY2t1cExvY2F0aW9uLmFkZHJlc3MsXG4gICAgICAgICAgbGF0OiBwaWNrdXBMb2NhdGlvbi5sYXQsXG4gICAgICAgICAgbG5nOiBwaWNrdXBMb2NhdGlvbi5sbmdcbiAgICAgICAgfSxcbiAgICAgICAgYWlycG9ydDoge1xuICAgICAgICAgIG5hbWU6IHNlbGVjdGVkQWlycG9ydC5uYW1lLFxuICAgICAgICAgIGNvZGU6IHNlbGVjdGVkQWlycG9ydC5jb2RlXG4gICAgICAgIH0sXG4gICAgICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgICAgICBmYXJlOiBlc3RpbWF0ZWRGYXJlID4gMCA/IGVzdGltYXRlZEZhcmUgOiBjYWxjdWxhdGVGYXJlKCksXG4gICAgICAgIHBhc3NlbmdlcnM6IHBhc3NlbmdlcnMsXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ0NyZWF0aW5nIGJvb2tpbmcgd2l0aCBkYXRhOicsIGJvb2tpbmcpO1xuXG4gICAgICAvLyBBZGQgdGhlIGJvb2tpbmcgdG8gRmlyZXN0b3JlIHdpdGggb3VyIHNhZmUgZnVuY3Rpb24gaWYgaW4gcHJvZHVjdGlvblxuICAgICAgbGV0IGJvb2tpbmdJZDogc3RyaW5nO1xuXG4gICAgICBpZiAoaXNGaXJlYmFzZUhvc3RpbmcoKSkge1xuICAgICAgICAvLyBVc2Ugc2FmZSBmdW5jdGlvbiB3aXRoIGJ1aWx0LWluIHJldHJ5IGxvZ2ljXG4gICAgICAgIGNvbnNvbGUubG9nKCdVc2luZyBzYWZlIGZ1bmN0aW9uIHRvIGNyZWF0ZSBib29raW5nIGluIHByb2R1Y3Rpb24nKTtcbiAgICAgICAgYm9va2luZ0lkID0gYXdhaXQgc2FmZUFkZERvYygnYm9va2luZ3MnLCBib29raW5nKTtcbiAgICAgICAgY29uc29sZS5sb2coJ0Jvb2tpbmcgY3JlYXRlZCB3aXRoIElEOicsIGJvb2tpbmdJZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBVc2Ugc3RhbmRhcmQgZnVuY3Rpb24gaW4gZGV2ZWxvcG1lbnRcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBib29raW5nUmVmID0gYXdhaXQgYWRkRG9jKGNvbGxlY3Rpb24oZGIsICdib29raW5ncycpLCBib29raW5nKTtcbiAgICAgICAgICBib29raW5nSWQgPSBib29raW5nUmVmLmlkO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdCb29raW5nIGNyZWF0ZWQgd2l0aCBJRDonLCBib29raW5nSWQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGJvb2tpbmc6JywgZXJyb3IpO1xuICAgICAgICAgIHNob3dOb3RpZmljYXRpb24oJ1JldHJ5aW5nIGJvb2tpbmcgY3JlYXRpb24uLi4nLCAnaW5mbycpO1xuXG4gICAgICAgICAgLy8gU2Vjb25kIGF0dGVtcHQgYWZ0ZXIgYSBzaG9ydCBkZWxheVxuICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG4gICAgICAgICAgY29uc3QgYm9va2luZ1JlZiA9IGF3YWl0IGFkZERvYyhjb2xsZWN0aW9uKGRiLCAnYm9va2luZ3MnKSwgYm9va2luZyk7XG4gICAgICAgICAgYm9va2luZ0lkID0gYm9va2luZ1JlZi5pZDtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQm9va2luZyBjcmVhdGVkIG9uIHNlY29uZCBhdHRlbXB0IHdpdGggSUQ6JywgYm9va2luZ0lkKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgdGhlIHVzZXIncyBib29raW5nIGhpc3RvcnlcbiAgICAgIHRyeSB7XG4gICAgICAgIGlmIChpc0ZpcmViYXNlSG9zdGluZygpKSB7XG4gICAgICAgICAgLy8gVXNlIHNhZmUgZnVuY3Rpb24gaW4gcHJvZHVjdGlvblxuICAgICAgICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCAndXNlcnMnLCB1c2VyLmlkKSk7XG5cbiAgICAgICAgICBpZiAodXNlckRvYy5leGlzdHMoKSkge1xuICAgICAgICAgICAgY29uc3QgdXNlckRhdGEgPSB1c2VyRG9jLmRhdGEoKTtcbiAgICAgICAgICAgIGNvbnN0IGJvb2tpbmdIaXN0b3J5ID0gdXNlckRhdGEuYm9va2luZ0hpc3RvcnkgfHwgW107XG5cbiAgICAgICAgICAgIC8vIE9ubHkgYWRkIHRoZSBib29raW5nIElEIGlmIGl0J3Mgbm90IGFscmVhZHkgaW4gdGhlIGhpc3RvcnlcbiAgICAgICAgICAgIGlmICghYm9va2luZ0hpc3RvcnkuaW5jbHVkZXMoYm9va2luZ0lkKSkge1xuICAgICAgICAgICAgICBhd2FpdCBzYWZlVXBkYXRlRG9jKCd1c2VycycsIHVzZXIuaWQsIHtcbiAgICAgICAgICAgICAgICBib29raW5nSGlzdG9yeTogWy4uLmJvb2tpbmdIaXN0b3J5LCBib29raW5nSWRdXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXBkYXRlZCB1c2VyIGJvb2tpbmcgaGlzdG9yeScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBVc2Ugc3RhbmRhcmQgZnVuY3Rpb24gaW4gZGV2ZWxvcG1lbnRcbiAgICAgICAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCAndXNlcnMnLCB1c2VyLmlkKTtcbiAgICAgICAgICBjb25zdCB1c2VyRG9jID0gYXdhaXQgZ2V0RG9jKHVzZXJSZWYpO1xuXG4gICAgICAgICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gdXNlckRvYy5kYXRhKCk7XG4gICAgICAgICAgICBjb25zdCBib29raW5nSGlzdG9yeSA9IHVzZXJEYXRhLmJvb2tpbmdIaXN0b3J5IHx8IFtdO1xuXG4gICAgICAgICAgICAvLyBPbmx5IGFkZCB0aGUgYm9va2luZyBJRCBpZiBpdCdzIG5vdCBhbHJlYWR5IGluIHRoZSBoaXN0b3J5XG4gICAgICAgICAgICBpZiAoIWJvb2tpbmdIaXN0b3J5LmluY2x1ZGVzKGJvb2tpbmdJZCkpIHtcbiAgICAgICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICAgICAgICBib29raW5nSGlzdG9yeTogWy4uLmJvb2tpbmdIaXN0b3J5LCBib29raW5nSWRdXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXBkYXRlZCB1c2VyIGJvb2tpbmcgaGlzdG9yeScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoaGlzdG9yeUVycm9yKSB7XG4gICAgICAgIC8vIE5vbi1jcml0aWNhbCBlcnJvciwgbG9nIGJ1dCBjb250aW51ZVxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBib29raW5nIGhpc3Rvcnk6JywgaGlzdG9yeUVycm9yKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIGEgbm90aWZpY2F0aW9uIGZvciB0aGUgdXNlclxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgbm90aWZpY2F0aW9uRGF0YSA9IHtcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgbWVzc2FnZTogJ1lvdXIgcmlkZSBoYXMgYmVlbiBib29rZWQgc3VjY2Vzc2Z1bGx5LiBXYWl0aW5nIGZvciBhIGRyaXZlciB0byBhY2NlcHQuJyxcbiAgICAgICAgICB0eXBlOiAnaW5mbycsXG4gICAgICAgICAgcmVhZDogZmFsc2UsXG4gICAgICAgICAgcmVsYXRlZEJvb2tpbmdJZDogYm9va2luZ0lkLFxuICAgICAgICAgIGNyZWF0ZWRBdDogbm93XG4gICAgICAgIH07XG5cbiAgICAgICAgaWYgKGlzRmlyZWJhc2VIb3N0aW5nKCkpIHtcbiAgICAgICAgICAvLyBVc2Ugc2FmZSBmdW5jdGlvbiBpbiBwcm9kdWN0aW9uXG4gICAgICAgICAgYXdhaXQgc2FmZUFkZERvYygnbm90aWZpY2F0aW9ucycsIG5vdGlmaWNhdGlvbkRhdGEpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFVzZSBzdGFuZGFyZCBmdW5jdGlvbiBpbiBkZXZlbG9wbWVudFxuICAgICAgICAgIGF3YWl0IGFkZERvYyhjb2xsZWN0aW9uKGRiLCAnbm90aWZpY2F0aW9ucycpLCBub3RpZmljYXRpb25EYXRhKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zb2xlLmxvZygnQ3JlYXRlZCBub3RpZmljYXRpb24gZm9yIHVzZXInKTtcbiAgICAgIH0gY2F0Y2ggKG5vdGlmaWNhdGlvbkVycm9yKSB7XG4gICAgICAgIC8vIE5vbi1jcml0aWNhbCBlcnJvciwgbG9nIGJ1dCBjb250aW51ZVxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBub3RpZmljYXRpb246Jywgbm90aWZpY2F0aW9uRXJyb3IpO1xuICAgICAgfVxuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3Mgbm90aWZpY2F0aW9uXG4gICAgICBzaG93Tm90aWZpY2F0aW9uKCdCb29raW5nIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5ISBXYWl0aW5nIGZvciBhIGRyaXZlciB0byBhY2NlcHQuJywgJ3N1Y2Nlc3MnKTtcblxuICAgICAgLy8gUmVzZXQgZm9ybSBhZnRlciBzdWNjZXNzZnVsIGJvb2tpbmdcbiAgICAgIHNldFBpY2t1cExvY2F0aW9uKHtcbiAgICAgICAgYWRkcmVzczogJycsXG4gICAgICAgIGxhdDogMCxcbiAgICAgICAgbG5nOiAwLFxuICAgICAgfSk7XG4gICAgICBzZXRQYXNzZW5nZXJzKDEpO1xuICAgICAgc2V0RXN0aW1hdGVkRmFyZSgwKTtcbiAgICAgIHNldFNlbGVjdGVkQm9va2luZ0lkKG51bGwpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGJvb2tpbmc6JywgZXJyb3IpO1xuXG4gICAgICAvLyBQcm92aWRlIG1vcmUgZGV0YWlsZWQgZXJyb3IgbWVzc2FnZXNcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRmFpbGVkIHRvIGNyZWF0ZSBib29raW5nLiBQbGVhc2UgdHJ5IGFnYWluLic7XG5cbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCduZXR3b3JrJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnTmV0d29yayBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgaW50ZXJuZXQgY29ubmVjdGlvbiBhbmQgdHJ5IGFnYWluLic7XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygncGVybWlzc2lvbi1kZW5pZWQnKSkge1xuICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdQZXJtaXNzaW9uIGRlbmllZC4gUGxlYXNlIGxvZyBvdXQgYW5kIGxvZyBiYWNrIGluLic7XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnbm90LWZvdW5kJykpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnRGF0YWJhc2UgY29ubmVjdGlvbiBlcnJvci4gUGxlYXNlIHJlZnJlc2ggdGhlIHBhZ2UgYW5kIHRyeSBhZ2Fpbi4nO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHNob3dOb3RpZmljYXRpb24oZXJyb3JNZXNzYWdlLCAnZXJyb3InKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHNob3dOb3RpZmljYXRpb24oJ1BsZWFzZSBsb2cgaW4gdG8gYm9vayBhIHJpZGUuJywgJ2Vycm9yJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgcGlja3VwIGxvY2F0aW9uXG4gICAgaWYgKCFwaWNrdXBMb2NhdGlvbi5hZGRyZXNzIHx8IHBpY2t1cExvY2F0aW9uLmxhdCA9PT0gMCB8fCBwaWNrdXBMb2NhdGlvbi5sbmcgPT09IDApIHtcbiAgICAgIHNob3dOb3RpZmljYXRpb24oJ1BsZWFzZSBzZWxlY3QgYSB2YWxpZCBwaWNrdXAgbG9jYXRpb24uJywgJ2Vycm9yJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIGJvb2tpbmcgZGlyZWN0bHkgKG5vIHBheW1lbnQgcmVxdWlyZWQpXG4gICAgYXdhaXQgY3JlYXRlQm9va2luZygpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPExheW91dCB0aXRsZT1cIkJhcm9SaWRlIC0gQm9vayBhIFJpZGVcIj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBwLTIgc206cC00IG1heC13LTR4bFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBzbTp0ZXh0LTJ4bCBsZzp0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkJvb2sgWW91ciBSaWRlPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktNjAwIG10LTFcIj5RdWljayBhbmQgZWFzeSBhaXJwb3J0IHRyYW5zcG9ydGF0aW9uPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTQgc206c3BhY2UteS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206anVzdGlmeS1iZXR3ZWVuIHNtOml0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBzbTptYi0wXCI+UGlja3VwIExvY2F0aW9uPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTWFwU2VsZWN0aW9ufVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBzbTp0ZXh0LXNtIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgdG91Y2gtbWFuaXB1bGF0aW9uIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0aW5nT25NYXBcbiAgICAgICAgICAgICAgICAgICAgICA/ICd0ZXh0LXdoaXRlIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGJvcmRlciBib3JkZXItYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtNzAwIGJnLWJsdWUtNTAgaG92ZXI6YmctYmx1ZS0xMDAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdG91Y2hBY3Rpb246ICdtYW5pcHVsYXRpb24nIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzU2VsZWN0aW5nT25NYXAgPyAn8J+TnSBNYW51YWwgRW50cnknIDogJ/Cfl7rvuI8gU2VsZWN0IG9uIE1hcCd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnZXRVc2VyTG9jYXRpb259XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1wdXJwbGUtNjAwIGhvdmVyOnRleHQtcHVycGxlLTcwMCBiZy1wdXJwbGUtNTAgaG92ZXI6YmctcHVycGxlLTEwMCBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzIHRvdWNoLW1hbmlwdWxhdGlvbiBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB0b3VjaEFjdGlvbjogJ21hbmlwdWxhdGlvbicgfX1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmdHcHN9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZ0dwcyA/IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiAtbWwtMSBtci0xIGgtMyB3LTMgc206aC00IHNtOnctNCB0ZXh0LXB1cnBsZS02MDBcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggY2xhc3NOYW1lPVwib3BhY2l0eS03NVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCI+PC9wYXRoPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIEdldHRpbmcuLi5cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgIPCfk40gTXkgTG9jYXRpb25cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIHtwcmV2aW91c0Jvb2tpbmdzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlUHJldmlvdXNCb29raW5nc31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTUwMCBob3Zlcjp0ZXh0LWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtzaG93UHJldmlvdXNCb29raW5ncyA/ICdIaWRlIFByZXZpb3VzJyA6ICdVc2UgUHJldmlvdXMnfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgeyFpc1NlbGVjdGluZ09uTWFwID8gKFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBwaWNrdXAgYWRkcmVzcyBtYW51YWxseVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwLTIgYm9yZGVyIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cGlja3VwTG9jYXRpb24uYWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0UGlja3VwTG9jYXRpb24ocHJldiA9PiAoeyAuLi5wcmV2LCBhZGRyZXNzOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgLy8gQ2xlYXIgc2VsZWN0ZWQgYm9va2luZyB3aGVuIG1hbnVhbGx5IGVkaXRpbmdcbiAgICAgICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRCb29raW5nSWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQm9va2luZ0lkKG51bGwpO1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7cGlja3VwTG9jYXRpb24uYWRkcmVzcyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhclNlbGVjdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0yIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGNsYXNzTmFtZT1cImgtNCB3LTRcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTMgaW5saW5lLWJsb2NrIG1yLTFcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTEzIDE2aC0xdi00aC0xbTEtNGguMDFNMjEgMTJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCI+PC9wYXRoPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICBFbnRlciB5b3VyIGFkZHJlc3MgbWFudWFsbHksIGNsaWNrIFwiVXNlIE15IExvY2F0aW9uXCIsIG9yIHNlbGVjdCBpdCBvbiB0aGUgbWFwXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC0zIG1iLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ibHVlLTYwMCBtci0yXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZD1cIk0xNy42NTcgMTYuNjU3TDEzLjQxNCAyMC45YTEuOTk4IDEuOTk4IDAgMDEtMi44MjcgMGwtNC4yNDQtNC4yNDNhOCA4IDAgMTExMS4zMTQgMHpcIj48L3BhdGg+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTE1IDExYTMgMyAwIDExLTYgMCAzIDMgMCAwMTYgMHpcIj48L3BhdGg+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPk1hcCBTZWxlY3Rpb24gTW9kZSBBY3RpdmU8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMFwiPlRhcCBhbnl3aGVyZSBvbiB0aGUgbWFwIGJlbG93IHRvIHNlbGVjdCB5b3VyIHBpY2t1cCBsb2NhdGlvbjwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBQcmV2aW91cyBib29raW5ncyBkcm9wZG93biAqL31cbiAgICAgICAgICAgIHtzaG93UHJldmlvdXNCb29raW5ncyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBib3JkZXIgcm91bmRlZCBzaGFkb3ctc20gb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHB4LTMgcHktMiBib3JkZXItYlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlByZXZpb3VzIEJvb2tpbmdzPC9oMz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLTYwIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZ0Jvb2tpbmdzID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIGFuaW1hdGUtc3BpbiBoLTUgdy01IGJvcmRlci0yIGJvcmRlci1ibHVlLTUwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IHByZXZpb3VzQm9va2luZ3MubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBObyBwcmV2aW91cyBib29raW5ncyBmb3VuZFxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7cHJldmlvdXNCb29raW5ncy5tYXAoKGJvb2tpbmcpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaVxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2Jvb2tpbmcuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMyBob3ZlcjpiZy1ncmF5LTUwIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRCb29raW5nSWQgPT09IGJvb2tpbmcuaWQgPyAnYmctYmx1ZS01MCBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMCcgOiAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2VsZWN0UHJldmlvdXNCb29raW5nKGJvb2tpbmcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPntib29raW5nLnBpY2t1cExvY2F0aW9uLmFkZHJlc3N9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+VG86IHtib29raW5nLmFpcnBvcnQubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoYm9va2luZy51cGRhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRCb29raW5nSWQgPT09IGJvb2tpbmcuaWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS01MDAgZm9udC1tZWRpdW1cIj5TZWxlY3RlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMlwiPlBpY2t1cCBMb2NhdGlvbiBNYXA8L2gzPlxuICAgICAgICAgICAgPEJhc2ljTWFwXG4gICAgICAgICAgICAgIGhlaWdodD1cIjMwMHB4XCJcbiAgICAgICAgICAgICAgc2VsZWN0YWJsZT17aXNTZWxlY3RpbmdPbk1hcH1cbiAgICAgICAgICAgICAgb25Mb2NhdGlvblNlbGVjdGVkPXtoYW5kbGVMb2NhdGlvblNlbGVjdGVkfVxuICAgICAgICAgICAgICBpbml0aWFsTG9jYXRpb249e3BpY2t1cExvY2F0aW9uLmxhdCAhPT0gMCA/IHBpY2t1cExvY2F0aW9uIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIHtwaWNrdXBMb2NhdGlvbi5sYXQgIT09IDAgJiYgcGlja3VwTG9jYXRpb24ubG5nICE9PSAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHAtMiBiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0xMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+U2VsZWN0ZWQgUGlja3VwOiB7cGlja3VwTG9jYXRpb24uYWRkcmVzc308L3A+XG4gICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEJvb2tpbmdJZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIHB4LTIgcHktMSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEZyb20gUHJldmlvdXMgQm9va2luZ1xuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17Y2xlYXJTZWxlY3Rpb259XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC14cyB0ZXh0LXJlZC01MDAgaG92ZXI6dGV4dC1yZWQtNzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQ2xlYXJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlNlbGVjdCBBaXJwb3J0PC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZEFpcnBvcnQuY29kZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGFpcnBvcnQgPSBBSVJQT1JUUy5maW5kKGEgPT4gYS5jb2RlID09PSBlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICBpZiAoYWlycG9ydCkgc2V0U2VsZWN0ZWRBaXJwb3J0KGFpcnBvcnQpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgcm91bmRlZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7QUlSUE9SVFMubWFwKGFpcnBvcnQgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2FpcnBvcnQuY29kZX0gdmFsdWU9e2FpcnBvcnQuY29kZX0+XG4gICAgICAgICAgICAgICAgICAgIHthaXJwb3J0Lm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+TnVtYmVyIG9mIFBhc3NlbmdlcnM8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRQYXNzZW5nZXJzKHByZXYgPT4gTWF0aC5tYXgoMSwgcHJldiAtIDEpKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBiZy1ncmF5LTEwMCBib3JkZXIgcm91bmRlZC1sIGhvdmVyOmJnLWdyYXktMjAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPVwiMlwiIGQ9XCJNMjAgMTJINFwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBtaW49XCIxXCJcbiAgICAgICAgICAgICAgICAgIG1heD1cIjhcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3Bhc3NlbmdlcnN9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBhc3NlbmdlcnMoTWF0aC5tYXgoMSwgTWF0aC5taW4oOCwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIHx8IDEpKSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcC0yIGJvcmRlci10IGJvcmRlci1iIHRleHQtY2VudGVyXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UGFzc2VuZ2VycyhwcmV2ID0+IE1hdGgubWluKDgsIHByZXYgKyAxKSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgYmctZ3JheS0xMDAgYm9yZGVyIHJvdW5kZWQtciBob3ZlcjpiZy1ncmF5LTIwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTEyIDZ2MTJNNiAxMmgxMlwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5NYXhpbXVtIDggcGFzc2VuZ2VycyBwZXIgcmlkZTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEZhcmUgZXN0aW1hdGUgKi99XG4gICAgICAgICAge2VzdGltYXRlZEZhcmUgPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQgcC00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS04MDAgbWItMlwiPkZhcmUgRXN0aW1hdGU8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5CYXNlIGZhcmU8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5EaXN0YW5jZTwvcD5cbiAgICAgICAgICAgICAgICAgIHtwYXNzZW5nZXJzID4gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPkFkZGl0aW9uYWwgcGFzc2VuZ2VycyAoe3Bhc3NlbmdlcnMgLSAxfSk8L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+JDM1LjAwPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+JHtNYXRoLmZsb29yKDEwICsgKChwaWNrdXBMb2NhdGlvbi5sYXQgKiBwaWNrdXBMb2NhdGlvbi5sbmcpICUgMTAwKSAvIDUpLnRvRml4ZWQoMil9PC9wPlxuICAgICAgICAgICAgICAgICAge3Bhc3NlbmdlcnMgPiAxICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+JHsoKHBhc3NlbmdlcnMgLSAxKSAqIDUpLnRvRml4ZWQoMil9PC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWJsdWUtMjAwIG10LTIgcHQtMiBmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWJsdWUtODAwXCI+VG90YWwgZXN0aW1hdGVkIGZhcmU8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTgwMFwiPiR7ZXN0aW1hdGVkRmFyZS50b0ZpeGVkKDIpfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBtdC0yXCI+XG4gICAgICAgICAgICAgICAgKiBBY3R1YWwgZmFyZSBtYXkgdmFyeSBiYXNlZCBvbiB0cmFmZmljLCB3ZWF0aGVyLCBhbmQgb3RoZXIgZmFjdG9ycy5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtNFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsICR7XG4gICAgICAgICAgICAgICAgIXBpY2t1cExvY2F0aW9uLmFkZHJlc3MgfHwgcGlja3VwTG9jYXRpb24ubGF0ID09PSAwIHx8IHBpY2t1cExvY2F0aW9uLmxuZyA9PT0gMFxuICAgICAgICAgICAgICAgICAgPyAnYmctZ3JheS00MDAgY3Vyc29yLW5vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgICAgICAgOiAnYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDAnXG4gICAgICAgICAgICAgIH0gdGV4dC13aGl0ZSBwLTMgcm91bmRlZCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9yc2B9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXshcGlja3VwTG9jYXRpb24uYWRkcmVzcyB8fCBwaWNrdXBMb2NhdGlvbi5sYXQgPT09IDAgfHwgcGlja3VwTG9jYXRpb24ubG5nID09PSAwfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7IXVzZXIgPyAnUGxlYXNlIExvZyBJbiB0byBCb29rIGEgUmlkZScgOiAnQm9vayBSaWRlIE5vdyd9XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgeyghcGlja3VwTG9jYXRpb24uYWRkcmVzcyB8fCBwaWNrdXBMb2NhdGlvbi5sYXQgPT09IDAgfHwgcGlja3VwTG9jYXRpb24ubG5nID09PSAwKSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMlwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC01MDAgbXItMVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItM0wxMy43MzIgNGMtLjc3LTEuMzMzLTIuNjk0LTEuMzMzLTMuNDY0IDBMMy4zNCAxNmMtLjc3IDEuMzMzLjE5MiAzIDEuNzMyIDN6XCI+PC9wYXRoPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXNtXCI+UGxlYXNlIGVudGVyIG9yIHNlbGVjdCBhIHBpY2t1cCBsb2NhdGlvbjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7IXVzZXIgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTJcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC15ZWxsb3ctNTAwIG1yLTFcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9XCIyXCIgZD1cIk0xMyAxNmgtMXYtNGgtMW0xLTRoLjAxTTIxIDEyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy01MDAgdGV4dC1zbVwiPllvdSBuZWVkIHRvIGJlIGxvZ2dlZCBpbiB0byBib29rIGEgcmlkZTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L0xheW91dD5cbiAgKTtcbn1cblxuLy8gV3JhcCB0aGUgY29tcG9uZW50IHdpdGggUHJvdGVjdGVkUm91dGVcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJvb2tSaWRlUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8UHJvdGVjdGVkUm91dGUgcmVxdWlyZWRSb2xlcz17WydhZG1pbicsICdyaWRlciddfT5cbiAgICAgIDxCb29rUmlkZSAvPlxuICAgIDwvUHJvdGVjdGVkUm91dGU+XG4gICk7XG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsImRiIiwiY29sbGVjdGlvbiIsImFkZERvYyIsInF1ZXJ5Iiwid2hlcmUiLCJnZXREb2NzIiwib3JkZXJCeSIsImxpbWl0IiwiZG9jIiwiZ2V0RG9jIiwidXBkYXRlRG9jIiwiQmFzaWNNYXAiLCJMYXlvdXQiLCJ1c2VOb3RpZmljYXRpb24iLCJzYWZlQWRkRG9jIiwic2FmZVVwZGF0ZURvYyIsInNhZmVRdWVyeURvY3MiLCJpc0ZpcmViYXNlSG9zdGluZyIsIlByb3RlY3RlZFJvdXRlIiwiQUlSUE9SVFMiLCJjb2RlIiwibmFtZSIsIkJvb2tSaWRlIiwidXNlciIsInNob3dOb3RpZmljYXRpb24iLCJwaWNrdXBMb2NhdGlvbiIsInNldFBpY2t1cExvY2F0aW9uIiwiYWRkcmVzcyIsImxhdCIsImxuZyIsInNlbGVjdGVkQWlycG9ydCIsInNldFNlbGVjdGVkQWlycG9ydCIsInBhc3NlbmdlcnMiLCJzZXRQYXNzZW5nZXJzIiwiaXNTZWxlY3RpbmdPbk1hcCIsInNldElzU2VsZWN0aW5nT25NYXAiLCJlc3RpbWF0ZWRGYXJlIiwic2V0RXN0aW1hdGVkRmFyZSIsInByZXZpb3VzQm9va2luZ3MiLCJzZXRQcmV2aW91c0Jvb2tpbmdzIiwiaXNMb2FkaW5nQm9va2luZ3MiLCJzZXRJc0xvYWRpbmdCb29raW5ncyIsInNob3dQcmV2aW91c0Jvb2tpbmdzIiwic2V0U2hvd1ByZXZpb3VzQm9va2luZ3MiLCJzZWxlY3RlZEJvb2tpbmdJZCIsInNldFNlbGVjdGVkQm9va2luZ0lkIiwiaXNMb2FkaW5nR3BzIiwic2V0SXNMb2FkaW5nR3BzIiwiZmV0Y2hQcmV2aW91c0Jvb2tpbmdzIiwiY29uc29sZSIsImxvZyIsImJvb2tpbmdzIiwiY29uc3RyYWludHMiLCJpZCIsInJlc3VsdHMiLCJtYXAiLCJkYXRhIiwic2NoZWR1bGVkVGltZSIsIkRhdGUiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJib29raW5nc1F1ZXJ5IiwicXVlcnlTbmFwc2hvdCIsImZvckVhY2giLCJib29raW5nIiwicHVzaCIsImxlbmd0aCIsImVycm9yIiwiY2xlYXJTZWxlY3Rpb24iLCJ0b2dnbGVNYXBTZWxlY3Rpb24iLCJoYW5kbGVMb2NhdGlvblNlbGVjdGVkIiwibG9jYXRpb24iLCJuZXdMb2NhdGlvbiIsImZhcmUiLCJjYWxjdWxhdGVGYXJlIiwidmFsaWRhdGVBbmRHZW9jb2RlQWRkcmVzcyIsInRyaW0iLCJyZXNwb25zZSIsImZldGNoIiwiZW5jb2RlVVJJQ29tcG9uZW50Iiwib2siLCJqc29uIiwiZmVhdHVyZXMiLCJmZWF0dXJlIiwiY2VudGVyIiwicHJldiIsInBsYWNlX25hbWUiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwidG9nZ2xlUHJldmlvdXNCb29raW5ncyIsImdldFVzZXJMb2NhdGlvbiIsIm5hdmlnYXRvciIsImdlb2xvY2F0aW9uIiwicG9zaXRpb24iLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImdldEN1cnJlbnRQb3NpdGlvbiIsImNvb3JkcyIsIm1lc3NhZ2UiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsImVuYWJsZUhpZ2hBY2N1cmFjeSIsInRpbWVvdXQiLCJtYXhpbXVtQWdlIiwibGF0aXR1ZGUiLCJsb25naXR1ZGUiLCJzdGF0dXMiLCJnZW9jb2RpbmdFcnJvciIsInNlbGVjdFByZXZpb3VzQm9va2luZyIsImFpcnBvcnQiLCJmaW5kIiwiYSIsImJhc2VGYXJlIiwic2VlZCIsImRpc3RhbmNlRmFyZSIsIk1hdGgiLCJmbG9vciIsInBhc3NlbmdlckZhcmUiLCJjcmVhdGVCb29raW5nIiwibm93IiwicmlkZXJJZCIsImJvb2tpbmdJZCIsImJvb2tpbmdSZWYiLCJ1c2VyRG9jIiwiZXhpc3RzIiwidXNlckRhdGEiLCJib29raW5nSGlzdG9yeSIsImluY2x1ZGVzIiwidXNlclJlZiIsImhpc3RvcnlFcnJvciIsIm5vdGlmaWNhdGlvbkRhdGEiLCJ1c2VySWQiLCJ0eXBlIiwicmVhZCIsInJlbGF0ZWRCb29raW5nSWQiLCJub3RpZmljYXRpb25FcnJvciIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInRpdGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJidXR0b24iLCJvbkNsaWNrIiwic3R5bGUiLCJ0b3VjaEFjdGlvbiIsImRpc2FibGVkIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJwYXRoIiwiZCIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiaDMiLCJzcGFuIiwidWwiLCJsaSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImhlaWdodCIsInNlbGVjdGFibGUiLCJvbkxvY2F0aW9uU2VsZWN0ZWQiLCJpbml0aWFsTG9jYXRpb24iLCJ1bmRlZmluZWQiLCJzZWxlY3QiLCJvcHRpb24iLCJtYXgiLCJtaW4iLCJwYXJzZUludCIsInRvRml4ZWQiLCJCb29rUmlkZVBhZ2UiLCJyZXF1aXJlZFJvbGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/book/index.tsx\n"));

/***/ }),

/***/ "./src/utils/firebase-helpers.ts":
/*!***************************************!*\
  !*** ./src/utils/firebase-helpers.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: function() { return /* binding */ isBrowser; },\n/* harmony export */   isFirebaseHosting: function() { return /* binding */ isFirebaseHosting; },\n/* harmony export */   isProduction: function() { return /* binding */ isProduction; },\n/* harmony export */   safeAddDoc: function() { return /* binding */ safeAddDoc; },\n/* harmony export */   safeGetDoc: function() { return /* binding */ safeGetDoc; },\n/* harmony export */   safeQueryDocs: function() { return /* binding */ safeQueryDocs; },\n/* harmony export */   safeUpdateDoc: function() { return /* binding */ safeUpdateDoc; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n\n\n/**\n * Helper function to safely get a document from Firestore with retry logic\n */ const safeGetDoc = async function(path, id) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path, id);\n            const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(docRef);\n            if (docSnap.exists()) {\n                return {\n                    id: docSnap.id,\n                    ...docSnap.data()\n                };\n            }\n            return null;\n        } catch (error) {\n            retries++;\n            console.error(\"Error getting document (attempt \".concat(retries, \"/\").concat(maxRetries, \"):\"), error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    return null;\n};\n/**\n * Helper function to safely query documents from Firestore with retry logic\n */ const safeQueryDocs = async function(path, constraints) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path), ...constraints);\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(q);\n            const results = [];\n            querySnapshot.forEach((doc)=>{\n                results.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            return results;\n        } catch (error) {\n            retries++;\n            console.error(\"Error querying documents (attempt \".concat(retries, \"/\").concat(maxRetries, \"):\"), error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    return [];\n};\n/**\n * Helper function to safely add a document to Firestore with retry logic\n */ const safeAddDoc = async function(path, data) {\n    let maxRetries = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path), data);\n            return docRef.id;\n        } catch (error) {\n            retries++;\n            console.error(\"Error adding document (attempt \".concat(retries, \"/\").concat(maxRetries, \"):\"), error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    throw new Error(\"Failed to add document after maximum retries\");\n};\n/**\n * Helper function to safely update a document in Firestore with retry logic\n */ const safeUpdateDoc = async function(path, id, data) {\n    let maxRetries = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2;\n    let retries = 0;\n    while(retries < maxRetries){\n        try {\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, path, id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(docRef, data);\n            return;\n        } catch (error) {\n            retries++;\n            console.error(\"Error updating document (attempt \".concat(retries, \"/\").concat(maxRetries, \"):\"), error);\n            if (retries >= maxRetries) {\n                throw error;\n            }\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n    throw new Error(\"Failed to update document after maximum retries\");\n};\n/**\n * Check if the app is running in a production environment\n */ const isProduction = ()=>{\n    return \"development\" === \"production\";\n};\n/**\n * Check if the app is running in a browser environment\n */ const isBrowser = ()=>{\n    return \"object\" !== \"undefined\";\n};\n/**\n * Check if the app is running on Firebase hosting\n */ const isFirebaseHosting = ()=>{\n    if (!isBrowser()) return false;\n    // Check if the URL contains firebase hosting domains\n    const hostname = window.location.hostname;\n    return hostname.includes(\"firebaseapp.com\") || hostname.includes(\"web.app\") || hostname === \"baroride.web.app\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/firebase-helpers.ts\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cbook%5Cindex.tsx&page=%2Fbook!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);