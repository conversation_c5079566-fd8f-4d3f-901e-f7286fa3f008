import { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useNotification } from '@/contexts/NotificationContext';
import { authenticateUser, isPhoneNumber, normalizePhoneNumber } from '@/utils/auth-helpers';
import { debugUsers, testPhoneNormalization, createTestUser } from '@/utils/debug-users';

export default function Login() {
  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { showNotification } = useNotification();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!identifier) {
      setError('Please enter your email or phone number');
      return;
    }

    if (!password) {
      setError('Please enter your password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      console.log('Attempting login with identifier:', identifier);
      console.log('Is phone number:', isPhoneNumber(identifier));
      if (isPhoneNumber(identifier)) {
        console.log('Normalized phone number:', normalizePhoneNumber(identifier));
      }

      // Authenticate with either email or phone number
      const userData = await authenticateUser(identifier, password);

      console.log('Login successful for user:', userData);

      // Show a success notification that will auto-dismiss after 3 seconds
      showNotification('Login successful! Redirecting...', 'success', 3000);

      // Redirect based on user role
      if (userData.role === 'admin') {
        // If user is an admin, redirect to admin dashboard
        router.push('/admin/dashboard');
      } else if (userData.role === 'driver') {
        // If user is a driver, redirect to driver dashboard
        router.push('/driver/dashboard');
      } else {
        // If user is a rider, redirect to home page
        router.push('/');
      }
    } catch (err) {
      console.error('Login error:', err);

      // Provide more specific error messages
      if (err instanceof Error) {
        console.log('Error message:', err.message);
        if (err.message.includes('user-not-found') || err.message.includes('User not found')) {
          const errorMsg = isPhoneNumber(identifier)
            ? 'No account found with this phone number. Please check the number or create a new account.'
            : 'No account found with this email address. Please check the email or create a new account.';
          setError(errorMsg);
          showNotification(errorMsg, 'error', 5000);
        } else if (err.message.includes('wrong-password') || err.message.includes('invalid-credential')) {
          setError('Incorrect password. Please try again.');
          showNotification('Incorrect password. Please try again.', 'error', 3000);
        } else if (err.message.includes('Invalid identifier format')) {
          setError('Please enter a valid email or phone number.');
          showNotification('Please enter a valid email or phone number.', 'error', 3000);
        } else {
          setError(`Login failed: ${err.message}`);
          showNotification(`Login failed: ${err.message}`, 'error', 5000);
        }
      } else {
        setError('Failed to login. Please check your credentials.');
        showNotification('Login failed. Please check your credentials.', 'error', 3000);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 safe-area-top safe-area-bottom">
      <Head>
        <title>BaroRide - Login</title>
        <meta name="description" content="Login to your BaroRide account" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      </Head>

      {/* Mobile-optimized container */}
      <div className="flex flex-col min-h-screen">
        {/* Header section */}
        <div className="flex-shrink-0 pt-8 pb-4 px-4 text-center">
          <div className="flex justify-center mb-4">
            <img
              src="/logo-icon.svg"
              alt="BaroRide Logo"
              className="h-16 w-16 sm:h-20 sm:w-20"
            />
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Welcome Back</h1>
          <p className="text-sm sm:text-base text-gray-600">Sign in to your BaroRide account</p>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex items-center justify-center px-4 pb-8">
          <div className="w-full max-w-sm">
            {/* Error message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 text-sm">
                {error}
              </div>
            )}

            {/* Login form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email/Phone input */}
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-2">
                  Email or Phone Number
                </label>
                <input
                  id="identifier"
                  type="text"
                  value={identifier}
                  onChange={(e) => setIdentifier(e.target.value)}
                  placeholder="Enter your email or phone number"
                  className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                  autoComplete="username"
                  autoCapitalize="none"
                  autoCorrect="off"
                  spellCheck="false"
                  required
                />
              </div>

              {/* Password input */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors"
                    autoComplete="current-password"
                    autoCapitalize="none"
                    autoCorrect="off"
                    spellCheck="false"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              {/* Forgot password link */}
              <div className="flex justify-end">
                <Link href="/forgot-password" className="text-sm text-blue-600 hover:text-blue-700 transition-colors touch-target">
                  Forgot password?
                </Link>
              </div>

              {/* Submit button */}
              <button
                type="submit"
                className={`mobile-button w-full py-4 px-6 rounded-lg font-semibold text-base transition-all duration-200 touch-target ${
                  isLoading
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl'
                } text-white flex items-center justify-center`}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="mobile-spinner mr-3"></div>
                    Signing in...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Sign In
                  </>
                )}
              </button>
            </form>

            {/* Sign up link */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{' '}
                <Link href="/signup" className="text-blue-600 hover:text-blue-700 font-semibold transition-colors touch-target">
                  Create one here
                </Link>
              </p>
            </div>

            {/* Debug buttons - remove in production */}
            <div className="mt-6 space-y-3">
              <button
                type="button"
                onClick={() => {
                  debugUsers();
                  testPhoneNormalization();
                }}
                className="w-full py-3 px-4 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600 touch-target"
              >
                Debug Users (Check Console)
              </button>
              <button
                type="button"
                onClick={() => {
                  createTestUser();
                }}
                className="w-full py-3 px-4 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 touch-target"
              >
                Create Test User (Check Console)
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}