(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[410],{2065:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/mobile-test",function(){return t(2544)}])},2544:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return a}});var i=t(5893),l=t(7294),n=t(2151),o=t(445),r=t(3302);function a(){var e,s;let[t,a]=(0,l.useState)(null),[c,d]=(0,l.useState)({});return((0,l.useEffect)(()=>{a((0,o.dz)()),(()=>{var e;let s={},t=document.querySelector('meta[name="viewport"]');s.viewportMeta=null!==t&&(null===(e=t.getAttribute("content"))||void 0===e?void 0:e.includes("user-scalable=no"))===!0;let i=document.createElement("div");i.style.touchAction="manipulation",s.touchAction="manipulation"===i.style.touchAction,s.cssCustomProperties=CSS.supports("height","calc(var(--vh, 1vh) * 100)"),s.safeAreaSupport=CSS.supports("padding","env(safe-area-inset-top)");let l=document.createElement("input");l.style.fontSize="16px",s.inputFontSize="16px"===l.style.fontSize,s.touchEvents="ontouchstart"in window,s.devicePixelRatio=window.devicePixelRatio>0,s.orientationSupport="orientation"in window||"onorientationchange"in window,d(s)})()},[]),t)?(0,i.jsx)(n.Z,{title:"Mobile Compatibility Test - BaroRide",children:(0,i.jsxs)("div",{className:"container mx-auto p-4 max-w-4xl",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Mobile Compatibility Test"}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Device Information"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Device Type:"})," ",t.isMobile?"Mobile":t.isTablet?"Tablet":"Desktop"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Operating System:"})," ",t.isIOS?"iOS":t.isAndroid?"Android":"Other"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Browser:"})," ",t.isSafari?"Safari":t.isChrome?"Chrome":"Other"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Touch Support:"})," ",t.touchSupport?"Yes":"No"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Screen Size:"})," ",t.screenWidth," \xd7 ",t.screenHeight]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Pixel Ratio:"})," ",t.pixelRatio]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Orientation:"})," ",t.orientation]})]})]}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Compatibility Tests"}),(0,i.jsx)("div",{className:"space-y-2",children:Object.entries(c).map(e=>{let[s,t]=e;return(0,i.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,i.jsx)("span",{className:"text-sm",children:s.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())}),(0,i.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(t?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t?"PASS":"FAIL"})]},s)})})]}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Form Input Testing"}),(0,i.jsxs)("form",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Text Input (should not zoom on focus)"}),(0,i.jsx)("input",{type:"text",placeholder:"Type here to test zoom prevention",className:"mobile-input w-full"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Email Input"}),(0,i.jsx)("input",{type:"email",placeholder:"<EMAIL>",className:"mobile-input w-full",autoComplete:"email",inputMode:"email"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Phone Input"}),(0,i.jsx)("input",{type:"tel",placeholder:"+****************",className:"mobile-input w-full",autoComplete:"tel",inputMode:"tel"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Number Input"}),(0,i.jsx)("input",{type:"number",placeholder:"123",className:"mobile-input w-full",inputMode:"numeric"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Select Dropdown"}),(0,i.jsxs)("select",{className:"mobile-input w-full",children:[(0,i.jsx)("option",{children:"Option 1"}),(0,i.jsx)("option",{children:"Option 2"}),(0,i.jsx)("option",{children:"Option 3"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Textarea"}),(0,i.jsx)("textarea",{placeholder:"Type a longer message here...",className:"mobile-input w-full",rows:4})]})]})]}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Button Touch Testing"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsx)("button",{className:"mobile-button bg-blue-500 text-white",children:"Primary Button"}),(0,i.jsx)("button",{className:"mobile-button bg-gray-500 text-white",children:"Secondary Button"}),(0,i.jsx)("button",{className:"mobile-button bg-green-500 text-white",children:"Success Button"}),(0,i.jsx)("button",{className:"mobile-button bg-red-500 text-white",children:"Danger Button"})]}),(0,i.jsx)("p",{className:"text-xs text-gray-600 mt-2",children:"All buttons should have minimum 44px touch targets and proper touch feedback."})]}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Map Interaction Testing"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Test map interactions on mobile. The map should prevent page scrolling when interacting."}),(0,i.jsx)("div",{className:"map-container",children:(0,i.jsx)(r.Z,{height:t.isMobile?"40vh":"300px",selectable:!0,onLocationSelected:e=>{console.log("Location selected:",e)}})})]}),(0,i.jsxs)("div",{className:"mobile-card mb-6",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Scroll Performance Testing"}),(0,i.jsxs)("div",{className:"h-64 overflow-y-auto border rounded p-4 bg-gray-50",children:[(0,i.jsx)("p",{className:"mb-4",children:"This is a scrollable area to test smooth scrolling performance on mobile."}),Array.from({length:50},(e,s)=>(0,i.jsxs)("p",{className:"mb-2",children:["Scroll test item ",s+1," - This should scroll smoothly with momentum on iOS devices."]},s))]})]}),(0,i.jsxs)("div",{className:"mobile-card",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Performance Metrics"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Connection:"})," ",(null===(e=navigator.connection)||void 0===e?void 0:e.effectiveType)||"Unknown"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Memory:"})," ",(null===(s=performance.memory)||void 0===s?void 0:s.usedJSHeapSize)?"".concat(Math.round(performance.memory.usedJSHeapSize/1024/1024),"MB"):"Unknown"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Hardware Concurrency:"})," ",navigator.hardwareConcurrency||"Unknown"]})]})]}),(0,i.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 border border-blue-200 rounded",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"Test Summary"}),(0,i.jsxs)("p",{className:"text-blue-700 text-sm",children:[Object.values(c).filter(Boolean).length," of ",Object.keys(c).length," tests passed.",t.isMobile?" Mobile optimizations are active.":" Desktop mode detected."]})]})]})}):(0,i.jsx)(n.Z,{title:"Mobile Test - Loading",children:(0,i.jsxs)("div",{className:"flex items-center justify-center min-h-64",children:[(0,i.jsx)("div",{className:"mobile-spinner"}),(0,i.jsx)("span",{className:"ml-2",children:"Loading device info..."})]})})}}},function(e){e.O(0,[996,151,302,888,774,179],function(){return e(e.s=2065)}),_N_E=e.O()}]);