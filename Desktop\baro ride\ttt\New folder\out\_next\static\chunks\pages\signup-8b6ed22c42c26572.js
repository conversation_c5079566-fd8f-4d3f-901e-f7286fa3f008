(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[616],{7805:function(e,r,l){(window.__NEXT_P=window.__NEXT_P||[]).push(["/signup",function(){return l(6254)}])},6254:function(e,r,l){"use strict";l.r(r),l.d(r,{default:function(){return m}});var a=l(5893),s=l(7294),t=l(1517),o=l(109),i=l(404),n=l(1163),d=l(9008),c=l.n(d),u=l(6492);function m(){let[e,r]=(0,s.useState)(""),[l,d]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),[h,p]=(0,s.useState)(""),[b,f]=(0,s.useState)(""),[g,v]=(0,s.useState)("rider"),[y,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(!1),C=(0,n.useRouter)(),{showNotification:k}=(0,u.l)(),[R,z]=(0,s.useState)(""),[M,P]=(0,s.useState)(""),[S,F]=(0,s.useState)(""),[B,E]=(0,s.useState)(""),[_,q]=(0,s.useState)(""),[D,I]=(0,s.useState)(!1),A=async r=>{r.preventDefault(),N(!0),j("");try{if(!e||!l||!h||!b)throw Error("Please fill in all required fields");if("driver"===g&&(!R||!M||!S||!B))throw Error("Please fill in all vehicle details");if("driver"===g&&"244117"!==_)throw Error("Invalid BaroRide ID password. Please contact support if you need assistance.");let r=(await (0,t.Xb)(i.I,e,l)).user.uid,a={id:r,email:e,phoneNumber:b,fullName:h,role:g,createdAt:new Date,updatedAt:new Date};if("rider"===g){let e={...a,role:"rider",bookingHistory:[]};await (0,o.pl)((0,o.JU)(i.db,"users",r),e)}else{let e={...a,role:"driver",isOnline:!1,isVerified:!0,vehicleDetails:{make:R,model:M,color:S,licensePlate:B},rating:5,completedRides:0};await (0,o.pl)((0,o.JU)(i.db,"users",r),e)}k("Account created successfully! Redirecting...","success",3e3),"driver"===g?C.push("/driver/dashboard"):C.push("/")}catch(r){let e=r instanceof Error?r.message:"Failed to create account. Please try again.";j(e),k(e,"error",3e3),N(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4",children:[(0,a.jsxs)(c(),{children:[(0,a.jsx)("title",{children:"BaroRide - Sign Up"}),(0,a.jsx)("meta",{name:"description",content:"Create your BaroRide account"})]}),(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-16 w-16"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your BaroRide account"})]}),y&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative",role:"alert",children:(0,a.jsx)("span",{className:"block sm:inline",children:y})}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:A,children:[(0,a.jsxs)("div",{className:"rounded-md shadow-sm space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Password",value:l,onChange:e=>d(e.target.value)}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 cursor-pointer",onClick:()=>x(!m),children:m?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"sr-only",children:"Full Name"}),(0,a.jsx)("input",{id:"fullName",name:"fullName",type:"text",autoComplete:"name",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Full Name",value:h,onChange:e=>p(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phoneNumber",className:"sr-only",children:"Phone Number"}),(0,a.jsx)("input",{id:"phoneNumber",name:"phoneNumber",type:"tel",autoComplete:"tel",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Phone Number",value:b,onChange:e=>f(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"I want to:"}),(0,a.jsxs)("div",{className:"mt-2 flex space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"rider",name:"role",type:"radio",checked:"rider"===g,onChange:()=>v("rider"),className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"}),(0,a.jsx)("label",{htmlFor:"rider",className:"ml-2 block text-sm text-gray-900",children:"Book rides (Rider)"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"driver",name:"role",type:"radio",checked:"driver"===g,onChange:()=>v("driver"),className:"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"}),(0,a.jsx)("label",{htmlFor:"driver",className:"ml-2 block text-sm text-gray-900",children:"Drive (Driver)"})]})]})]}),"driver"===g&&(0,a.jsxs)("div",{className:"space-y-4 border-t pt-4 mt-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Vehicle Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleMake",className:"sr-only",children:"Vehicle Make"}),(0,a.jsx)("input",{id:"vehicleMake",name:"vehicleMake",type:"text",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Vehicle Make (e.g., Toyota)",value:R,onChange:e=>z(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleModel",className:"sr-only",children:"Vehicle Model"}),(0,a.jsx)("input",{id:"vehicleModel",name:"vehicleModel",type:"text",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Vehicle Model (e.g., Camry)",value:M,onChange:e=>P(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"vehicleColor",className:"sr-only",children:"Vehicle Color"}),(0,a.jsx)("input",{id:"vehicleColor",name:"vehicleColor",type:"text",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Vehicle Color",value:S,onChange:e=>F(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"licensePlate",className:"sr-only",children:"License Plate"}),(0,a.jsx)("input",{id:"licensePlate",name:"licensePlate",type:"text",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"License Plate Number",value:B,onChange:e=>E(e.target.value)})]}),(0,a.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Driver Verification"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"baroRideIdPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"BaroRide ID Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"baroRideIdPassword",name:"baroRideIdPassword",type:D?"text":"password",required:!0,className:"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter BaroRide ID Password",value:_,onChange:e=>q(e.target.value)}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 cursor-pointer",onClick:()=>I(!D),children:D?(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,a.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"This password is required for all driver registrations. Contact support if you don't have it."})]})]})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:w,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ".concat(w?"opacity-70 cursor-not-allowed":""),children:w?"Creating Account...":"Sign Up"})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)("a",{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})]})]})}},9008:function(e,r,l){e.exports=l(3867)}},function(e){e.O(0,[888,774,179],function(){return e(e.s=7805)}),_N_E=e.O()}]);