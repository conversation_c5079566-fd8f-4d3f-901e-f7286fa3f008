import { useState } from 'react';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/firebase/config';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useNotification } from '@/contexts/NotificationContext';
import { normalizePhoneNumber } from '@/utils/auth-helpers';
import type { User, Rider, Driver } from '@/types/user';

export default function Signup() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [fullName, setFullName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [role, setRole] = useState<'rider' | 'driver'>('rider');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { showNotification } = useNotification();

  // Driver specific fields
  const [vehicleMake, setVehicleMake] = useState('');
  const [vehicleModel, setVehicleModel] = useState('');
  const [vehicleColor, setVehicleColor] = useState('');
  const [licensePlate, setLicensePlate] = useState('');
  const [baroRideIdPassword, setBaroRideIdPassword] = useState('');
  const [showBaroRidePassword, setShowBaroRidePassword] = useState(false);

  // The correct BaroRide ID password for driver verification
  const CORRECT_BARO_RIDE_PASSWORD = '244117';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Validate fields
      if (!email || !password || !fullName || !phoneNumber) {
        throw new Error('Please fill in all required fields');
      }

      if (role === 'driver' && (!vehicleMake || !vehicleModel || !vehicleColor || !licensePlate)) {
        throw new Error('Please fill in all vehicle details');
      }

      // Validate BaroRide ID password for drivers
      if (role === 'driver' && baroRideIdPassword !== CORRECT_BARO_RIDE_PASSWORD) {
        throw new Error('Invalid BaroRide ID password. Please contact support if you need assistance.');
      }

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const uid = userCredential.user.uid;

      // Normalize phone number for consistent storage
      const normalizedPhoneNumber = normalizePhoneNumber(phoneNumber);

      // Create base user data
      const userData: Partial<User> = {
        id: uid,
        email,
        phoneNumber: normalizedPhoneNumber,
        fullName,
        role,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add role-specific data
      if (role === 'rider') {
        const riderData: Partial<Rider> = {
          ...userData,
          role: 'rider',
          bookingHistory: [],
        };
        await setDoc(doc(db, 'users', uid), riderData);
      } else {
        const driverData: Partial<Driver> = {
          ...userData,
          role: 'driver',
          isOnline: false,
          isVerified: true, // Driver is verified because they provided the correct BaroRide ID password
          vehicleDetails: {
            make: vehicleMake,
            model: vehicleModel,
            color: vehicleColor,
            licensePlate,
          },
          rating: 5.0, // Default rating
          completedRides: 0,
        };
        await setDoc(doc(db, 'users', uid), driverData);
      }

      // Show success notification that will auto-dismiss after 3 seconds
      showNotification('Account created successfully! Redirecting...', 'success', 3000);

      // Redirect based on user role
      if (role === 'driver') {
        // If user is a driver, redirect to driver dashboard
        router.push('/driver/dashboard');
      } else {
        // If user is a rider, redirect to home page
        router.push('/');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create account. Please try again.';
      setError(errorMessage);
      showNotification(errorMessage, 'error', 3000);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 safe-area-top safe-area-bottom">
      <Head>
        <title>BaroRide - Sign Up</title>
        <meta name="description" content="Create your BaroRide account" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      </Head>

      {/* Mobile-optimized container */}
      <div className="flex flex-col min-h-screen">
        {/* Header section */}
        <div className="flex-shrink-0 pt-8 pb-4 px-4 text-center">
          <div className="flex justify-center mb-4">
            <img
              src="/logo-icon.svg"
              alt="BaroRide Logo"
              className="h-16 w-16 sm:h-20 sm:w-20"
            />
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Join BaroRide</h1>
          <p className="text-sm sm:text-base text-gray-600">Create your account to get started</p>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex items-start justify-center px-4 pb-8">
          <div className="w-full max-w-sm">
            {/* Error message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 text-sm">
                {error}
              </div>
            )}

            {/* Signup form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoCapitalize="none"
                  autoCorrect="off"
                  spellCheck="false"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="new-password"
                    required
                    className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    autoCapitalize="none"
                    autoCorrect="off"
                    spellCheck="false"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name
                </label>
                <input
                  id="fullName"
                  name="fullName"
                  type="text"
                  autoComplete="name"
                  required
                  className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  autoCapitalize="words"
                  autoCorrect="off"
                  spellCheck="false"
                />
              </div>

              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  id="phoneNumber"
                  name="phoneNumber"
                  type="tel"
                  autoComplete="tel"
                  required
                  className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                  placeholder="Enter your phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  autoCapitalize="none"
                  autoCorrect="off"
                  spellCheck="false"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">I want to:</label>
                <div className="space-y-3">
                  <div className="flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target">
                    <input
                      id="rider"
                      name="role"
                      type="radio"
                      checked={role === 'rider'}
                      onChange={() => setRole('rider')}
                      className="focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300"
                    />
                    <label htmlFor="rider" className="ml-3 block text-base text-gray-900 cursor-pointer flex-1">
                      Book rides (Rider)
                    </label>
                  </div>
                  <div className="flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target">
                    <input
                      id="driver"
                      name="role"
                      type="radio"
                      checked={role === 'driver'}
                      onChange={() => setRole('driver')}
                      className="focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300"
                    />
                    <label htmlFor="driver" className="ml-3 block text-base text-gray-900 cursor-pointer flex-1">
                      Drive (Driver)
                    </label>
                  </div>
                </div>
              </div>

              {role === 'driver' && (
                <div className="space-y-6 border-t-2 border-gray-200 pt-6 mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Information</h3>

                  <div>
                    <label htmlFor="vehicleMake" className="block text-sm font-medium text-gray-700 mb-2">
                      Vehicle Make
                    </label>
                    <input
                      id="vehicleMake"
                      name="vehicleMake"
                      type="text"
                      required
                      className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                      placeholder="e.g., Toyota, Honda, Ford"
                      value={vehicleMake}
                      onChange={(e) => setVehicleMake(e.target.value)}
                      autoCapitalize="words"
                      autoCorrect="off"
                      spellCheck="false"
                    />
                  </div>

                  <div>
                    <label htmlFor="vehicleModel" className="block text-sm font-medium text-gray-700 mb-2">
                      Vehicle Model
                    </label>
                    <input
                      id="vehicleModel"
                      name="vehicleModel"
                      type="text"
                      required
                      className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                      placeholder="e.g., Camry, Civic, Focus"
                      value={vehicleModel}
                      onChange={(e) => setVehicleModel(e.target.value)}
                      autoCapitalize="words"
                      autoCorrect="off"
                      spellCheck="false"
                    />
                  </div>

                  <div>
                    <label htmlFor="vehicleColor" className="block text-sm font-medium text-gray-700 mb-2">
                      Vehicle Color
                    </label>
                    <input
                      id="vehicleColor"
                      name="vehicleColor"
                      type="text"
                      required
                      className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                      placeholder="e.g., White, Black, Silver"
                      value={vehicleColor}
                      onChange={(e) => setVehicleColor(e.target.value)}
                      autoCapitalize="words"
                      autoCorrect="off"
                      spellCheck="false"
                    />
                  </div>

                  <div>
                    <label htmlFor="licensePlate" className="block text-sm font-medium text-gray-700 mb-2">
                      License Plate Number
                    </label>
                    <input
                      id="licensePlate"
                      name="licensePlate"
                      type="text"
                      required
                      className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors"
                      placeholder="Enter license plate number"
                      value={licensePlate}
                      onChange={(e) => setLicensePlate(e.target.value)}
                      autoCapitalize="characters"
                      autoCorrect="off"
                      spellCheck="false"
                    />
                  </div>

                  <div className="border-t-2 border-gray-200 pt-6 mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Verification</h3>
                    <div>
                      <label htmlFor="baroRideIdPassword" className="block text-sm font-medium text-gray-700 mb-2">
                        BaroRide ID Password
                      </label>
                      <div className="relative">
                        <input
                          id="baroRideIdPassword"
                          name="baroRideIdPassword"
                          type={showBaroRidePassword ? "text" : "password"}
                          required
                          className="mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors"
                          placeholder="Enter BaroRide ID Password"
                          value={baroRideIdPassword}
                          onChange={(e) => setBaroRideIdPassword(e.target.value)}
                          autoCapitalize="none"
                          autoCorrect="off"
                          spellCheck="false"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target"
                          onClick={() => setShowBaroRidePassword(!showBaroRidePassword)}
                          aria-label={showBaroRidePassword ? "Hide password" : "Show password"}
                        >
                          {showBaroRidePassword ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">This password is required for all driver registrations. Contact support if you don't have it.</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit button */}
              <button
                type="submit"
                disabled={isLoading}
                className={`mobile-button w-full py-4 px-6 rounded-lg font-semibold text-base transition-all duration-200 touch-target ${
                  isLoading
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl'
                } text-white flex items-center justify-center mt-8`}
              >
                {isLoading ? (
                  <>
                    <div className="mobile-spinner mr-3"></div>
                    Creating Account...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Create Account
                  </>
                )}
              </button>
            </form>

            {/* Sign in link */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <a href="/login" className="text-blue-600 hover:text-blue-700 font-semibold transition-colors touch-target">
                  Sign in here
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
