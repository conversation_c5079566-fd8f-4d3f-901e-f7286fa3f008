/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\n/* harmony import */ var _MobileOptimizer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MobileOptimizer */ \"./src/components/MobileOptimizer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Navbar__WEBPACK_IMPORTED_MODULE_2__]);\n_Navbar__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction Layout({ children, title = \"BaroRide\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileOptimizer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Book your ride with BaroRide - fixed fares and reliable service\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileOptimizer__WEBPACK_IMPORTED_MODULE_3__.MobileStyles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white text-gray-900 safe-area-top safe-area-bottom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-6 safe-area-left safe-area-right\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-100 border-t border-gray-200 py-4 mt-auto safe-area-bottom\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center text-gray-600 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" BaroRide. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/MobileOptimizer.tsx":
/*!********************************************!*\
  !*** ./src/components/MobileOptimizer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileStyles: () => (/* binding */ MobileStyles),\n/* harmony export */   \"default\": () => (/* binding */ MobileOptimizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/mobile-optimization */ \"./src/utils/mobile-optimization.ts\");\n\n\n\nfunction MobileOptimizer({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if (true) {\n            return;\n        }\n        // Initialize mobile optimizations when component mounts\n        (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n        // Add device-specific classes to body\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        // Clean up existing device classes\n        document.body.classList.remove(\"is-mobile\", \"is-desktop\", \"is-tablet\", \"is-ios\", \"is-android\", \"has-touch\", \"no-touch\");\n        // Add current device classes\n        document.body.classList.add(deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\", deviceInfo.isTablet ? \"is-tablet\" : \"\", deviceInfo.isIOS ? \"is-ios\" : \"\", deviceInfo.isAndroid ? \"is-android\" : \"\", deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\");\n        // Add mobile-specific meta tags if on mobile\n        if (deviceInfo.isMobile) {\n            // Ensure viewport meta tag is properly set\n            let viewportMeta = document.querySelector('meta[name=\"viewport\"]');\n            if (!viewportMeta) {\n                viewportMeta = document.createElement(\"meta\");\n                viewportMeta.setAttribute(\"name\", \"viewport\");\n                document.head.appendChild(viewportMeta);\n            }\n            viewportMeta.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\");\n            // Add mobile web app meta tags\n            const addMetaTag = (name, content)=>{\n                let meta = document.querySelector(`meta[name=\"${name}\"]`);\n                if (!meta) {\n                    meta = document.createElement(\"meta\");\n                    meta.setAttribute(\"name\", name);\n                    document.head.appendChild(meta);\n                }\n                meta.setAttribute(\"content\", content);\n            };\n            addMetaTag(\"mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-capable\", \"yes\");\n            addMetaTag(\"apple-mobile-web-app-status-bar-style\", \"default\");\n            addMetaTag(\"apple-mobile-web-app-title\", \"BaroRide\");\n            addMetaTag(\"theme-color\", \"#1e3a5f\");\n            addMetaTag(\"format-detection\", \"telephone=no\");\n        }\n        // Handle orientation changes\n        const handleOrientationChange = ()=>{\n            // Re-initialize optimizations after orientation change\n            setTimeout(()=>{\n                (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.initializeMobileOptimizations)();\n            }, 100);\n        };\n        window.addEventListener(\"orientationchange\", handleOrientationChange);\n        window.addEventListener(\"resize\", handleOrientationChange);\n        // Cleanup\n        return ()=>{\n            window.removeEventListener(\"orientationchange\", handleOrientationChange);\n            window.removeEventListener(\"resize\", handleOrientationChange);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// CSS-in-JS styles for mobile optimizations\nconst MobileStyles = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run in browser environment\n        if (true) {\n            return;\n        }\n        const deviceInfo = (0,_utils_mobile_optimization__WEBPACK_IMPORTED_MODULE_2__.getDeviceInfo)();\n        if (deviceInfo.isMobile) {\n            // Add mobile-specific styles\n            const style = document.createElement(\"style\");\n            style.textContent = `\n        /* Mobile-specific overrides */\n        .is-mobile input,\n        .is-mobile select,\n        .is-mobile textarea {\n          font-size: 16px !important;\n          -webkit-appearance: none;\n          -moz-appearance: none;\n          appearance: none;\n        }\n\n        .is-mobile button {\n          min-height: 44px;\n          touch-action: manipulation;\n          -webkit-tap-highlight-color: transparent;\n        }\n\n        .is-mobile .map-container {\n          touch-action: none;\n        }\n\n        /* Prevent zoom on input focus */\n        .is-mobile input:focus,\n        .is-mobile select:focus,\n        .is-mobile textarea:focus {\n          font-size: 16px !important;\n        }\n\n        /* Better scrolling on mobile */\n        .is-mobile {\n          -webkit-overflow-scrolling: touch;\n        }\n\n        /* Hide scrollbars on mobile for cleaner look */\n        .is-mobile ::-webkit-scrollbar {\n          width: 0px;\n          background: transparent;\n        }\n\n        /* Mobile-specific form improvements */\n        .is-mobile .mobile-form {\n          padding: 16px;\n        }\n\n        .is-mobile .mobile-form input,\n        .is-mobile .mobile-form select,\n        .is-mobile .mobile-form textarea {\n          padding: 16px;\n          border-radius: 12px;\n          border: 2px solid #e5e7eb;\n          font-size: 16px !important;\n        }\n\n        .is-mobile .mobile-form button {\n          padding: 16px;\n          border-radius: 12px;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        /* Mobile navigation improvements */\n        .is-mobile .mobile-nav {\n          position: sticky;\n          top: 0;\n          z-index: 50;\n          background: white;\n          border-bottom: 1px solid #e5e7eb;\n          padding: 12px 16px;\n        }\n\n        /* Mobile card improvements */\n        .is-mobile .mobile-card {\n          margin: 8px;\n          border-radius: 16px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n        }\n\n        /* Mobile modal improvements */\n        .is-mobile .mobile-modal {\n          padding: 16px;\n        }\n\n        .is-mobile .mobile-modal-content {\n          border-radius: 16px;\n          max-height: 85vh;\n        }\n\n        /* Mobile table improvements */\n        .is-mobile .mobile-table {\n          font-size: 14px;\n        }\n\n        .is-mobile .mobile-table th,\n        .is-mobile .mobile-table td {\n          padding: 12px 8px;\n        }\n\n        /* Mobile notification improvements */\n        .is-mobile .mobile-notification {\n          margin: 16px;\n          border-radius: 12px;\n          padding: 16px;\n          font-size: 16px;\n        }\n\n        /* Keyboard handling */\n        .is-mobile.keyboard-open {\n          position: fixed;\n          width: 100%;\n        }\n\n        /* Safe area handling for devices with notches */\n        .is-mobile .safe-area-top {\n          padding-top: max(16px, env(safe-area-inset-top));\n        }\n\n        .is-mobile .safe-area-bottom {\n          padding-bottom: max(16px, env(safe-area-inset-bottom));\n        }\n\n        .is-mobile .safe-area-left {\n          padding-left: max(16px, env(safe-area-inset-left));\n        }\n\n        .is-mobile .safe-area-right {\n          padding-right: max(16px, env(safe-area-inset-right));\n        }\n      `;\n            document.head.appendChild(style);\n            return ()=>{\n                document.head.removeChild(style);\n            };\n        }\n    }, []);\n    return null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MobileOptimizer.tsx\n");

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NotificationBell */ \"./src/components/NotificationBell.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _NotificationBell__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _NotificationBell__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Navbar() {\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBackButton, setShowBackButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Determine if back button should be shown based on current route\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Show back button on all pages except home page\n        setShowBackButton(router.pathname !== \"/\");\n    }, [\n        router.pathname\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleBack = ()=>{\n        router.back();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-2 sm:px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors\",\n                                    \"aria-label\": \"Go back\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 sm:w-7 sm:h-7 text-gray-700\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo-icon.svg\",\n                                            alt: \"BaroRide Logo\",\n                                            className: \"h-8 w-8 sm:h-10 sm:w-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600 font-bold text-lg sm:text-xl\",\n                                            children: \"BaroRide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/book\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Book a Ride\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/driver/dashboard\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: user.fullName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-1\",\n                            children: [\n                                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 22\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 border-b border-gray-100 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 capitalize\",\n                                        children: user.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this),\n                            user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/book\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDCF1 Book a Ride\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 19\n                            }, this) : user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/driver/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDE97 Driver Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"⚙️ Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    signOut();\n                                    setIsMenuOpen(false);\n                                },\n                                className: \"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                children: \"\\uD83D\\uDEAA Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDD11 Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"✨ Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n");

/***/ }),

/***/ "./src/components/Notification.tsx":
/*!*****************************************!*\
  !*** ./src/components/Notification.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Notification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Notification({ message, type = \"info\", duration = 5000, onClose, driverDetails }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setIsVisible(false);\n            if (onClose) onClose();\n        }, duration);\n        return ()=>clearTimeout(timer);\n    }, [\n        duration,\n        onClose\n    ]);\n    if (!isVisible) return null;\n    const getTypeStyles = ()=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 border-green-500 text-green-700\";\n            case \"warning\":\n                return \"bg-yellow-100 border-yellow-500 text-yellow-700\";\n            case \"error\":\n                return \"bg-red-100 border-red-500 text-red-700\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 border-blue-500 text-blue-700\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this);\n            case \"info\":\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 max-w-md shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `p-4 mb-4 text-sm rounded-lg border ${getTypeStyles()}`,\n            role: \"alert\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mr-2\",\n                            children: getIcon()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium flex-grow whitespace-pre-line\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500\",\n                            onClick: ()=>{\n                                setIsVisible(false);\n                                if (onClose) onClose();\n                            },\n                            \"aria-label\": \"Close\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-gray-600\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold\",\n                                        children: driverDetails.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            driverDetails.vehicleColor,\n                                            \" \",\n                                            driverDetails.vehicleMake,\n                                            \" \",\n                                            driverDetails.vehicleModel\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs font-medium\",\n                                        children: [\n                                            \"License Plate: \",\n                                            driverDetails.licensePlate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Phone: \",\n                                            driverDetails.phoneNumber\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Notification.tsx\n");

/***/ }),

/***/ "./src/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _firebase_config__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction NotificationBell() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) return;\n        // Listen for user notifications\n        // Note: We're not using orderBy to avoid Firestore index issues\n        const notificationsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", user.id));\n        const unsubscribe = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.onSnapshot)(notificationsQuery, (snapshot)=>{\n            const userNotifications = [];\n            let count = 0;\n            snapshot.forEach((doc)=>{\n                const data = doc.data();\n                const notification = {\n                    id: doc.id,\n                    ...data,\n                    createdAt: data.createdAt?.toDate() || new Date()\n                };\n                userNotifications.push(notification);\n                if (!notification.read) {\n                    count++;\n                }\n            });\n            // Sort notifications by createdAt in descending order (newest first)\n            userNotifications.sort((a, b)=>{\n                return b.createdAt.getTime() - a.createdAt.getTime();\n            });\n            setNotifications(userNotifications);\n            setUnreadCount(count);\n        });\n        return ()=>unsubscribe();\n    }, [\n        user\n    ]);\n    const markAsRead = async (notificationId)=>{\n        if (!user) return;\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notificationId), {\n                read: true\n            });\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n        }\n    };\n    const markAllAsRead = async ()=>{\n        if (!user) return;\n        try {\n            const promises = notifications.filter((notification)=>!notification.read).map((notification)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notification.id), {\n                    read: true\n                }));\n            await Promise.all(promises);\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n        }\n    };\n    const toggleDropdown = ()=>{\n        setShowDropdown(!showDropdown);\n    };\n    const getNotificationTypeStyles = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 text-blue-800\";\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleDropdown,\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                \"aria-label\": \"Notifications\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\",\n                        children: unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-100 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: markAllAsRead,\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                children: \"No notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `px-4 py-3 border-b border-gray-100 ${!notification.read ? \"bg-blue-50\" : \"\"}`,\n                                    onClick: ()=>markAsRead(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 rounded-full p-1 ${getNotificationTypeStyles(notification.type)}`,\n                                                children: [\n                                                    notification.type === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"warning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-900 whitespace-pre-line\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-500\",\n                                                        children: new Date(notification.createdAt).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    notification.driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold\",\n                                                                            children: notification.driverDetails.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                notification.driverDetails.vehicleColor,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleMake,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleModel\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium\",\n                                                                            children: [\n                                                                                \"License: \",\n                                                                                notification.driverDetails.licensePlate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/NotificationBell.tsx\n");

/***/ }),

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__]);\n([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.onAuthStateChanged)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", firebaseUser.uid));\n                const userData = userDoc.data();\n                setUser(userData);\n                setLoading(false);\n                // Auto-redirect users based on role if they're on the homepage\n                if (router.pathname === \"/\") {\n                    if (userData.role === \"admin\") {\n                        console.log(\"Admin detected on homepage, redirecting to admin dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/admin/dashboard\");\n                        }, 100);\n                    } else if (userData.role === \"driver\") {\n                        console.log(\"Driver detected on homepage, redirecting to driver dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/driver/dashboard\");\n                        }, 100);\n                    }\n                }\n            } else {\n                setUser(null);\n                setLoading(false);\n            }\n        });\n        return unsubscribe;\n    }, [\n        router\n    ]);\n    const signOut = async ()=>{\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth);\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Notification */ \"./src/components/Notification.tsx\");\n\n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    showNotification: ()=>{}\n});\nfunction NotificationProvider({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showNotification = (message, type = \"info\", duration = 5000, driverDetails)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type,\n                    duration,\n                    driverDetails\n                }\n            ]);\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            showNotification\n        },\n        children: [\n            children,\n            notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Notification__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    message: notification.message,\n                    type: notification.type,\n                    duration: notification.duration,\n                    driverDetails: notification.driverDetails,\n                    onClose: ()=>removeNotification(notification.id)\n                }, notification.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nconst useNotification = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "./src/contexts/RBACContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/RBACContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RBACProvider: () => (/* binding */ RBACProvider),\n/* harmony export */   useRBAC: () => (/* binding */ useRBAC)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// Create the context with default values\nconst RBACContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    hasAccess: ()=>false,\n    checkAccess: ()=>false,\n    userRole: \"guest\",\n    isAdmin: false,\n    isDriver: false,\n    isRider: false\n});\n// Define route access permissions\nconst routeAccess = {\n    \"/\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/login\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/signup\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/forgot-password\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/book\": [\n        \"admin\",\n        \"rider\"\n    ],\n    \"/driver\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/driver/dashboard\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/admin\": [\n        \"admin\"\n    ],\n    \"/admin/dashboard\": [\n        \"admin\"\n    ],\n    \"/admin/users\": [\n        \"admin\"\n    ],\n    \"/admin/bookings\": [\n        \"admin\"\n    ]\n};\n// RBAC Provider component\nfunction RBACProvider({ children }) {\n    const { user, loading } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showNotification } = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification)();\n    const [authorized, setAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Determine user role\n    const userRole = user ? user.role === \"admin\" ? \"admin\" : user.role === \"driver\" ? \"driver\" : \"rider\" : \"guest\";\n    // Check if user has access to a specific route\n    const hasAccess = (route)=>{\n        // Check if the route exists in our access map\n        const allowedRoles = routeAccess[route];\n        // If route is not defined in our access map, default to admin-only\n        if (!allowedRoles) {\n            return userRole === \"admin\";\n        }\n        // Check if user's role is in the allowed roles for this route\n        return allowedRoles.includes(userRole);\n    };\n    // Check if user has one of the required roles\n    const checkAccess = (requiredRoles)=>{\n        return requiredRoles.includes(userRole);\n    };\n    // Role-specific boolean flags for easier checks\n    const isAdmin = userRole === \"admin\";\n    const isDriver = userRole === \"driver\";\n    const isRider = userRole === \"rider\";\n    // Route protection effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Skip during initial loading\n        if (loading) return;\n        // Get the current path\n        const path = router.pathname;\n        // Check if user has access to the current route\n        const hasRouteAccess = hasAccess(path);\n        if (!hasRouteAccess) {\n            // Redirect to appropriate page based on role\n            if (userRole === \"guest\") {\n                router.push(\"/login\");\n                showNotification(\"Please log in to access this page\", \"warning\");\n            } else if (userRole === \"driver\") {\n                router.push(\"/driver/dashboard\");\n                showNotification(\"Access denied. Redirected to driver dashboard.\", \"warning\");\n            } else if (userRole === \"rider\") {\n                router.push(\"/\");\n                showNotification(\"Access denied. Redirected to home page.\", \"warning\");\n            } else {\n                router.push(\"/\");\n                showNotification(\"Access denied. Please contact support if you believe this is an error.\", \"error\");\n            }\n        } else {\n            setAuthorized(true);\n        }\n    }, [\n        router.pathname,\n        userRole,\n        loading\n    ]);\n    // Provide the context value\n    const contextValue = {\n        hasAccess,\n        checkAccess,\n        userRole,\n        isAdmin,\n        isDriver,\n        isRider\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RBACContext.Provider, {\n        value: contextValue,\n        children: authorized ? children : null\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\RBACContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the RBAC context\nconst useRBAC = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RBACContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/RBACContext.tsx\n");

/***/ }),

/***/ "./src/firebase/config.ts":
/*!********************************!*\
  !*** ./src/firebase/config.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"firebase/storage\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po\",\n    authDomain: \"baroride.firebaseapp.com\",\n    projectId: \"baroride\",\n    storageBucket: \"baroride.firebasestorage.app\",\n    messagingSenderId: \"191771619835\",\n    appId: \"1:191771619835:web:2fc57d131cf64a35e2db5e\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Configure Firestore for better performance in production\nif (false) {}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvZmlyZWJhc2UvY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNkM7QUFDTDtBQUNVO0FBQ0o7QUFFOUMsTUFBTUksaUJBQWlCO0lBQ3JCQyxRQUFRO0lBQ1JDLFlBQVk7SUFDWkMsV0FBVztJQUNYQyxlQUFlO0lBQ2ZDLG1CQUFtQjtJQUNuQkMsT0FBTztBQUNUO0FBRUEsc0JBQXNCO0FBQ3RCLE1BQU1DLE1BQU1YLDJEQUFhQSxDQUFDSTtBQUNuQixNQUFNUSxPQUFPWCxzREFBT0EsQ0FBQ1UsS0FBSztBQUMxQixNQUFNRSxLQUFLWCxnRUFBWUEsQ0FBQ1MsS0FBSztBQUM3QixNQUFNRyxVQUFVWCw0REFBVUEsQ0FBQ1EsS0FBSztBQUV2QywyREFBMkQ7QUFDM0QsSUFBSSxLQUFrQixFQUFhLEVBTWxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWlycG9ydC1yaWRlLWJvb2tpbmcvLi9zcmMvZmlyZWJhc2UvY29uZmlnLnRzPzMxOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW5pdGlhbGl6ZUFwcCB9IGZyb20gJ2ZpcmViYXNlL2FwcCc7XG5pbXBvcnQgeyBnZXRBdXRoIH0gZnJvbSAnZmlyZWJhc2UvYXV0aCc7XG5pbXBvcnQgeyBnZXRGaXJlc3RvcmUgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xuaW1wb3J0IHsgZ2V0U3RvcmFnZSB9IGZyb20gJ2ZpcmViYXNlL3N0b3JhZ2UnO1xuXG5jb25zdCBmaXJlYmFzZUNvbmZpZyA9IHtcbiAgYXBpS2V5OiBcIkFJemFTeUNwTTlrdDNOenV6aGVuTTdLd2ZHa05pTzlCLXAwLTRQb1wiLFxuICBhdXRoRG9tYWluOiBcImJhcm9yaWRlLmZpcmViYXNlYXBwLmNvbVwiLFxuICBwcm9qZWN0SWQ6IFwiYmFyb3JpZGVcIixcbiAgc3RvcmFnZUJ1Y2tldDogXCJiYXJvcmlkZS5maXJlYmFzZXN0b3JhZ2UuYXBwXCIsXG4gIG1lc3NhZ2luZ1NlbmRlcklkOiBcIjE5MTc3MTYxOTgzNVwiLFxuICBhcHBJZDogXCIxOjE5MTc3MTYxOTgzNTp3ZWI6MmZjNTdkMTMxY2Y2NGEzNWUyZGI1ZVwiXG59O1xuXG4vLyBJbml0aWFsaXplIEZpcmViYXNlXG5jb25zdCBhcHAgPSBpbml0aWFsaXplQXBwKGZpcmViYXNlQ29uZmlnKTtcbmV4cG9ydCBjb25zdCBhdXRoID0gZ2V0QXV0aChhcHApO1xuZXhwb3J0IGNvbnN0IGRiID0gZ2V0RmlyZXN0b3JlKGFwcCk7XG5leHBvcnQgY29uc3Qgc3RvcmFnZSA9IGdldFN0b3JhZ2UoYXBwKTtcblxuLy8gQ29uZmlndXJlIEZpcmVzdG9yZSBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlIGluIHByb2R1Y3Rpb25cbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAvLyBMb2cgaW5pdGlhbGl6YXRpb24gZm9yIGRlYnVnZ2luZ1xuICBjb25zb2xlLmxvZygnRmlyZWJhc2UgaW5pdGlhbGl6ZWQgaW4gYnJvd3NlciBlbnZpcm9ubWVudCcpO1xuXG4gIC8vIEFkZCBhZGRpdGlvbmFsIGNvbmZpZ3VyYXRpb24gaWYgbmVlZGVkIGZvciB0aGUgZGVwbG95ZWQgdmVyc2lvblxuICAvLyBUaGlzIGlzIHdoZXJlIHlvdSB3b3VsZCBhZGQgYW55IEZpcmViYXNlLXNwZWNpZmljIGNvbmZpZ3VyYXRpb24gZm9yIHByb2R1Y3Rpb25cbn0iXSwibmFtZXMiOlsiaW5pdGlhbGl6ZUFwcCIsImdldEF1dGgiLCJnZXRGaXJlc3RvcmUiLCJnZXRTdG9yYWdlIiwiZmlyZWJhc2VDb25maWciLCJhcGlLZXkiLCJhdXRoRG9tYWluIiwicHJvamVjdElkIiwic3RvcmFnZUJ1Y2tldCIsIm1lc3NhZ2luZ1NlbmRlcklkIiwiYXBwSWQiLCJhcHAiLCJhdXRoIiwiZGIiLCJzdG9yYWdlIiwiY29uc29sZSIsImxvZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/firebase/config.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/RBACContext */ \"./src/contexts/RBACContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.RBACProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBRXdCO0FBQ2dCO0FBQ2hCO0FBRXRELFNBQVNHLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDL0MscUJBQ0UsOERBQUNMLCtEQUFZQTtrQkFDWCw0RUFBQ0MsK0VBQW9CQTtzQkFDbkIsNEVBQUNDLCtEQUFZQTswQkFDWCw0RUFBQ0U7b0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2xDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJwb3J0LXJpZGUtYm9va2luZy8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgTm90aWZpY2F0aW9uUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL05vdGlmaWNhdGlvbkNvbnRleHQnO1xuaW1wb3J0IHsgUkJBQ1Byb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9SQkFDQ29udGV4dCc7XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPE5vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgICAgICA8UkJBQ1Byb3ZpZGVyPlxuICAgICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgICAgPC9SQkFDUHJvdmlkZXI+XG4gICAgICA8L05vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDtcbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJOb3RpZmljYXRpb25Qcm92aWRlciIsIlJCQUNQcm92aWRlciIsIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-navbutton-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_3__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Home = ()=>{\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [isNavigating, setIsNavigating] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Show welcome notification when the page loads\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (user) {\n            showNotification(`Welcome back, ${user.fullName}!`, \"info\", 3000); // Auto-dismiss after 3 seconds\n        }\n    }, [\n        user,\n        showNotification\n    ]);\n    // We'll let the AuthContext handle the automatic redirection\n    // Function to navigate to the driver dashboard\n    const goToDriverDashboard = ()=>{\n        setIsNavigating(true);\n        showNotification(\"Opening Driver Dashboard...\", \"info\", 2000);\n        // Use both methods to ensure navigation works\n        window.location.href = \"/driver/dashboard\"; // Direct browser navigation as fallback\n        router.push(\"/driver/dashboard\");\n    };\n    // Function to navigate to the book a ride page\n    const goToBookRide = ()=>{\n        setIsNavigating(true);\n        showNotification(\"Opening Book a Ride...\", \"info\", 2000);\n        // Use both methods to ensure navigation works\n        window.location.href = \"/book\"; // Direct browser navigation as fallback\n        router.push(\"/book\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"BaroRide - Book Your Ride\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo.svg\",\n                                    alt: \"BaroRide Logo\",\n                                    className: \"h-32 w-auto sm:h-40 md:h-48\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4\",\n                                children: \"Welcome to BaroRide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl sm:text-2xl text-gray-600 mb-8\",\n                                children: \"Your reliable ride-sharing service in Gambela, Ethiopia\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined),\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goToDriverDashboard,\n                            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation\",\n                            style: {\n                                touchAction: \"manipulation\"\n                            },\n                            children: \"Open Driver Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 17\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goToBookRide,\n                            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation\",\n                            style: {\n                                touchAction: \"manipulation\"\n                            },\n                            children: \"Book a Ride Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-md rounded-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6 text-center\",\n                                children: \"Book your ride with fixed fares and professional drivers.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ${isNavigating ? \"opacity-75 cursor-wait\" : \"\"}`,\n                                    onClick: goToBookRide,\n                                    disabled: isNavigating,\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: isNavigating ? \"Opening Booking...\" : \"Book a Ride\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ${isNavigating ? \"opacity-75 cursor-wait\" : \"\"}`,\n                                    onClick: goToDriverDashboard,\n                                    disabled: isNavigating,\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: isNavigating ? \"Opening Dashboard...\" : \"Go to Driver Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/login\",\n                                        className: \"bg-gray-200 text-gray-800 px-6 py-3 rounded-md hover:bg-gray-300 inline-block\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 inline-block\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Fixed Fares\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"Know exactly what you'll pay before booking your ride.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Professional Drivers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"All our drivers are vetted and professionally trained.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Easy Booking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"Book your ride in just a few clicks, anytime, anywhere.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkI7QUFDb0I7QUFDUjtBQUN3QjtBQUNyQjtBQUNKO0FBRXhDLE1BQU1PLE9BQWlCO0lBQ3JCLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdQLDhEQUFPQTtJQUN4QixNQUFNLEVBQUVRLGdCQUFnQixFQUFFLEdBQUdOLDhFQUFlQTtJQUM1QyxNQUFNTyxTQUFTSixzREFBU0E7SUFDeEIsTUFBTSxDQUFDSyxjQUFjQyxnQkFBZ0IsR0FBR1AsK0NBQVFBLENBQUM7SUFFakQsZ0RBQWdEO0lBQ2hERCxnREFBU0EsQ0FBQztRQUNSLElBQUlJLE1BQU07WUFDUkMsaUJBQWlCLENBQUMsY0FBYyxFQUFFRCxLQUFLSyxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUUsUUFBUSxPQUFPLCtCQUErQjtRQUNwRztJQUNGLEdBQUc7UUFBQ0w7UUFBTUM7S0FBaUI7SUFFM0IsNkRBQTZEO0lBRTdELCtDQUErQztJQUMvQyxNQUFNSyxzQkFBc0I7UUFDMUJGLGdCQUFnQjtRQUNoQkgsaUJBQWlCLCtCQUErQixRQUFRO1FBRXhELDhDQUE4QztRQUM5Q00sT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUcscUJBQXFCLHdDQUF3QztRQUNwRlAsT0FBT1EsSUFBSSxDQUFDO0lBQ2Q7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTUMsZUFBZTtRQUNuQlAsZ0JBQWdCO1FBQ2hCSCxpQkFBaUIsMEJBQTBCLFFBQVE7UUFFbkQsOENBQThDO1FBQzlDTSxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRyxTQUFTLHdDQUF3QztRQUN4RVAsT0FBT1EsSUFBSSxDQUFDO0lBQ2Q7SUFFQSxxQkFDRSw4REFBQ2hCLDBEQUFNQTtRQUFDa0IsT0FBTTtrQkFDWiw0RUFBQ0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDQztvQ0FDQ0MsS0FBSTtvQ0FDSkMsS0FBSTtvQ0FDSkgsV0FBVTs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNJO2dDQUFHSixXQUFVOzBDQUFnRTs7Ozs7OzBDQUc5RSw4REFBQ0s7Z0NBQUVMLFdBQVU7MENBQXlDOzs7Ozs7Ozs7Ozs7b0JBTXZEZCxzQkFDQyw4REFBQ2E7d0JBQUlDLFdBQVU7a0NBQ1pkLEtBQUtvQixJQUFJLEtBQUsseUJBQ2IsOERBQUNDOzRCQUNDQyxTQUFTaEI7NEJBQ1RRLFdBQVU7NEJBQ1ZTLE9BQU87Z0NBQUVDLGFBQWE7NEJBQWU7c0NBQ3RDOzs7OztzREFJRCw4REFBQ0g7NEJBQ0NDLFNBQVNYOzRCQUNURyxXQUFVOzRCQUNWUyxPQUFPO2dDQUFFQyxhQUFhOzRCQUFlO3NDQUN0Qzs7Ozs7Ozs7Ozs7a0NBT1AsOERBQUNYO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQUVMLFdBQVU7MENBQXlDOzs7Ozs7NEJBSXJEZCxxQkFDQyw4REFBQ2E7Z0NBQUlDLFdBQVU7MENBQ1pkLEtBQUtvQixJQUFJLEtBQUssd0JBQ2IsOERBQUNDO29DQUNDUCxXQUFXLENBQUMsa0xBQWtMLEVBQUVYLGVBQWUsMkJBQTJCLEdBQUcsQ0FBQztvQ0FDOU9tQixTQUFTWDtvQ0FDVGMsVUFBVXRCO29DQUNWb0IsT0FBTzt3Q0FBRUMsYUFBYTtvQ0FBZTs4Q0FFcENyQixlQUFlLHVCQUF1Qjs7Ozs7OERBR3pDLDhEQUFDa0I7b0NBQ0NQLFdBQVcsQ0FBQyxrTEFBa0wsRUFBRVgsZUFBZSwyQkFBMkIsR0FBRyxDQUFDO29DQUM5T21CLFNBQVNoQjtvQ0FDVG1CLFVBQVV0QjtvQ0FDVm9CLE9BQU87d0NBQUVDLGFBQWE7b0NBQWU7OENBRXBDckIsZUFBZSx5QkFBeUI7Ozs7Ozs7Ozs7MERBSy9DLDhEQUFDVTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN0QixrREFBSUE7d0NBQUNpQixNQUFLO3dDQUFTSyxXQUFVO2tEQUFnRjs7Ozs7O2tEQUc5Ryw4REFBQ3RCLGtEQUFJQTt3Q0FBQ2lCLE1BQUs7d0NBQVVLLFdBQVU7a0RBQTZFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT2xILDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1k7d0NBQUdaLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDSzt3Q0FBRUwsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FHL0IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1k7d0NBQUdaLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDSzt3Q0FBRUwsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FHL0IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1k7d0NBQUdaLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDSzt3Q0FBRUwsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPM0M7QUFFQSxpRUFBZWYsSUFBSUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpcnBvcnQtcmlkZS1ib29raW5nLy4vc3JjL3BhZ2VzL2luZGV4LnRzeD8xOWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRQYWdlIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvTGF5b3V0JztcbmltcG9ydCB7IHVzZU5vdGlmaWNhdGlvbiB9IGZyb20gJ0AvY29udGV4dHMvTm90aWZpY2F0aW9uQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuXG5jb25zdCBIb21lOiBOZXh0UGFnZSA9ICgpID0+IHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgc2hvd05vdGlmaWNhdGlvbiB9ID0gdXNlTm90aWZpY2F0aW9uKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbaXNOYXZpZ2F0aW5nLCBzZXRJc05hdmlnYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIFNob3cgd2VsY29tZSBub3RpZmljYXRpb24gd2hlbiB0aGUgcGFnZSBsb2Fkc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyKSB7XG4gICAgICBzaG93Tm90aWZpY2F0aW9uKGBXZWxjb21lIGJhY2ssICR7dXNlci5mdWxsTmFtZX0hYCwgJ2luZm8nLCAzMDAwKTsgLy8gQXV0by1kaXNtaXNzIGFmdGVyIDMgc2Vjb25kc1xuICAgIH1cbiAgfSwgW3VzZXIsIHNob3dOb3RpZmljYXRpb25dKTtcblxuICAvLyBXZSdsbCBsZXQgdGhlIEF1dGhDb250ZXh0IGhhbmRsZSB0aGUgYXV0b21hdGljIHJlZGlyZWN0aW9uXG5cbiAgLy8gRnVuY3Rpb24gdG8gbmF2aWdhdGUgdG8gdGhlIGRyaXZlciBkYXNoYm9hcmRcbiAgY29uc3QgZ29Ub0RyaXZlckRhc2hib2FyZCA9ICgpID0+IHtcbiAgICBzZXRJc05hdmlnYXRpbmcodHJ1ZSk7XG4gICAgc2hvd05vdGlmaWNhdGlvbignT3BlbmluZyBEcml2ZXIgRGFzaGJvYXJkLi4uJywgJ2luZm8nLCAyMDAwKTtcblxuICAgIC8vIFVzZSBib3RoIG1ldGhvZHMgdG8gZW5zdXJlIG5hdmlnYXRpb24gd29ya3NcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvZHJpdmVyL2Rhc2hib2FyZCc7IC8vIERpcmVjdCBicm93c2VyIG5hdmlnYXRpb24gYXMgZmFsbGJhY2tcbiAgICByb3V0ZXIucHVzaCgnL2RyaXZlci9kYXNoYm9hcmQnKTtcbiAgfTtcblxuICAvLyBGdW5jdGlvbiB0byBuYXZpZ2F0ZSB0byB0aGUgYm9vayBhIHJpZGUgcGFnZVxuICBjb25zdCBnb1RvQm9va1JpZGUgPSAoKSA9PiB7XG4gICAgc2V0SXNOYXZpZ2F0aW5nKHRydWUpO1xuICAgIHNob3dOb3RpZmljYXRpb24oJ09wZW5pbmcgQm9vayBhIFJpZGUuLi4nLCAnaW5mbycsIDIwMDApO1xuXG4gICAgLy8gVXNlIGJvdGggbWV0aG9kcyB0byBlbnN1cmUgbmF2aWdhdGlvbiB3b3Jrc1xuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9ib29rJzsgLy8gRGlyZWN0IGJyb3dzZXIgbmF2aWdhdGlvbiBhcyBmYWxsYmFja1xuICAgIHJvdXRlci5wdXNoKCcvYm9vaycpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPExheW91dCB0aXRsZT1cIkJhcm9SaWRlIC0gQm9vayBZb3VyIFJpZGVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICB7LyogSGVybyBTZWN0aW9uIHdpdGggTG9nbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPVwiL2xvZ28uc3ZnXCJcbiAgICAgICAgICAgICAgICBhbHQ9XCJCYXJvUmlkZSBMb2dvXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTMyIHctYXV0byBzbTpoLTQwIG1kOmgtNDhcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgc206dGV4dC01eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICBXZWxjb21lIHRvIEJhcm9SaWRlXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBzbTp0ZXh0LTJ4bCB0ZXh0LWdyYXktNjAwIG1iLThcIj5cbiAgICAgICAgICAgICAgWW91ciByZWxpYWJsZSByaWRlLXNoYXJpbmcgc2VydmljZSBpbiBHYW1iZWxhLCBFdGhpb3BpYVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFF1aWNrIGFjY2VzcyBidXR0b25zIGZvciB1c2VycyBiYXNlZCBvbiByb2xlICovfVxuICAgICAgICAgIHt1c2VyICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICB7dXNlci5yb2xlID09PSAnZHJpdmVyJyA/IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnb1RvRHJpdmVyRGFzaGJvYXJkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0zIHB4LTYgcm91bmRlZC1mdWxsIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSB0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdG91Y2hBY3Rpb246ICdtYW5pcHVsYXRpb24nIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgT3BlbiBEcml2ZXIgRGFzaGJvYXJkXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub0Jvb2tSaWRlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0zIHB4LTYgcm91bmRlZC1mdWxsIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSB0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgdG91Y2hBY3Rpb246ICdtYW5pcHVsYXRpb24nIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgQm9vayBhIFJpZGUgTm93XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctbWQgcm91bmRlZC1sZyBwLTggbWItOFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNzAwIG1iLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgQm9vayB5b3VyIHJpZGUgd2l0aCBmaXhlZCBmYXJlcyBhbmQgcHJvZmVzc2lvbmFsIGRyaXZlcnMuXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIHt1c2VyID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAge3VzZXIucm9sZSA9PT0gJ3JpZGVyJyA/IChcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC04IHB5LTQgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBpbmxpbmUtYmxvY2sgc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1IGZvbnQtbWVkaXVtIHRleHQtbGcgdG91Y2gtbWFuaXB1bGF0aW9uICR7aXNOYXZpZ2F0aW5nID8gJ29wYWNpdHktNzUgY3Vyc29yLXdhaXQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub0Jvb2tSaWRlfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNOYXZpZ2F0aW5nfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB0b3VjaEFjdGlvbjogJ21hbmlwdWxhdGlvbicgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2lzTmF2aWdhdGluZyA/ICdPcGVuaW5nIEJvb2tpbmcuLi4nIDogJ0Jvb2sgYSBSaWRlJ31cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtOCBweS00IHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS03MDAgaW5saW5lLWJsb2NrIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBmb250LW1lZGl1bSB0ZXh0LWxnIHRvdWNoLW1hbmlwdWxhdGlvbiAke2lzTmF2aWdhdGluZyA/ICdvcGFjaXR5LTc1IGN1cnNvci13YWl0JyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9Ecml2ZXJEYXNoYm9hcmR9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc05hdmlnYXRpbmd9XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHRvdWNoQWN0aW9uOiAnbWFuaXB1bGF0aW9uJyB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXNOYXZpZ2F0aW5nID8gJ09wZW5pbmcgRGFzaGJvYXJkLi4uJyA6ICdHbyB0byBEcml2ZXIgRGFzaGJvYXJkJ31cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2xvZ2luXCIgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgdGV4dC1ncmF5LTgwMCBweC02IHB5LTMgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTMwMCBpbmxpbmUtYmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIExvZ2luXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2lnbnVwXCIgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBpbmxpbmUtYmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIFNpZ24gVXBcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctbWQgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0zXCI+Rml4ZWQgRmFyZXM8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+S25vdyBleGFjdGx5IHdoYXQgeW91J2xsIHBheSBiZWZvcmUgYm9va2luZyB5b3VyIHJpZGUuPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LW1kIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItM1wiPlByb2Zlc3Npb25hbCBEcml2ZXJzPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiPkFsbCBvdXIgZHJpdmVycyBhcmUgdmV0dGVkIGFuZCBwcm9mZXNzaW9uYWxseSB0cmFpbmVkLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1tZCByb3VuZGVkLWxnIHAtNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTNcIj5FYXN5IEJvb2tpbmc8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCI+Qm9vayB5b3VyIHJpZGUgaW4ganVzdCBhIGZldyBjbGlja3MsIGFueXRpbWUsIGFueXdoZXJlLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTGF5b3V0PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSG9tZTsiXSwibmFtZXMiOlsiTGluayIsInVzZUF1dGgiLCJMYXlvdXQiLCJ1c2VOb3RpZmljYXRpb24iLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIkhvbWUiLCJ1c2VyIiwic2hvd05vdGlmaWNhdGlvbiIsInJvdXRlciIsImlzTmF2aWdhdGluZyIsInNldElzTmF2aWdhdGluZyIsImZ1bGxOYW1lIiwiZ29Ub0RyaXZlckRhc2hib2FyZCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsInB1c2giLCJnb1RvQm9va1JpZGUiLCJ0aXRsZSIsImRpdiIsImNsYXNzTmFtZSIsImltZyIsInNyYyIsImFsdCIsImgxIiwicCIsInJvbGUiLCJidXR0b24iLCJvbkNsaWNrIiwic3R5bGUiLCJ0b3VjaEFjdGlvbiIsImRpc2FibGVkIiwiaDIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/utils/mobile-optimization.ts":
/*!******************************************!*\
  !*** ./src/utils/mobile-optimization.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getDeviceInfo),\n/* harmony export */   getDeviceInfo: () => (/* binding */ getDeviceInfo),\n/* harmony export */   handleMobileKeyboard: () => (/* binding */ handleMobileKeyboard),\n/* harmony export */   handleMobileViewport: () => (/* binding */ handleMobileViewport),\n/* harmony export */   initializeMobileOptimizations: () => (/* binding */ initializeMobileOptimizations),\n/* harmony export */   needsMobileOptimization: () => (/* binding */ needsMobileOptimization),\n/* harmony export */   optimizeFormForMobile: () => (/* binding */ optimizeFormForMobile),\n/* harmony export */   optimizeMapForMobile: () => (/* binding */ optimizeMapForMobile),\n/* harmony export */   optimizeScrolling: () => (/* binding */ optimizeScrolling),\n/* harmony export */   optimizeTouchInteraction: () => (/* binding */ optimizeTouchInteraction),\n/* harmony export */   preventIOSZoom: () => (/* binding */ preventIOSZoom)\n/* harmony export */ });\n// Mobile optimization utilities for BaroRide\n// Detect device and browser information\nconst getDeviceInfo = ()=>{\n    if (true) {\n        // Server-side fallback\n        return {\n            isMobile: false,\n            isTablet: false,\n            isDesktop: true,\n            isIOS: false,\n            isAndroid: false,\n            isSafari: false,\n            isChrome: false,\n            screenWidth: 1920,\n            screenHeight: 1080,\n            pixelRatio: 1,\n            touchSupport: false,\n            orientation: \"landscape\"\n        };\n    }\n    const userAgent = navigator.userAgent.toLowerCase();\n    const screenWidth = window.screen.width;\n    const screenHeight = window.screen.height;\n    const pixelRatio = window.devicePixelRatio || 1;\n    // Device detection\n    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;\n    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || screenWidth > 768 && screenWidth <= 1024;\n    const isDesktop = !isMobile && !isTablet;\n    // OS detection\n    const isIOS = /iphone|ipad|ipod/i.test(userAgent);\n    const isAndroid = /android/i.test(userAgent);\n    // Browser detection\n    const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent);\n    const isChrome = /chrome/i.test(userAgent);\n    // Touch support\n    const touchSupport = \"ontouchstart\" in window || navigator.maxTouchPoints > 0;\n    // Orientation\n    const orientation = screenWidth > screenHeight ? \"landscape\" : \"portrait\";\n    return {\n        isMobile,\n        isTablet,\n        isDesktop,\n        isIOS,\n        isAndroid,\n        isSafari,\n        isChrome,\n        screenWidth,\n        screenHeight,\n        pixelRatio,\n        touchSupport,\n        orientation\n    };\n};\n// Optimize touch interactions\nconst optimizeTouchInteraction = (element)=>{\n    if (!element) return;\n    // Prevent iOS zoom on double tap\n    element.style.touchAction = \"manipulation\";\n    // Remove tap highlight\n    element.style.webkitTapHighlightColor = \"transparent\";\n    // Ensure minimum touch target size (44px)\n    const computedStyle = window.getComputedStyle(element);\n    const minSize = 44;\n    if (parseInt(computedStyle.height) < minSize) {\n        element.style.minHeight = `${minSize}px`;\n    }\n    if (parseInt(computedStyle.width) < minSize) {\n        element.style.minWidth = `${minSize}px`;\n    }\n};\n// Prevent iOS zoom on input focus\nconst preventIOSZoom = ()=>{\n    if (true) return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isIOS) return;\n    // Set font size to 16px to prevent zoom\n    const inputs = document.querySelectorAll(\"input, select, textarea\");\n    inputs.forEach((input)=>{\n        const element = input;\n        if (element.style.fontSize === \"\" || parseInt(element.style.fontSize) < 16) {\n            element.style.fontSize = \"16px\";\n        }\n    });\n};\n// Handle viewport height issues on mobile\nconst handleMobileViewport = ()=>{\n    if (true) return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Set CSS custom property for actual viewport height\n    const setVH = ()=>{\n        const vh = window.innerHeight * 0.01;\n        document.documentElement.style.setProperty(\"--vh\", `${vh}px`);\n    };\n    setVH();\n    window.addEventListener(\"resize\", setVH);\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(setVH, 100); // Delay to ensure orientation change is complete\n    });\n};\n// Optimize map interactions for mobile\nconst optimizeMapForMobile = (mapContainer)=>{\n    if (!mapContainer || \"undefined\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (deviceInfo.isMobile) {\n        // Prevent page scroll when interacting with map\n        mapContainer.style.touchAction = \"none\";\n        // Add mobile-specific event listeners\n        let isMapInteracting = false;\n        mapContainer.addEventListener(\"touchstart\", ()=>{\n            isMapInteracting = true;\n            document.body.style.overflow = \"hidden\";\n        }, {\n            passive: true\n        });\n        mapContainer.addEventListener(\"touchend\", ()=>{\n            isMapInteracting = false;\n            document.body.style.overflow = \"\";\n        }, {\n            passive: true\n        });\n        // Handle map container sizing\n        const resizeMap = ()=>{\n            const containerHeight = Math.min(window.innerHeight * 0.4, 400);\n            mapContainer.style.height = `${containerHeight}px`;\n        };\n        resizeMap();\n        window.addEventListener(\"resize\", resizeMap);\n        window.addEventListener(\"orientationchange\", ()=>{\n            setTimeout(resizeMap, 100);\n        });\n    }\n};\n// Optimize form interactions for mobile\nconst optimizeFormForMobile = (form)=>{\n    if (!form || \"undefined\" === \"undefined\") return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    // Optimize all inputs in the form\n    const inputs = form.querySelectorAll(\"input, select, textarea, button\");\n    inputs.forEach((input)=>{\n        const element = input;\n        optimizeTouchInteraction(element);\n        // Add mobile-specific attributes\n        if (element.tagName === \"INPUT\") {\n            const inputElement = element;\n            // Prevent autocorrect and autocapitalize for certain input types\n            if (inputElement.type === \"email\" || inputElement.type === \"url\") {\n                inputElement.setAttribute(\"autocorrect\", \"off\");\n                inputElement.setAttribute(\"autocapitalize\", \"none\");\n                inputElement.setAttribute(\"spellcheck\", \"false\");\n            }\n            // Set appropriate input modes\n            if (inputElement.type === \"tel\") {\n                inputElement.setAttribute(\"inputmode\", \"tel\");\n            } else if (inputElement.type === \"email\") {\n                inputElement.setAttribute(\"inputmode\", \"email\");\n            } else if (inputElement.type === \"number\") {\n                inputElement.setAttribute(\"inputmode\", \"numeric\");\n            }\n        }\n    });\n};\n// Handle keyboard visibility on mobile\nconst handleMobileKeyboard = ()=>{\n    if (true) return;\n    const deviceInfo = getDeviceInfo();\n    if (!deviceInfo.isMobile) return;\n    let initialViewportHeight = window.innerHeight;\n    const handleResize = ()=>{\n        const currentHeight = window.innerHeight;\n        const heightDifference = initialViewportHeight - currentHeight;\n        // If height decreased significantly, keyboard is likely open\n        if (heightDifference > 150) {\n            document.body.classList.add(\"keyboard-open\");\n            // Scroll active input into view\n            const activeElement = document.activeElement;\n            if (activeElement && (activeElement.tagName === \"INPUT\" || activeElement.tagName === \"TEXTAREA\")) {\n                setTimeout(()=>{\n                    activeElement.scrollIntoView({\n                        behavior: \"smooth\",\n                        block: \"center\"\n                    });\n                }, 100);\n            }\n        } else {\n            document.body.classList.remove(\"keyboard-open\");\n        }\n    };\n    window.addEventListener(\"resize\", handleResize);\n    // Reset on orientation change\n    window.addEventListener(\"orientationchange\", ()=>{\n        setTimeout(()=>{\n            initialViewportHeight = window.innerHeight;\n        }, 500);\n    });\n};\n// Optimize scrolling performance\nconst optimizeScrolling = ()=>{\n    if (true) return;\n    // Enable smooth scrolling\n    document.documentElement.style.scrollBehavior = \"smooth\";\n    // Enable smooth scrolling on iOS\n    document.documentElement.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Add momentum scrolling for iOS\n    document.body.style[\"webkitOverflowScrolling\"] = \"touch\";\n    // Optimize scroll containers\n    const scrollContainers = document.querySelectorAll(\".overflow-auto, .overflow-y-auto, .overflow-x-auto\");\n    scrollContainers.forEach((container)=>{\n        const element = container;\n        element.style[\"webkitOverflowScrolling\"] = \"touch\";\n    });\n};\n// Initialize all mobile optimizations\nconst initializeMobileOptimizations = ()=>{\n    if (true) return;\n    // Wait for DOM to be ready\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>{\n            initializeMobileOptimizations();\n        });\n        return;\n    }\n    const deviceInfo = getDeviceInfo();\n    // Add device classes to body (only in browser environment)\n    if (typeof document !== \"undefined\" && document.body) {\n        document.body.classList.add(deviceInfo.isMobile ? \"is-mobile\" : \"is-desktop\", deviceInfo.isTablet ? \"is-tablet\" : \"\", deviceInfo.isIOS ? \"is-ios\" : \"\", deviceInfo.isAndroid ? \"is-android\" : \"\", deviceInfo.touchSupport ? \"has-touch\" : \"no-touch\");\n    }\n    // Apply optimizations\n    handleMobileViewport();\n    preventIOSZoom();\n    handleMobileKeyboard();\n    optimizeScrolling();\n    // Optimize existing forms (only in browser environment)\n    if (typeof document !== \"undefined\") {\n        const forms = document.querySelectorAll(\"form\");\n        forms.forEach(optimizeFormForMobile);\n        // Optimize existing maps\n        const mapContainers = document.querySelectorAll('.map-container, [id*=\"map\"]');\n        mapContainers.forEach((container)=>optimizeMapForMobile(container));\n    }\n    console.log(\"Mobile optimizations initialized for:\", deviceInfo);\n};\n// Utility to check if device needs mobile optimizations\nconst needsMobileOptimization = ()=>{\n    const deviceInfo = getDeviceInfo();\n    return deviceInfo.isMobile || deviceInfo.isTablet;\n};\n// Export device info for use in components\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/mobile-optimization.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "firebase/storage":
/*!***********************************!*\
  !*** external "firebase/storage" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/storage");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();