// Mobile optimization utilities for BaroRide

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isSafari: boolean;
  isChrome: boolean;
  screenWidth: number;
  screenHeight: number;
  pixelRatio: number;
  touchSupport: boolean;
  orientation: 'portrait' | 'landscape';
}

// Detect device and browser information
export const getDeviceInfo = (): DeviceInfo => {
  if (typeof window === 'undefined') {
    // Server-side fallback
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isIOS: false,
      isAndroid: false,
      isSafari: false,
      isChrome: false,
      screenWidth: 1920,
      screenHeight: 1080,
      pixelRatio: 1,
      touchSupport: false,
      orientation: 'landscape'
    };
  }

  const userAgent = navigator.userAgent.toLowerCase();
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  const pixelRatio = window.devicePixelRatio || 1;

  // Device detection
  const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || screenWidth <= 768;
  const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || (screenWidth > 768 && screenWidth <= 1024);
  const isDesktop = !isMobile && !isTablet;

  // OS detection
  const isIOS = /iphone|ipad|ipod/i.test(userAgent);
  const isAndroid = /android/i.test(userAgent);

  // Browser detection
  const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent);
  const isChrome = /chrome/i.test(userAgent);

  // Touch support
  const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // Orientation
  const orientation = screenWidth > screenHeight ? 'landscape' : 'portrait';

  return {
    isMobile,
    isTablet,
    isDesktop,
    isIOS,
    isAndroid,
    isSafari,
    isChrome,
    screenWidth,
    screenHeight,
    pixelRatio,
    touchSupport,
    orientation
  };
};

// Optimize touch interactions
export const optimizeTouchInteraction = (element: HTMLElement): void => {
  if (!element) return;

  // Prevent iOS zoom on double tap
  element.style.touchAction = 'manipulation';
  
  // Remove tap highlight
  (element.style as any).webkitTapHighlightColor = 'transparent';
  
  // Ensure minimum touch target size (44px)
  const computedStyle = window.getComputedStyle(element);
  const minSize = 44;
  
  if (parseInt(computedStyle.height) < minSize) {
    element.style.minHeight = `${minSize}px`;
  }
  
  if (parseInt(computedStyle.width) < minSize) {
    element.style.minWidth = `${minSize}px`;
  }
};

// Prevent iOS zoom on input focus
export const preventIOSZoom = (): void => {
  if (typeof window === 'undefined') return;

  const deviceInfo = getDeviceInfo();
  if (!deviceInfo.isIOS) return;

  // Set font size to 16px to prevent zoom
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach((input) => {
    const element = input as HTMLElement;
    if (element.style.fontSize === '' || parseInt(element.style.fontSize) < 16) {
      element.style.fontSize = '16px';
    }
  });
};

// Handle viewport height issues on mobile
export const handleMobileViewport = (): void => {
  if (typeof window === 'undefined') return;

  const deviceInfo = getDeviceInfo();
  if (!deviceInfo.isMobile) return;

  // Set CSS custom property for actual viewport height
  const setVH = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };

  setVH();
  window.addEventListener('resize', setVH);
  window.addEventListener('orientationchange', () => {
    setTimeout(setVH, 100); // Delay to ensure orientation change is complete
  });
};

// Optimize map interactions for mobile
export const optimizeMapForMobile = (mapContainer: HTMLElement): void => {
  if (!mapContainer || typeof window === 'undefined') return;

  const deviceInfo = getDeviceInfo();
  
  if (deviceInfo.isMobile) {
    // Prevent page scroll when interacting with map
    mapContainer.style.touchAction = 'none';
    
    // Add mobile-specific event listeners
    let isMapInteracting = false;
    
    mapContainer.addEventListener('touchstart', () => {
      isMapInteracting = true;
      document.body.style.overflow = 'hidden';
    }, { passive: true });
    
    mapContainer.addEventListener('touchend', () => {
      isMapInteracting = false;
      document.body.style.overflow = '';
    }, { passive: true });
    
    // Handle map container sizing
    const resizeMap = () => {
      const containerHeight = Math.min(window.innerHeight * 0.4, 400);
      mapContainer.style.height = `${containerHeight}px`;
    };
    
    resizeMap();
    window.addEventListener('resize', resizeMap);
    window.addEventListener('orientationchange', () => {
      setTimeout(resizeMap, 100);
    });
  }
};

// Optimize form interactions for mobile
export const optimizeFormForMobile = (form: HTMLFormElement): void => {
  if (!form || typeof window === 'undefined') return;

  const deviceInfo = getDeviceInfo();
  if (!deviceInfo.isMobile) return;

  // Optimize all inputs in the form
  const inputs = form.querySelectorAll('input, select, textarea, button');
  inputs.forEach((input) => {
    const element = input as HTMLElement;
    optimizeTouchInteraction(element);
    
    // Add mobile-specific attributes
    if (element.tagName === 'INPUT') {
      const inputElement = element as HTMLInputElement;
      
      // Prevent autocorrect and autocapitalize for certain input types
      if (inputElement.type === 'email' || inputElement.type === 'url') {
        inputElement.setAttribute('autocorrect', 'off');
        inputElement.setAttribute('autocapitalize', 'none');
        inputElement.setAttribute('spellcheck', 'false');
      }
      
      // Set appropriate input modes
      if (inputElement.type === 'tel') {
        inputElement.setAttribute('inputmode', 'tel');
      } else if (inputElement.type === 'email') {
        inputElement.setAttribute('inputmode', 'email');
      } else if (inputElement.type === 'number') {
        inputElement.setAttribute('inputmode', 'numeric');
      }
    }
  });
};

// Handle keyboard visibility on mobile
export const handleMobileKeyboard = (): void => {
  if (typeof window === 'undefined') return;

  const deviceInfo = getDeviceInfo();
  if (!deviceInfo.isMobile) return;

  let initialViewportHeight = window.innerHeight;
  
  const handleResize = () => {
    const currentHeight = window.innerHeight;
    const heightDifference = initialViewportHeight - currentHeight;
    
    // If height decreased significantly, keyboard is likely open
    if (heightDifference > 150) {
      document.body.classList.add('keyboard-open');
      
      // Scroll active input into view
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        setTimeout(() => {
          activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
      }
    } else {
      document.body.classList.remove('keyboard-open');
    }
  };

  window.addEventListener('resize', handleResize);
  
  // Reset on orientation change
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      initialViewportHeight = window.innerHeight;
    }, 500);
  });
};

// Optimize scrolling performance
export const optimizeScrolling = (): void => {
  if (typeof window === 'undefined') return;

  // Enable smooth scrolling
  document.documentElement.style.scrollBehavior = 'smooth';
  
  // Enable smooth scrolling on iOS
  (document.documentElement.style as any)['webkitOverflowScrolling'] = 'touch';
  
  // Add momentum scrolling for iOS
  (document.body.style as any)['webkitOverflowScrolling'] = 'touch';
  
  // Optimize scroll containers
  const scrollContainers = document.querySelectorAll('.overflow-auto, .overflow-y-auto, .overflow-x-auto');
  scrollContainers.forEach((container) => {
    const element = container as HTMLElement;
    (element.style as any)['webkitOverflowScrolling'] = 'touch';
  });
};

// Initialize all mobile optimizations
export const initializeMobileOptimizations = (): void => {
  if (typeof window === 'undefined') return;

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      initializeMobileOptimizations();
    });
    return;
  }

  const deviceInfo = getDeviceInfo();
  
  // Add device classes to body
  document.body.classList.add(
    deviceInfo.isMobile ? 'is-mobile' : 'is-desktop',
    deviceInfo.isTablet ? 'is-tablet' : '',
    deviceInfo.isIOS ? 'is-ios' : '',
    deviceInfo.isAndroid ? 'is-android' : '',
    deviceInfo.touchSupport ? 'has-touch' : 'no-touch'
  );

  // Apply optimizations
  handleMobileViewport();
  preventIOSZoom();
  handleMobileKeyboard();
  optimizeScrolling();

  // Optimize existing forms
  const forms = document.querySelectorAll('form');
  forms.forEach(optimizeFormForMobile);

  // Optimize existing maps
  const mapContainers = document.querySelectorAll('.map-container, [id*="map"]');
  mapContainers.forEach((container) => optimizeMapForMobile(container as HTMLElement));

  console.log('Mobile optimizations initialized for:', deviceInfo);
};

// Utility to check if device needs mobile optimizations
export const needsMobileOptimization = (): boolean => {
  const deviceInfo = getDeviceInfo();
  return deviceInfo.isMobile || deviceInfo.isTablet;
};

// Export device info for use in components
export { getDeviceInfo as default };
