/* Universal Device Optimization CSS for BaroRide */
/* Works consistently across mobile, tablet, and desktop devices */

/* ===== UNIVERSAL BASE STYLES ===== */

/* Reset and normalize for all devices */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px; /* Base font size for all devices */
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== UNIVERSAL VIEWPORT HANDLING ===== */

:root {
  --vh: 1vh;
  --safe-area-inset-top: 0px;
  --safe-area-inset-right: 0px;
  --safe-area-inset-bottom: 0px;
  --safe-area-inset-left: 0px;
}

/* Support for devices with safe areas (notches, etc.) */
@supports (padding: max(0px)) {
  :root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
  }
}

/* ===== UNIVERSAL INPUT STYLES ===== */

/* Consistent input styling across all devices */
input, select, textarea, button {
  font-family: inherit;
  font-size: 16px; /* Prevents zoom on iOS */
  line-height: 1.5;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  padding: 12px 16px;
  transition: all 0.2s ease;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: white;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button styling for all devices */
button {
  cursor: pointer;
  background-color: #3b82f6;
  color: white;
  border: none;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* Minimum touch target */
  min-width: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

button:hover {
  background-color: #2563eb;
}

button:active {
  transform: translateY(1px);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== UNIVERSAL RESPONSIVE GRID ===== */

.universal-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

/* Mobile-first grid (1 column) */
.universal-grid {
  grid-template-columns: 1fr;
}

/* Tablet grid (2 columns) */
@media (min-width: 640px) {
  .universal-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop grid (3+ columns) */
@media (min-width: 1024px) {
  .universal-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .universal-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ===== UNIVERSAL CONTAINER ===== */

.universal-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 640px) {
  .universal-container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .universal-container {
    padding: 0 32px;
  }
}

/* ===== UNIVERSAL CARD COMPONENT ===== */

.universal-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.universal-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* ===== UNIVERSAL FORM STYLES ===== */

.universal-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.universal-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.universal-form-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.universal-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.universal-form-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== UNIVERSAL NAVIGATION ===== */

.universal-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 50;
}

.universal-nav-brand {
  font-size: 20px;
  font-weight: 700;
  color: #1e3a5f;
  text-decoration: none;
}

.universal-nav-menu {
  display: flex;
  align-items: center;
  gap: 16px;
  list-style: none;
}

.universal-nav-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.universal-nav-link:hover {
  color: #3b82f6;
  background-color: #f3f4f6;
}

/* ===== UNIVERSAL MAP STYLES ===== */

.universal-map-container {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  touch-action: none; /* Prevent page scroll when interacting */
}

/* Responsive map heights */
@media (min-width: 640px) {
  .universal-map-container {
    height: 400px;
  }
}

@media (min-width: 1024px) {
  .universal-map-container {
    height: 500px;
  }
}

/* ===== UNIVERSAL MODAL ===== */

.universal-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: rgba(0, 0, 0, 0.5);
}

.universal-modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  -webkit-overflow-scrolling: touch;
}

/* ===== UNIVERSAL UTILITIES ===== */

.universal-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.universal-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: universal-spin 1s linear infinite;
}

@keyframes universal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== DEVICE-SPECIFIC OPTIMIZATIONS ===== */

/* Touch device optimizations */
.has-touch button,
.has-touch input,
.has-touch select,
.has-touch textarea,
.has-touch .universal-nav-link {
  min-height: 44px;
  min-width: 44px;
}

/* Mouse device optimizations */
.no-touch button:hover,
.no-touch .universal-nav-link:hover {
  transform: translateY(-1px);
}

/* Mobile device optimizations */
.is-mobile .universal-container {
  padding: 0 12px;
}

.is-mobile .universal-card {
  border-radius: 8px;
  padding: 16px;
}

.is-mobile .universal-nav {
  padding: 8px 12px;
}

/* Tablet device optimizations */
.is-tablet .universal-container {
  padding: 0 20px;
}

/* Desktop device optimizations */
.is-desktop .universal-container {
  padding: 0 24px;
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  button, input, select, textarea {
    border-width: 3px;
  }
}

/* Focus visible for keyboard navigation */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
.universal-nav-link:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
