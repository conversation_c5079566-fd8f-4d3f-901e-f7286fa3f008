import { createContext, useContext, useState, ReactNode } from 'react';
import Notification from '@/components/Notification';

type NotificationType = 'success' | 'info' | 'warning' | 'error';

interface DriverDetails {
  fullName: string;
  phoneNumber: string;
  licensePlate: string;
  vehicleColor: string;
  vehicleMake: string;
  vehicleModel: string;
}

interface NotificationItem {
  id: string;
  message: string;
  type: NotificationType;
  duration?: number;
  driverDetails?: DriverDetails;
}

interface NotificationContextType {
  showNotification: (
    message: string,
    type?: NotificationType,
    duration?: number,
    driverDetails?: DriverDetails
  ) => void;
}

const NotificationContext = createContext<NotificationContextType>({
  showNotification: () => {},
});

export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const showNotification = (
    message: string,
    type: NotificationType = 'info',
    duration: number = 5000,
    driverDetails?: DriverDetails
  ) => {
    const id = Date.now().toString();
    setNotifications((prev) => [...prev, { id, message, type, duration, driverDetails }]);
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  };

  return (
    <NotificationContext.Provider value={{ showNotification }}>
      {children}
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          message={notification.message}
          type={notification.type}
          duration={notification.duration}
          driverDetails={notification.driverDetails}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </NotificationContext.Provider>
  );
}

export const useNotification = () => useContext(NotificationContext);

