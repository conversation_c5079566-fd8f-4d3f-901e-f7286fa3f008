import { NextPage } from 'next';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/Layout';
import { useNotification } from '@/contexts/NotificationContext';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

const Home: NextPage = () => {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  // Show welcome notification when the page loads
  useEffect(() => {
    if (user) {
      showNotification(`Welcome back, ${user.fullName}!`, 'info', 3000); // Auto-dismiss after 3 seconds
    }
  }, [user, showNotification]);

  // We'll let the AuthContext handle the automatic redirection

  // Function to navigate to the driver dashboard
  const goToDriverDashboard = () => {
    setIsNavigating(true);
    showNotification('Opening Driver Dashboard...', 'info', 2000);

    // Use both methods to ensure navigation works
    window.location.href = '/driver/dashboard'; // Direct browser navigation as fallback
    router.push('/driver/dashboard');
  };

  // Function to navigate to the book a ride page
  const goToBookRide = () => {
    setIsNavigating(true);
    showNotification('Opening Book a Ride...', 'info', 2000);

    // Use both methods to ensure navigation works
    window.location.href = '/book'; // Direct browser navigation as fallback
    router.push('/book');
  };

  return (
    <Layout title="BaroRide - Book Your Ride">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section with Logo */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <img
                src="/logo.png"
                alt="BaroRide Logo"
                className="h-32 w-auto sm:h-40 md:h-48"
              />
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4">
              Welcome to BaroRide
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 mb-8">
              Your reliable ride-sharing service in Gambela, Ethiopia
            </p>
          </div>

          {/* Quick access buttons for users based on role */}
          {user && (
            <div className="text-center mb-6">
              {user.role === 'driver' ? (
                <button
                  onClick={goToDriverDashboard}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation"
                  style={{ touchAction: 'manipulation' }}
                >
                  Open Driver Dashboard
                </button>
              ) : (
                <button
                  onClick={goToBookRide}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation"
                  style={{ touchAction: 'manipulation' }}
                >
                  Book a Ride Now
                </button>
              )}
            </div>
          )}

          <div className="bg-white shadow-md rounded-lg p-8 mb-8">
            <p className="text-lg text-gray-700 mb-6 text-center">
              Book your ride with fixed fares and professional drivers.
            </p>

            {user ? (
              <div className="text-center">
                {user.role === 'rider' ? (
                  <button
                    className={`bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ${isNavigating ? 'opacity-75 cursor-wait' : ''}`}
                    onClick={goToBookRide}
                    disabled={isNavigating}
                    style={{ touchAction: 'manipulation' }}
                  >
                    {isNavigating ? 'Opening Booking...' : 'Book a Ride'}
                  </button>
                ) : (
                  <button
                    className={`bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation ${isNavigating ? 'opacity-75 cursor-wait' : ''}`}
                    onClick={goToDriverDashboard}
                    disabled={isNavigating}
                    style={{ touchAction: 'manipulation' }}
                  >
                    {isNavigating ? 'Opening Dashboard...' : 'Go to Driver Dashboard'}
                  </button>
                )}
              </div>
            ) : (
              <div className="text-center space-x-4">
                <Link href="/login" className="bg-gray-200 text-gray-800 px-6 py-3 rounded-md hover:bg-gray-300 inline-block">
                  Login
                </Link>
                <Link href="/signup" className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 inline-block">
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-3">Fixed Fares</h2>
              <p className="text-gray-700">Know exactly what you'll pay before booking your ride.</p>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-3">Professional Drivers</h2>
              <p className="text-gray-700">All our drivers are vetted and professionally trained.</p>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-3">Easy Booking</h2>
              <p className="text-gray-700">Book your ride in just a few clicks, anytime, anywhere.</p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Home;