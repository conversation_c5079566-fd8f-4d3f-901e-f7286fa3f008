import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/firebase/config';
import { collection, addDoc, query, where, getDocs, orderBy, limit, doc, getDoc, updateDoc } from 'firebase/firestore';
import type { Booking } from '@/types/booking';
import BasicMap from '@/components/BasicMap';
import Layout from '@/components/Layout';
import { useNotification } from '@/contexts/NotificationContext';
import { safeAddDoc, safeUpdateDoc, safeQueryDocs, isFirebaseHosting } from '@/utils/firebase-helpers';
import ProtectedRoute from '@/components/ProtectedRoute';
import PaymentProcessor, { PaymentResult } from '@/components/PaymentProcessor';

const AIRPORTS = [
  { code: 'GMB', name: 'Gambela International Airport' },
  // Add more airports as needed
];

function BookRide() {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const [pickupLocation, setPickupLocation] = useState({
    address: '',
    lat: 0,
    lng: 0,
  });
  const [selectedAirport, setSelectedAirport] = useState(AIRPORTS[0]);
  const [passengers, setPassengers] = useState(1);
  const [isSelectingOnMap, setIsSelectingOnMap] = useState(false);
  const [estimatedFare, setEstimatedFare] = useState(0);
  const [previousBookings, setPreviousBookings] = useState<Booking[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);
  const [showPreviousBookings, setShowPreviousBookings] = useState(false);
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const [isLoadingGps, setIsLoadingGps] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'completed' | 'failed'>('pending');
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);

  // Fetch previous bookings when component mounts
  useEffect(() => {
    // Fetch previous bookings if user is logged in
    if (user) {
      fetchPreviousBookings();
    }
  }, [user]);

  // Fetch user's previous bookings
  const fetchPreviousBookings = async () => {
    if (!user) return;

    setIsLoadingBookings(true);
    try {
      // Log for debugging in production
      if (isFirebaseHosting()) {
        console.log('Fetching previous bookings in Firebase hosting environment');
      }

      // Use our safe query function if we're in production, otherwise use regular query
      let bookings: Booking[] = [];

      if (isFirebaseHosting()) {
        // Use the safe query function with retry logic
        const constraints = [
          where('riderId', '==', user.id),
          where('status', '==', 'completed'),
          orderBy('updatedAt', 'desc'),
          limit(5)
        ];

        const results = await safeQueryDocs('bookings', constraints);

        bookings = results.map(data => ({
          id: data.id,
          ...data,
          scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),
          createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),
          updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),
          passengers: data.passengers || 1, // Default to 1 passenger for older bookings
        } as Booking));
      } else {
        // Regular query for development environment
        const bookingsQuery = query(
          collection(db, 'bookings'),
          where('riderId', '==', user.id),
          where('status', '==', 'completed'),
          orderBy('updatedAt', 'desc'),
          limit(5) // Limit to 5 most recent bookings
        );

        const querySnapshot = await getDocs(bookingsQuery);

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          // Convert Firestore timestamps to Date objects
          const booking = {
            id: doc.id,
            ...data,
            scheduledTime: data.scheduledTime ? new Date(data.scheduledTime) : new Date(),
            createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),
            updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),
            passengers: data.passengers || 1, // Default to 1 passenger for older bookings
          } as Booking;
          bookings.push(booking);
        });
      }

      setPreviousBookings(bookings);
      console.log(`Fetched ${bookings.length} previous bookings`);
    } catch (error) {
      console.error('Error fetching previous bookings:', error);
      showNotification('Could not load your previous bookings. Please try again later.', 'warning');
    } finally {
      setIsLoadingBookings(false);
    }
  };

  // Clear selected booking and pickup location
  const clearSelection = () => {
    setPickupLocation({
      address: '',
      lat: 0,
      lng: 0,
    });
    setSelectedBookingId(null);
  };

  // Toggle between map selection and manual entry
  const toggleMapSelection = () => {
    setIsSelectingOnMap(!isSelectingOnMap);
  };

  // Handle location selection from map
  const handleLocationSelected = (location: { lat: number; lng: number; address?: string }) => {
    // Validate the location
    if (!location || typeof location.lat !== 'number' || typeof location.lng !== 'number') {
      showNotification('Invalid location selected. Please try again.', 'error');
      return;
    }

    // Create a valid location object
    const newLocation = {
      lat: location.lat,
      lng: location.lng,
      address: location.address || 'Selected location'
    };

    console.log('Location selected from map:', newLocation);

    // Update state
    setPickupLocation(newLocation);

    // Clear selected booking since we're selecting a new location
    setSelectedBookingId(null);
    setIsSelectingOnMap(false);

    // Calculate fare for the new location
    const fare = calculateFare(newLocation);
    setEstimatedFare(fare);

    // Show confirmation to the user
    showNotification('Pickup location selected successfully!', 'success');
  };

  // Toggle showing previous bookings
  const togglePreviousBookings = () => {
    setShowPreviousBookings(!showPreviousBookings);
  };

  // Get user's current location using GPS - manual action
  const getUserLocation = async () => {
    if (!navigator.geolocation) {
      showNotification('Geolocation is not supported by your browser. Please enter your location manually.', 'error');
      return;
    }

    setIsLoadingGps(true);
    showNotification('Getting your current location...', 'info');

    try {
      // Get current position with better timeout and error handling
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            console.log('GPS position obtained:', position.coords);
            resolve(position);
          },
          (error) => {
            console.error('GPS error:', error.code, error.message);
            let errorMessage = 'Unable to retrieve your location.';

            // Provide more specific error messages
            if (error.code === 1) { // PERMISSION_DENIED
              errorMessage = 'Location access denied. Please enable location services in your browser settings.';
            } else if (error.code === 2) { // POSITION_UNAVAILABLE
              errorMessage = 'Your current position is unavailable. Please try again later.';
            } else if (error.code === 3) { // TIMEOUT
              errorMessage = 'Location request timed out. Please try again.';
            }

            reject(new Error(errorMessage));
          },
          {
            enableHighAccuracy: true,
            timeout: 10000, // Increased timeout for better reliability
            maximumAge: 0
          }
        );
      });

      const { latitude, longitude } = position.coords;
      console.log(`GPS coordinates: ${latitude}, ${longitude}`);

      try {
        // Get address from coordinates using Mapbox Geocoding API
        const response = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A`
        );

        if (!response.ok) {
          throw new Error(`Geocoding API error: ${response.status}`);
        }

        const data = await response.json();
        console.log('Geocoding response:', data);

        let address = 'Your current location';
        if (data.features && data.features.length > 0) {
          address = data.features[0].place_name;
          console.log('Address found:', address);
        }

        // Create location object
        const location = {
          lat: latitude,
          lng: longitude,
          address
        };

        // Update pickup location
        setPickupLocation(location);

        // Clear selected booking
        setSelectedBookingId(null);

        // Calculate fare for the new location
        const fare = calculateFare(location);
        setEstimatedFare(fare);

        // Show success notification
        showNotification('Your current location has been set as the pickup point.', 'success');
      } catch (geocodingError) {
        console.error('Error getting address:', geocodingError);

        // Still set the location even if geocoding fails
        const location = {
          lat: latitude,
          lng: longitude,
          address: 'Your current location'
        };

        setPickupLocation(location);
        setSelectedBookingId(null);

        const fare = calculateFare(location);
        setEstimatedFare(fare);

        showNotification('Location set, but we couldn\'t get your exact address. You can edit it manually.', 'warning');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      showNotification(error instanceof Error ? error.message : 'Unable to retrieve your location. Please try again or select manually.', 'error');
    } finally {
      setIsLoadingGps(false);
    }
  };

  // Select a previous booking
  const selectPreviousBooking = (booking: Booking) => {
    // Update pickup location
    setPickupLocation(booking.pickupLocation);

    // Update airport if it exists in our list
    const airport = AIRPORTS.find(a => a.code === booking.airport.code);
    if (airport) {
      setSelectedAirport(airport);
    }

    // Set the selected booking ID
    setSelectedBookingId(booking.id);

    // Hide the previous bookings dropdown
    setShowPreviousBookings(false);
  };

  // Calculate fare based on distance and number of passengers
  const calculateFare = (location = pickupLocation) => {
    // In a real app, you would calculate based on distance between pickup and airport
    // For this example, we'll use a base fare plus a random amount based on coordinates
    if (location.lat === 0 || location.lng === 0) {
      return 0;
    }

    const baseFare = 35;
    // Use the coordinates to create a somewhat realistic variable fare
    // This is just for demonstration - in a real app you'd use actual distance calculation
    const seed = (location.lat * location.lng) % 100;
    const distanceFare = Math.floor(10 + seed / 5);

    // Add $5 per additional passenger
    const passengerFare = (passengers - 1) * 5;

    return baseFare + distanceFare + passengerFare;
  };

  // Update estimated fare when pickup location or passengers count changes
  useEffect(() => {
    if (pickupLocation.lat !== 0 && pickupLocation.lng !== 0) {
      const fare = calculateFare(pickupLocation);
      setEstimatedFare(fare);
    }
  }, [pickupLocation, passengers]);

  // Handle payment completion
  const handlePaymentComplete = async (result: PaymentResult) => {
    setPaymentResult(result);
    setPaymentStatus(result.status === 'completed' ? 'completed' : 'failed');

    if (result.success) {
      // If payment was successful, create the booking
      await createBooking(result.transactionId);
    } else {
      // If payment failed, show error and close payment modal
      showNotification(`Payment failed: ${result.message}. Please try again.`, 'error');
      setShowPaymentModal(false);
    }
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    showNotification('Payment cancelled. Your booking was not created.', 'info');
    setShowPaymentModal(false);
  };

  // Create booking after successful payment
  const createBooking = async (transactionId?: string) => {
    if (!user) {
      showNotification('Please log in to book a ride.', 'error');
      return;
    }

    try {
      // Show loading notification
      showNotification('Creating your booking...', 'info');

      // Create timestamp objects for Firestore
      const now = new Date();

      // Create the booking object with all required fields
      const booking: Partial<Booking> = {
        riderId: user.id,
        pickupLocation: {
          address: pickupLocation.address,
          lat: pickupLocation.lat,
          lng: pickupLocation.lng
        },
        airport: {
          name: selectedAirport.name,
          code: selectedAirport.code
        },
        status: 'pending',
        fare: estimatedFare > 0 ? estimatedFare : calculateFare(),
        passengers: passengers,
        createdAt: now,
        updatedAt: now,
        paymentStatus: 'completed',
        paymentTransactionId: transactionId
      };

      console.log('Creating booking with data:', booking);

      // Add the booking to Firestore with our safe function if in production
      let bookingId: string;

      if (isFirebaseHosting()) {
        // Use safe function with built-in retry logic
        console.log('Using safe function to create booking in production');
        bookingId = await safeAddDoc('bookings', booking);
        console.log('Booking created with ID:', bookingId);
      } else {
        // Use standard function in development
        try {
          const bookingRef = await addDoc(collection(db, 'bookings'), booking);
          bookingId = bookingRef.id;
          console.log('Booking created with ID:', bookingId);
        } catch (error) {
          console.error('Error creating booking:', error);
          showNotification('Retrying booking creation...', 'info');

          // Second attempt after a short delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          const bookingRef = await addDoc(collection(db, 'bookings'), booking);
          bookingId = bookingRef.id;
          console.log('Booking created on second attempt with ID:', bookingId);
        }
      }

      // Update the user's booking history
      try {
        if (isFirebaseHosting()) {
          // Use safe function in production
          const userDoc = await getDoc(doc(db, 'users', user.id));

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const bookingHistory = userData.bookingHistory || [];

            // Only add the booking ID if it's not already in the history
            if (!bookingHistory.includes(bookingId)) {
              await safeUpdateDoc('users', user.id, {
                bookingHistory: [...bookingHistory, bookingId]
              });
              console.log('Updated user booking history');
            }
          }
        } else {
          // Use standard function in development
          const userRef = doc(db, 'users', user.id);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const bookingHistory = userData.bookingHistory || [];

            // Only add the booking ID if it's not already in the history
            if (!bookingHistory.includes(bookingId)) {
              await updateDoc(userRef, {
                bookingHistory: [...bookingHistory, bookingId]
              });
              console.log('Updated user booking history');
            }
          }
        }
      } catch (historyError) {
        // Non-critical error, log but continue
        console.error('Error updating booking history:', historyError);
      }

      // Create a notification for the user
      try {
        const notificationData = {
          userId: user.id,
          message: 'Your ride has been booked and paid successfully. Waiting for a driver to accept.',
          type: 'info',
          read: false,
          relatedBookingId: bookingId,
          createdAt: now
        };

        if (isFirebaseHosting()) {
          // Use safe function in production
          await safeAddDoc('notifications', notificationData);
        } else {
          // Use standard function in development
          await addDoc(collection(db, 'notifications'), notificationData);
        }
        console.log('Created notification for user');
      } catch (notificationError) {
        // Non-critical error, log but continue
        console.error('Error creating notification:', notificationError);
      }

      // Show success notification
      showNotification('Booking created and payment processed successfully! Waiting for a driver to accept.', 'success');

      // Close payment modal
      setShowPaymentModal(false);

      // Reset form after successful booking
      setPickupLocation({
        address: '',
        lat: 0,
        lng: 0,
      });
      setPassengers(1);
      setEstimatedFare(0);
      setSelectedBookingId(null);

    } catch (error) {
      console.error('Error creating booking:', error);

      // Provide more detailed error messages
      let errorMessage = 'Failed to create booking. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else if (error.message.includes('permission-denied')) {
          errorMessage = 'Permission denied. Please log out and log back in.';
        } else if (error.message.includes('not-found')) {
          errorMessage = 'Database connection error. Please refresh the page and try again.';
        }
      }

      showNotification(errorMessage, 'error');
      setShowPaymentModal(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      showNotification('Please log in to book a ride.', 'error');
      return;
    }

    // Validate pickup location
    if (!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) {
      showNotification('Please select a valid pickup location.', 'error');
      return;
    }

    // Show payment modal
    setShowPaymentModal(true);
  };

  return (
    <Layout title="BaroRide - Book a Ride">

      <div className="container mx-auto p-2 sm:p-4 max-w-4xl">
        <div className="text-center mb-6">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Book Your Ride</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Quick and easy airport transportation</p>
        </div>

        {/* Payment Modal */}
        {showPaymentModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
              <PaymentProcessor
                amount={estimatedFare}
                onPaymentComplete={handlePaymentComplete}
                onCancel={handlePaymentCancel}
                description={`Ride to ${selectedAirport.name} with ${passengers} passenger${passengers > 1 ? 's' : ''}`}
              />
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-2 sm:mb-0">Pickup Location</label>
              <div className="flex flex-wrap gap-2">
                <button
                  type="button"
                  onClick={toggleMapSelection}
                  className="text-xs sm:text-sm text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-colors touch-manipulation"
                  style={{ touchAction: 'manipulation' }}
                >
                  {isSelectingOnMap ? '📝 Manual Entry' : '🗺️ Select on Map'}
                </button>
                <button
                  type="button"
                  onClick={getUserLocation}
                  className="text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center"
                  style={{ touchAction: 'manipulation' }}
                  disabled={isLoadingGps}
                >
                  {isLoadingGps ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Getting...
                    </>
                  ) : (
                    <>
                      📍 My Location
                    </>
                  )}
                </button>
                {previousBookings.length > 0 && (
                  <button
                    type="button"
                    onClick={togglePreviousBookings}
                    className="text-sm text-green-500 hover:text-green-700"
                  >
                    {showPreviousBookings ? 'Hide Previous' : 'Use Previous'}
                  </button>
                )}
              </div>
            </div>

            {!isSelectingOnMap ? (
              <div>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Enter your pickup address manually"
                    className="w-full p-2 border rounded"
                    value={pickupLocation.address}
                    onChange={(e) => {
                      setPickupLocation(prev => ({ ...prev, address: e.target.value }));
                      // Clear selected booking when manually editing
                      if (selectedBookingId) {
                        setSelectedBookingId(null);
                      }
                    }}
                  />
                  {pickupLocation.address && (
                    <button
                      type="button"
                      onClick={clearSelection}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Enter your address manually, click "Use My Location", or select it on the map
                </p>
              </div>
            ) : (
              <div className="text-sm text-gray-600 mb-2">
                Click on the map to manually select your pickup location
              </div>
            )}

            {/* Previous bookings dropdown */}
            {showPreviousBookings && (
              <div className="mt-2 border rounded shadow-sm overflow-hidden">
                <div className="bg-gray-50 px-3 py-2 border-b">
                  <h3 className="text-sm font-medium text-gray-700">Previous Bookings</h3>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  {isLoadingBookings ? (
                    <div className="p-4 text-center">
                      <div className="inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
                      <span className="text-sm text-gray-600">Loading...</span>
                    </div>
                  ) : previousBookings.length === 0 ? (
                    <div className="p-4 text-center text-sm text-gray-600">
                      No previous bookings found
                    </div>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {previousBookings.map((booking) => (
                        <li
                          key={booking.id}
                          className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                            selectedBookingId === booking.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                          }`}
                          onClick={() => selectPreviousBooking(booking)}
                        >
                          <div className="flex justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-700">{booking.pickupLocation.address}</p>
                              <p className="text-xs text-gray-500">To: {booking.airport.name}</p>
                            </div>
                            <div className="flex items-center">
                              <div className="text-xs text-gray-500 mr-2">
                                {new Date(booking.updatedAt).toLocaleDateString()}
                              </div>
                              {selectedBookingId === booking.id && (
                                <span className="text-xs text-blue-500 font-medium">Selected</span>
                              )}
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="mb-4">
            <h3 className="text-lg font-medium mb-2">Pickup Location Map</h3>
            <BasicMap
              height="300px"
              selectable={isSelectingOnMap}
              onLocationSelected={handleLocationSelected}
              initialLocation={pickupLocation.lat !== 0 ? pickupLocation : undefined}
            />
            {pickupLocation.lat !== 0 && pickupLocation.lng !== 0 && (
              <div className="mt-2 p-2 bg-blue-50 border border-blue-100 rounded">
                <div className="flex justify-between items-center">
                  <div className="flex-1">
                    <p className="text-sm font-medium">Selected Pickup: {pickupLocation.address}</p>
                    {selectedBookingId && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        From Previous Booking
                      </span>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={clearSelection}
                    className="ml-2 text-xs text-red-500 hover:text-red-700"
                  >
                    Clear
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Select Airport</label>
              <select
                value={selectedAirport.code}
                onChange={(e) => {
                  const airport = AIRPORTS.find(a => a.code === e.target.value);
                  if (airport) setSelectedAirport(airport);
                }}
                className="w-full p-2 border rounded"
              >
                {AIRPORTS.map(airport => (
                  <option key={airport.code} value={airport.code}>
                    {airport.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Number of Passengers</label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => setPassengers(prev => Math.max(1, prev - 1))}
                  className="p-2 bg-gray-100 border rounded-l hover:bg-gray-200"
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"></path>
                  </svg>
                </button>
                <input
                  type="number"
                  min="1"
                  max="8"
                  value={passengers}
                  onChange={(e) => setPassengers(Math.max(1, Math.min(8, parseInt(e.target.value) || 1)))}
                  className="w-full p-2 border-t border-b text-center"
                />
                <button
                  type="button"
                  onClick={() => setPassengers(prev => Math.min(8, prev + 1))}
                  className="p-2 bg-gray-100 border rounded-r hover:bg-gray-200"
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v12M6 12h12"></path>
                  </svg>
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-1">Maximum 8 passengers per ride</p>
            </div>
          </div>

          {/* Fare estimate */}
          {estimatedFare > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Fare Estimate</h3>
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-blue-700">Base fare</p>
                  <p className="text-sm text-blue-700">Distance</p>
                  {passengers > 1 && (
                    <p className="text-sm text-blue-700">Additional passengers ({passengers - 1})</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="text-sm text-blue-700">$35.00</p>
                  <p className="text-sm text-blue-700">${Math.floor(10 + ((pickupLocation.lat * pickupLocation.lng) % 100) / 5).toFixed(2)}</p>
                  {passengers > 1 && (
                    <p className="text-sm text-blue-700">${((passengers - 1) * 5).toFixed(2)}</p>
                  )}
                </div>
              </div>
              <div className="border-t border-blue-200 mt-2 pt-2 flex justify-between items-center">
                <p className="font-medium text-blue-800">Total estimated fare</p>
                <p className="font-medium text-blue-800">${estimatedFare.toFixed(2)}</p>
              </div>
              <p className="text-xs text-blue-600 mt-2">
                * Actual fare may vary based on traffic, weather, and other factors.
              </p>
            </div>
          )}

          <div className="pt-4">
            <button
              type="submit"
              className={`w-full ${
                !pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600'
              } text-white p-3 rounded font-medium transition-colors`}
              disabled={!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0}
            >
              {!user ? 'Please Log In to Book a Ride' : 'Proceed to Payment'}
            </button>

            {(!pickupLocation.address || pickupLocation.lat === 0 || pickupLocation.lng === 0) && (
              <div className="flex items-center mt-2">
                <svg className="w-4 h-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <p className="text-red-500 text-sm">Please enter or select a pickup location</p>
              </div>
            )}

            {!user && (
              <div className="flex items-center mt-2">
                <svg className="w-4 h-4 text-yellow-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p className="text-yellow-500 text-sm">You need to be logged in to book a ride</p>
              </div>
            )}
          </div>
        </form>
      </div>
    </Layout>
  );
}

// Wrap the component with ProtectedRoute
export default function BookRidePage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'rider']}>
      <BookRide />
    </ProtectedRoute>
  );
}