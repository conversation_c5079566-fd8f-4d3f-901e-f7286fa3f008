import { useEffect, useRef, useState } from 'react';
import { getDeviceInfo, optimizeMapForMobile } from '@/utils/mobile-optimization';

interface Location {
  lat: number;
  lng: number;
  address?: string;
}

interface BasicMapProps {
  height?: string;
  selectable?: boolean;
  onLocationSelected?: (location: Location) => void;
  initialLocation?: Location;
}

export default function BasicMap({
  height = '300px',
  selectable = false,
  onLocationSelected,
  initialLocation
}: BasicMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const mapRef = useRef<any>(null);
  const [deviceInfo] = useState(() => getDeviceInfo());

  // Method to update the map with a new location
  const updateMapLocation = (location: Location) => {
    if (!mapRef.current || !location || location.lat === 0 || location.lng === 0) return;

    // Remove existing markers
    const markers = document.querySelectorAll('.mapboxgl-marker');
    markers.forEach(marker => marker.remove());

    // Add a new marker
    new window.mapboxgl.Marker({ color: '#3b82f6' })
      .setLngLat([location.lng, location.lat])
      .addTo(mapRef.current);

    // Fly to the new location
    mapRef.current.flyTo({
      center: [location.lng, location.lat],
      zoom: 14,
      essential: true
    });

    // Add popup with address if available
    if (location.address) {
      new window.mapboxgl.Popup({ offset: 25, closeButton: false })
        .setLngLat([location.lng, location.lat])
        .setHTML(`<p style="margin: 0;">${location.address}</p>`)
        .addTo(mapRef.current);
    }
  };

  useEffect(() => {
    // Load Mapbox script dynamically
    const loadMapbox = () => {
      // Check if Mapbox is already loaded
      if (window.mapboxgl) {
        initializeMap();
        return;
      }

      // Create script element
      const script = document.createElement('script');
      script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js';
      script.async = true;
      script.onload = () => {
        // Initialize map after script loads
        initializeMap();
      };
      document.head.appendChild(script);

      // Add CSS
      const link = document.createElement('link');
      link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css';
      link.rel = 'stylesheet';
      document.head.appendChild(link);
    };

    // Initialize the map
    const initializeMap = () => {
      if (!mapContainerRef.current || !window.mapboxgl) return;

      // Set access token
      window.mapboxgl.accessToken = 'pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A';

      // Determine center coordinates
      let centerLng = 34.5925; // Default to Gambela, Ethiopia
      let centerLat = 8.2483;

      // Use initialLocation if provided
      if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {
        centerLng = initialLocation.lng;
        centerLat = initialLocation.lat;
      }

      // No automatic geolocation - we're making location selection fully manual
      console.log('Using manual location selection only - automatic geolocation disabled');

      // Create map with mobile-optimized settings
      const map = new window.mapboxgl.Map({
        container: mapContainerRef.current,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [centerLng, centerLat],
        zoom: deviceInfo.isMobile ? 12 : 13, // Slightly zoomed out on mobile for better context
        touchZoomRotate: deviceInfo.isMobile, // Enable touch zoom/rotate on mobile
        touchPitch: deviceInfo.isMobile, // Enable touch pitch on mobile
        dragRotate: !deviceInfo.isMobile, // Disable drag rotate on mobile to prevent confusion
        pitchWithRotate: !deviceInfo.isMobile, // Disable pitch with rotate on mobile
        doubleClickZoom: true,
        scrollZoom: true,
        boxZoom: !deviceInfo.isMobile, // Disable box zoom on mobile
        dragPan: true,
        keyboard: !deviceInfo.isMobile, // Disable keyboard controls on mobile
        cooperativeGestures: deviceInfo.isMobile, // Require two fingers for zoom on mobile
      });

      // Store map reference
      mapRef.current = map;

      // Add navigation controls with mobile-optimized positioning
      const navControl = new window.mapboxgl.NavigationControl({
        showCompass: !deviceInfo.isMobile, // Hide compass on mobile to save space
        showZoom: true,
        visualizePitch: !deviceInfo.isMobile
      });

      map.addControl(navControl, deviceInfo.isMobile ? 'bottom-right' : 'top-right');

      // Apply mobile optimizations to the map container
      if (deviceInfo.isMobile && mapContainerRef.current) {
        optimizeMapForMobile(mapContainerRef.current);
      }

      // Add a marker for the initial location if provided
      if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {
        // Create a marker with a popup showing the address
        new window.mapboxgl.Marker({ color: '#3b82f6' })
          .setLngLat([initialLocation.lng, initialLocation.lat])
          .addTo(map);

        // Add popup with address if available
        if (initialLocation.address) {
          new window.mapboxgl.Popup({ offset: 25, closeButton: false })
            .setLngLat([initialLocation.lng, initialLocation.lat])
            .setHTML(`<p style="margin: 0;">${initialLocation.address}</p>`)
            .addTo(map);
        }
      } else {
        // Add a default marker if no initial location
        new window.mapboxgl.Marker()
          .setLngLat([centerLng, centerLat])
          .addTo(map);
      }

      // Add click handler for location selection if selectable is true
      if (selectable && onLocationSelected) {
        // Add cursor style for selectable mode
        map.getCanvas().style.cursor = 'crosshair';

        // Handle both click and touchend events for better mobile support
        const handleLocationSelect = async (e: { lngLat: { lng: number; lat: number } }) => {
          const { lng, lat } = e.lngLat;

          console.log('Location selected:', { lng, lat });

          // Remove existing markers and popups
          const markers = document.querySelectorAll('.mapboxgl-marker');
          markers.forEach(marker => marker.remove());

          const popups = document.querySelectorAll('.mapboxgl-popup');
          popups.forEach(popup => popup.remove());

          // Create a new marker at the clicked location
          const marker = new window.mapboxgl.Marker({
            color: '#3b82f6',
            scale: 1.2 // Make marker slightly larger for better visibility
          })
            .setLngLat([lng, lat])
            .addTo(map);

          setIsLoading(true);

          try {
            // Get address from coordinates using Mapbox Geocoding API
            const response = await fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${window.mapboxgl.accessToken}`,
              {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                }
              }
            );

            if (!response.ok) {
              throw new Error(`Geocoding failed: ${response.status}`);
            }

            const data = await response.json();
            console.log('Geocoding response:', data);

            let address = 'Selected location';
            if (data.features && data.features.length > 0) {
              address = data.features[0].place_name;
            }

            // Create popup with address and action buttons
            const popup = new window.mapboxgl.Popup({
              offset: 25,
              closeButton: true,
              closeOnClick: false,
              maxWidth: '300px'
            })
              .setLngLat([lng, lat])
              .setHTML(`
                <div style="text-align: center; padding: 8px;">
                  <p style="margin: 0 0 12px 0; font-weight: 500; color: #374151;">${address}</p>
                  <div style="display: flex; gap: 8px; justify-content: center;">
                    <button id="select-location" style="
                      background-color: #3b82f6;
                      color: white;
                      border: none;
                      padding: 8px 16px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 14px;
                      font-weight: 500;
                      min-height: 44px;
                      min-width: 100px;
                      touch-action: manipulation;
                    ">
                      ✓ Select
                    </button>
                    <button id="cancel-location" style="
                      background-color: #6b7280;
                      color: white;
                      border: none;
                      padding: 8px 16px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 14px;
                      font-weight: 500;
                      min-height: 44px;
                      min-width: 80px;
                      touch-action: manipulation;
                    ">
                      Cancel
                    </button>
                  </div>
                </div>
              `)
              .addTo(map);

            // Add event listeners to the buttons
            setTimeout(() => {
              const selectButton = document.getElementById('select-location');
              const cancelButton = document.getElementById('cancel-location');

              if (selectButton) {
                selectButton.addEventListener('click', (event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  console.log('Select button clicked, calling onLocationSelected');
                  onLocationSelected({
                    lat,
                    lng,
                    address
                  });
                  popup.remove();
                });

                // Add touch event for mobile
                selectButton.addEventListener('touchend', (event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  console.log('Select button touched, calling onLocationSelected');
                  onLocationSelected({
                    lat,
                    lng,
                    address
                  });
                  popup.remove();
                });
              }

              if (cancelButton) {
                cancelButton.addEventListener('click', (event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  popup.remove();
                  marker.remove();
                });

                // Add touch event for mobile
                cancelButton.addEventListener('touchend', (event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  popup.remove();
                  marker.remove();
                });
              }
            }, 100);

          } catch (error) {
            console.error('Error geocoding location:', error);

            // Still allow selection even if geocoding fails
            const popup = new window.mapboxgl.Popup({
              offset: 25,
              closeButton: true,
              closeOnClick: false
            })
              .setLngLat([lng, lat])
              .setHTML(`
                <div style="text-align: center; padding: 8px;">
                  <p style="margin: 0 0 12px 0; color: #374151;">Selected location</p>
                  <p style="margin: 0 0 12px 0; font-size: 12px; color: #6b7280;">Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                  <div style="display: flex; gap: 8px; justify-content: center;">
                    <button id="select-location-fallback" style="
                      background-color: #3b82f6;
                      color: white;
                      border: none;
                      padding: 8px 16px;
                      border-radius: 6px;
                      cursor: pointer;
                      font-size: 14px;
                      min-height: 44px;
                      touch-action: manipulation;
                    ">
                      ✓ Select
                    </button>
                  </div>
                </div>
              `)
              .addTo(map);

            setTimeout(() => {
              const selectButton = document.getElementById('select-location-fallback');
              if (selectButton) {
                const handleSelect = (event: Event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  onLocationSelected({
                    lat,
                    lng,
                    address: 'Selected location'
                  });
                  popup.remove();
                };

                selectButton.addEventListener('click', handleSelect);
                selectButton.addEventListener('touchend', handleSelect);
              }
            }, 100);

          } finally {
            setIsLoading(false);
          }
        };

        // Add both click and touchend event listeners
        map.on('click', handleLocationSelect);
        map.on('touchend', handleLocationSelect);
      } else {
        // Reset cursor for non-selectable mode
        map.getCanvas().style.cursor = '';
      }

      // Clean up on unmount
      return () => map.remove();
    };

    loadMapbox();
  }, [selectable, onLocationSelected]);

  // Update map when initialLocation changes
  useEffect(() => {
    if (initialLocation && initialLocation.lat !== 0 && initialLocation.lng !== 0) {
      // Wait for map to be initialized
      const checkMapInterval = setInterval(() => {
        if (mapRef.current) {
          updateMapLocation(initialLocation);
          clearInterval(checkMapInterval);
        }
      }, 100);

      // Clear interval after 5 seconds to prevent memory leaks
      setTimeout(() => clearInterval(checkMapInterval), 5000);
    }
  }, [initialLocation]);

  return (
    <div style={{ position: 'relative' }}>
      <div
        ref={mapContainerRef}
        style={{
          width: '100%',
          height,
          position: 'relative',
          border: '1px solid #ddd',
          borderRadius: '4px',
        }}
      />

      {/* Loading indicator */}
      {isLoading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10,
          }}
        >
          <div
            style={{
              backgroundColor: 'white',
              padding: '12px',
              borderRadius: '50%',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" fill="none" stroke="#3b82f6" strokeWidth="4" opacity="0.25" />
              <circle
                cx="12"
                cy="12"
                r="10"
                fill="none"
                stroke="#3b82f6"
                strokeWidth="4"
                strokeDasharray="60 30"
                style={{ animation: 'spin 1s linear infinite' }}
              />
              <style>{`
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              `}</style>
            </svg>
          </div>
        </div>
      )}

      {/* Instructions */}
      {selectable && (
        <div
          style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            right: '10px',
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            zIndex: 5,
            fontSize: '14px',
            fontWeight: '500',
            textAlign: 'center',
            border: '2px solid #1e40af',
          }}
        >
          📍 Tap anywhere on the map to select your pickup location
        </div>
      )}
    </div>
  );
}

// Add type definition for window.mapboxgl
declare global {
  interface Window {
    mapboxgl: any;
  }
}
