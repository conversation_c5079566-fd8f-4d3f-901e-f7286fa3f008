(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[459],{3236:function(e,o,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return r(2078)}])},2078:function(e,o,r){"use strict";r.r(o),r.d(o,{default:function(){return f}});var t=r(5893),s=r(7294),l=r(1163),n=r(9008),a=r.n(n),i=r(1664),c=r.n(i),d=r(6492),u=r(8082),m=r(404),h=r(109);let g=async()=>{try{console.log("=== DEBUGGING USERS ===");let e=(0,h.hJ)(m.db,"users"),o=await (0,h.PL)(e);if(o.empty){console.log("No users found in the database");return}console.log("Found ".concat(o.docs.length," users:")),o.docs.forEach((e,o)=>{var r,t;let s=e.data();console.log("\n--- User ".concat(o+1," ---")),console.log("ID:",e.id),console.log("Email:",s.email),console.log("Phone Number (original):",s.phoneNumber),console.log("Phone Number (normalized):",s.phoneNumber?(0,u.XL)(s.phoneNumber):"N/A"),console.log("Full Name:",s.fullName),console.log("Role:",s.role),console.log("Created At:",(null===(t=s.createdAt)||void 0===t?void 0:null===(r=t.toDate)||void 0===r?void 0:r.call(t))||s.createdAt)}),console.log("\n=== END DEBUG ===")}catch(e){console.error("Error debugging users:",e)}},x=()=>{console.log("=== TESTING PHONE NORMALIZATION ==="),["1234567890","+1234567890","(*************","************","************","+****************","+251 123 456 789","0123456789","+251-123-456-789"].forEach(e=>{console.log('Original: "'.concat(e,'" -> Normalized: "').concat((0,u.XL)(e),'"'))}),console.log("=== END NORMALIZATION TEST ===")},b=async()=>{try{let{createUserWithEmailAndPassword:e}=await Promise.resolve().then(r.bind(r,1517)),{doc:o,setDoc:t}=await Promise.resolve().then(r.bind(r,109)),{auth:s}=await Promise.resolve().then(r.bind(r,404));console.log("=== CREATING TEST USER ===");let l="<EMAIL>",n="test123456",a="+************",i=(await e(s,l,n)).user.uid,c={id:i,email:l,phoneNumber:a,fullName:"Test User",role:"rider",createdAt:new Date,updatedAt:new Date,bookingHistory:[]};return await t(o(m.db,"users",i),c),console.log("Test user created successfully:"),console.log("Email:",l),console.log("Password:",n),console.log("Phone:",a),console.log("Normalized Phone:",(0,u.XL)(a)),console.log("=== END TEST USER CREATION ==="),c}catch(e){console.error("Error creating test user:",e instanceof Error?e.message:e),"object"==typeof e&&null!==e&&"code"in e&&"auth/email-already-in-use"===e.code&&(console.log("Test user already exists. You can use:"),console.log("Email: <EMAIL>"),console.log("Password: test123456"),console.log("Phone: +************"))}};function f(){let[e,o]=(0,s.useState)(""),[r,n]=(0,s.useState)(""),[i,m]=(0,s.useState)(!1),[h,f]=(0,s.useState)(""),[p,w]=(0,s.useState)(!1),N=(0,l.useRouter)(),{showNotification:v}=(0,d.l)(),y=async o=>{if(o.preventDefault(),!e){f("Please enter your email or phone number");return}if(!r){f("Please enter your password");return}w(!0),f("");try{console.log("Attempting login with identifier:",e),console.log("Is phone number:",(0,u.xT)(e)),(0,u.xT)(e)&&console.log("Normalized phone number:",(0,u.XL)(e));let o=await (0,u.So)(e,r);console.log("Login successful for user:",o),v("Login successful! Redirecting...","success",3e3),"admin"===o.role?N.push("/admin/dashboard"):"driver"===o.role?N.push("/driver/dashboard"):N.push("/")}catch(o){if(console.error("Login error:",o),o instanceof Error){if(console.log("Error message:",o.message),o.message.includes("user-not-found")||o.message.includes("User not found")){let o=(0,u.xT)(e)?"No account found with this phone number. Please check the number or create a new account.":"No account found with this email address. Please check the email or create a new account.";f(o),v(o,"error",5e3)}else o.message.includes("wrong-password")||o.message.includes("invalid-credential")?(f("Incorrect password. Please try again."),v("Incorrect password. Please try again.","error",3e3)):o.message.includes("Invalid identifier format")?(f("Please enter a valid email or phone number."),v("Please enter a valid email or phone number.","error",3e3)):(f("Login failed: ".concat(o.message)),v("Login failed: ".concat(o.message),"error",5e3))}else f("Failed to login. Please check your credentials."),v("Login failed. Please check your credentials.","error",3e3)}finally{w(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 safe-area-top safe-area-bottom",children:[(0,t.jsxs)(a(),{children:[(0,t.jsx)("title",{children:"BaroRide - Login"}),(0,t.jsx)("meta",{name:"description",content:"Login to your BaroRide account"}),(0,t.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"})]}),(0,t.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,t.jsxs)("div",{className:"flex-shrink-0 pt-8 pb-4 px-4 text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-16 w-16 sm:h-20 sm:w-20"})}),(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Sign in to your BaroRide account"})]}),(0,t.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 pb-8",children:(0,t.jsxs)("div",{className:"w-full max-w-sm",children:[h&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 text-sm",children:h}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"identifier",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email or Phone Number"}),(0,t.jsx)("input",{id:"identifier",type:"text",value:e,onChange:e=>o(e.target.value),placeholder:"Enter your email or phone number",className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors",autoComplete:"username",autoCapitalize:"none",autoCorrect:"off",spellCheck:"false",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{id:"password",type:i?"text":"password",value:r,onChange:e=>n(e.target.value),placeholder:"Enter your password",className:"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors",autoComplete:"current-password",autoCapitalize:"none",autoCorrect:"off",spellCheck:"false",required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target",onClick:()=>m(!i),"aria-label":i?"Hide password":"Show password",children:i?(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,t.jsx)("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),(0,t.jsx)("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):(0,t.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,t.jsx)("path",{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}),(0,t.jsx)("path",{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"})]})})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(c(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-700 transition-colors touch-target",children:"Forgot password?"})}),(0,t.jsx)("button",{type:"submit",className:"mobile-button w-full py-4 px-6 rounded-lg font-semibold text-base transition-all duration-200 touch-target ".concat(p?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl"," text-white flex items-center justify-center"),disabled:p,children:p?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mobile-spinner mr-3"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),"Sign In"]})})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(c(),{href:"/signup",className:"text-blue-600 hover:text-blue-700 font-semibold transition-colors touch-target",children:"Create one here"})]})}),(0,t.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,t.jsx)("button",{type:"button",onClick:()=>{g(),x()},className:"w-full py-3 px-4 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600 touch-target",children:"Debug Users (Check Console)"}),(0,t.jsx)("button",{type:"button",onClick:()=>{b()},className:"w-full py-3 px-4 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 touch-target",children:"Create Test User (Check Console)"})]})]})})]})]})}},8082:function(e,o,r){"use strict";r.d(o,{So:function(){return d},XL:function(){return a},xT:function(){return i}});var t=r(404),s=r(1517),l=r(109);let n=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),a=e=>{let o=e.replace(/[^\d+]/g,"");return o.startsWith("+")?"+"+o.substring(1).replace(/\+/g,""):o.replace(/\+/g,"")},i=e=>{let o=a(e);return/^(\+\d{1,3})?\d{7,15}$/.test(o)||[/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,/^[+]?[0-9]{1,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}[-\s.]?[0-9]{3,4}$/,/^[0-9]{10,15}$/,/^\+[0-9]{7,15}$/].some(o=>o.test(e))},c=async e=>{try{let o=(0,l.hJ)(t.db,"users"),r=(0,l.IO)(o,(0,l.ar)("phoneNumber","==",e)),s=await (0,l.PL)(r);if(!s.empty){let e=s.docs[0];return{id:e.id,...e.data()}}let n=a(e),i=(0,l.IO)(o);for(let e of(await (0,l.PL)(i)).docs){let o=e.data();if(o.phoneNumber&&a(o.phoneNumber)===n)return{id:e.id,...o}}return null}catch(e){return console.error("Error finding user by phone number:",e),null}},d=async(e,o)=>{try{let r,a;if(n(e)){r=await (0,s.e5)(t.auth,e,o);let n=await (0,l.QT)((0,l.doc)(t.db,"users",r.user.uid));a={id:n.id,...n.data()}}else if(i(e)){let l=await c(e);if(!l)throw Error("User not found with this phone number");r=await (0,s.e5)(t.auth,l.email,o),a=l}else throw Error("Invalid identifier format. Please enter a valid email or phone number.");return a}catch(e){throw console.error("Authentication error:",e),e}}}},function(e){e.O(0,[996,888,774,179],function(){return e(e.s=3236)}),_N_E=e.O()}]);