(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[742],{7627:function(e,s,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/forgot-password",function(){return t(1536)}])},1536:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return x}});var r=t(5893),a=t(7294),n=t(1517),l=t(404),i=t(1163),o=t(9008),c=t.n(o),d=t(1664),u=t.n(d),m=t(6492);function x(){let[e,s]=(0,a.useState)(""),[t,o]=(0,a.useState)(!1),[d,x]=(0,a.useState)(""),[h,b]=(0,a.useState)(!1);(0,i.useRouter)();let{showNotification:f}=(0,m.l)(),g=async s=>{if(s.preventDefault(),!e){x("Please enter your email address");return}o(!0),x("");try{await (0,n.LS)(l.auth,e),b(!0),f("Password reset email sent! Check your inbox.","success",5e3)}catch(s){let e=s instanceof Error?s.message:"Failed to send reset email";e.includes("user-not-found")?x("No account found with this email address"):e.includes("invalid-email")?x("Please enter a valid email address"):x("Failed to send reset email. Please try again later."),f("Error: "+(s instanceof Error?s.message:"Failed to send reset email"),"error",5e3)}finally{o(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 safe-area-top safe-area-bottom",children:[(0,r.jsxs)(c(),{children:[(0,r.jsx)("title",{children:"BaroRide - Forgot Password"}),(0,r.jsx)("meta",{name:"description",content:"Reset your BaroRide account password"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"})]}),(0,r.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,r.jsxs)("div",{className:"flex-shrink-0 pt-8 pb-4 px-4 text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)("img",{src:"/logo-icon.svg",alt:"BaroRide Logo",className:"h-16 w-16 sm:h-20 sm:w-20"})}),(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"Reset Password"}),(0,r.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"We'll help you get back into your account"})]}),(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 pb-8",children:(0,r.jsx)("div",{className:"w-full max-w-sm",children:h?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4",children:[(0,r.jsx)("p",{children:"Password reset email sent!"}),(0,r.jsx)("p",{className:"text-sm mt-2",children:"Check your email inbox for instructions to reset your password."})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(u(),{href:"/login",className:"text-blue-500 hover:text-blue-700",children:"Return to Login"})})]}):(0,r.jsxs)(r.Fragment,{children:[d&&(0,r.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:d}),(0,r.jsx)("p",{className:"mb-4 text-gray-600",children:"Enter your email address below and we'll send you instructions to reset your password."}),(0,r.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,r.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),placeholder:"Enter your email",className:"w-full p-2 border rounded focus:ring-blue-500 focus:border-blue-500",disabled:t})]}),(0,r.jsx)("button",{type:"submit",className:"w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ".concat(t?"opacity-70 cursor-not-allowed":""),disabled:t,children:t?"Sending...":"Send Reset Link"}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsx)(u(),{href:"/login",className:"text-sm text-blue-500 hover:text-blue-700",children:"Back to Login"})})]})]})})})]})]})}}},function(e){e.O(0,[996,888,774,179],function(){return e(e.s=7627)}),_N_E=e.O()}]);