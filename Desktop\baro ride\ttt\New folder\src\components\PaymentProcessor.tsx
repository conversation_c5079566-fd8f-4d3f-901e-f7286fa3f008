import { useState, useEffect } from 'react';
import { useNotification } from '@/contexts/NotificationContext';

// API key for payment processing
const PAYMENT_API_KEY = 'sXi288Mu8Z5Hw8LJpUkJwHNtwF8ygxPPPgulAxtg/qtc3lukW4dCLMXmgyJE/u2n2S5HIIqdzsC6HcukLykNCg==';

// Payment method types
export type PaymentMethod = 'credit_card' | 'debit_card' | 'mobile_money';

// Payment status types
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed';

// Payment information interface
export interface PaymentInfo {
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  cardNumber?: string;
  cardExpiry?: string;
  cardCVC?: string;
  mobileNumber?: string;
  description: string;
}

// Payment result interface
export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  status: PaymentStatus;
  message: string;
  timestamp: Date;
}

interface PaymentProcessorProps {
  amount: number;
  onPaymentComplete: (result: PaymentResult) => void;
  onCancel: () => void;
  description?: string;
}

export default function PaymentProcessor({
  amount,
  onPaymentComplete,
  onCancel,
  description = 'BaroRide Booking'
}: PaymentProcessorProps) {
  const { showNotification } = useNotification();
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('credit_card');
  const [cardNumber, setCardNumber] = useState('');
  const [cardExpiry, setCardExpiry] = useState('');
  const [cardCVC, setCardCVC] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showCardFields, setShowCardFields] = useState(true);
  const [showMobileFields, setShowMobileFields] = useState(false);

  // Update visible fields based on payment method
  useEffect(() => {
    setShowCardFields(paymentMethod === 'credit_card' || paymentMethod === 'debit_card');
    setShowMobileFields(paymentMethod === 'mobile_money');
  }, [paymentMethod]);

  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  // Format expiry date (MM/YY)
  const formatExpiry = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');

    if (v.length >= 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }

    return v;
  };

  // Process the payment
  const processPayment = async () => {
    // Validate inputs based on payment method
    if (paymentMethod === 'credit_card' || paymentMethod === 'debit_card') {
      if (!cardNumber || cardNumber.replace(/\s+/g, '').length < 16) {
        showNotification('Please enter a valid card number', 'error');
        return;
      }

      if (!cardExpiry || cardExpiry.length < 5) {
        showNotification('Please enter a valid expiry date (MM/YY)', 'error');
        return;
      }

      if (!cardCVC || cardCVC.length < 3) {
        showNotification('Please enter a valid CVC code', 'error');
        return;
      }
    } else if (paymentMethod === 'mobile_money') {
      if (!mobileNumber || mobileNumber.length < 10) {
        showNotification('Please enter a valid mobile number', 'error');
        return;
      }
    }

    setIsProcessing(true);
    showNotification('Processing payment...', 'info');

    try {
      // Create payment info object
      const paymentInfo: PaymentInfo = {
        amount,
        currency: 'ETB', // Ethiopian Birr
        paymentMethod,
        description: description || 'BaroRide Booking',
      };

      // Add payment method specific details
      if (paymentMethod === 'credit_card' || paymentMethod === 'debit_card') {
        paymentInfo.cardNumber = cardNumber.replace(/\s+/g, '');
        paymentInfo.cardExpiry = cardExpiry;
        paymentInfo.cardCVC = cardCVC;
      } else if (paymentMethod === 'mobile_money') {
        paymentInfo.mobileNumber = mobileNumber;
      }

      // Simulate API call to payment gateway
      // In a real implementation, this would be a fetch call to your payment API
      const result = await simulatePaymentProcessing(paymentInfo);

      // Handle the result
      if (result.success) {
        showNotification(`Payment successful! Transaction ID: ${result.transactionId}`, 'success');
      } else {
        showNotification(`Payment failed: ${result.message}`, 'error');
      }

      // Call the completion callback
      onPaymentComplete(result);
    } catch (error) {
      console.error('Payment processing error:', error);

      const errorResult: PaymentResult = {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'An unknown error occurred',
        timestamp: new Date()
      };

      showNotification(`Payment failed: ${errorResult.message}`, 'error');
      onPaymentComplete(errorResult);
    } finally {
      setIsProcessing(false);
    }
  };

  // Simulate payment processing (replace with actual API call in production)
  const simulatePaymentProcessing = async (paymentInfo: PaymentInfo): Promise<PaymentResult> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Generate a random transaction ID
    const transactionId = `TR${Date.now().toString().slice(-8)}${Math.floor(Math.random() * 1000)}`;

    // Simulate success (90% success rate)
    const isSuccess = Math.random() < 0.9;

    return {
      success: isSuccess,
      transactionId: isSuccess ? transactionId : undefined,
      status: isSuccess ? 'completed' : 'failed',
      message: isSuccess ? 'Payment processed successfully' : 'Payment declined by issuer',
      timestamp: new Date()
    };
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 sm:p-6 w-full">
      <div className="text-center mb-4">
        <h2 className="text-lg sm:text-xl font-bold text-gray-900">Payment Details</h2>
        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
          <p className="text-lg sm:text-xl font-bold text-blue-900">${amount.toFixed(2)} ETB</p>
          <p className="text-xs sm:text-sm text-gray-600 mt-1">{description}</p>
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
        <select
          value={paymentMethod}
          onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base"
          disabled={isProcessing}
        >
          <option value="credit_card">💳 Credit Card</option>
          <option value="debit_card">💳 Debit Card</option>
          <option value="mobile_money">📱 Mobile Money</option>
        </select>
      </div>

      {showCardFields && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Card Number</label>
            <input
              type="text"
              placeholder="1234 5678 9012 3456"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base"
              value={cardNumber}
              onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
              maxLength={19}
              disabled={isProcessing}
              autoComplete="cc-number"
            />
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
              <input
                type="text"
                placeholder="MM/YY"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base"
                value={cardExpiry}
                onChange={(e) => setCardExpiry(formatExpiry(e.target.value))}
                maxLength={5}
                disabled={isProcessing}
                autoComplete="cc-exp"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">CVC</label>
              <input
                type="text"
                placeholder="123"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base"
                value={cardCVC}
                onChange={(e) => setCardCVC(e.target.value.replace(/[^0-9]/g, '').substring(0, 3))}
                maxLength={3}
                disabled={isProcessing}
                autoComplete="cc-csc"
              />
            </div>
          </div>
        </div>
      )}

      {showMobileFields && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
          <input
            type="tel"
            placeholder="e.g., 0911234567"
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base"
            value={mobileNumber}
            onChange={(e) => setMobileNumber(e.target.value.replace(/[^0-9]/g, ''))}
            disabled={isProcessing}
            autoComplete="tel"
          />
          <p className="text-xs text-gray-500 mt-2">📱 You will receive a payment confirmation code via SMS</p>
        </div>
      )}

      <div className="mt-6 flex flex-col sm:flex-row gap-3 sm:justify-between">
        <button
          type="button"
          onClick={onCancel}
          className="w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-lg text-base font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors touch-manipulation"
          disabled={isProcessing}
          style={{ touchAction: 'manipulation' }}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={processPayment}
          className={`w-full sm:w-auto px-6 py-3 rounded-lg text-base font-medium text-white transition-all duration-200 touch-manipulation ${
            isProcessing
              ? 'bg-blue-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg'
          }`}
          disabled={isProcessing}
          style={{ touchAction: 'manipulation' }}
        >
          {isProcessing ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing Payment...
            </span>
          ) : (
            <span className="flex items-center justify-center">
              💳 Pay ${amount.toFixed(2)}
            </span>
          )}
        </button>
      </div>
    </div>
  );
}
