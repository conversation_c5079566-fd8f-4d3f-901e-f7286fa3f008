/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cindex.tsx&page=%2F!":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cindex.tsx&page=%2F! ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.tsx */ \"./src/pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNidXJhayU1Q0Rlc2t0b3AlNUNiYXJvJTIwcmlkZSU1Q3R0dCU1Q05ldyUyMGZvbGRlciU1Q3NyYyU1Q3BhZ2VzJTVDaW5kZXgudHN4JnBhZ2U9JTJGISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLG9EQUF1QjtBQUM5QztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZTJjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vc3JjL3BhZ2VzL2luZGV4LnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlhQSxxQkFBbUI7ZUFBbkJBOztJQUpBQyxpQkFBZTtlQUFmQTs7SUFHQUMsaUJBQWU7ZUFBZkE7O0lBSkFDLGdCQUFjO2VBQWRBOztJQUVBQyxnQkFBYztlQUFkQTs7SUFJQUMsc0JBQW9CO2VBQXBCQTs7SUFIQUMscUJBQW1CO2VBQW5CQTs7Ozs7Ozs7SUF1UUdDLFlBQVU7ZUFBVkE7OztBQTFRVCxNQUFNSixpQkFBaUI7QUFDdkIsTUFBTUYsa0JBQWtCO0FBQ3hCLE1BQU1HLGlCQUFpQjtBQUN2QixNQUFNRSxzQkFBc0I7QUFDNUIsTUFBTUosa0JBQWtCO0FBQ3hCLE1BQU1GLHNCQUFzQjtBQUM1QixNQUFNSyx1QkFBdUI7O1VBdUl4QkcsWUFBQUE7Ozs7R0FBQUEsZ0JBQUFBLENBQUFBLGVBQUFBLENBQUFBLENBQUFBOztVQThEQUMsd0JBQUFBOzs7OztHQUFBQSw0QkFBQUEsQ0FBQUEsMkJBQUFBLENBQUFBLENBQUFBO0FBK0RMLFNBQVNGLFdBQVdHLEtBQVU7SUFDbkMsNkVBQTZFO0lBQzdFLDZFQUE2RTtJQUM3RSx1RUFBdUU7SUFDdkUsZ0JBQWdCO0lBQ2hCLE9BQ0VBLFNBQ0MsUUFBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsZUFDL0MsT0FBT0EsTUFBTUMsSUFBSSxLQUFLO0FBRTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMudHM/ZWYxYyJdLCJuYW1lcyI6WyJBQ1RJT05fRkFTVF9SRUZSRVNIIiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1BSRUZFVENIIiwiQUNUSU9OX1JFRlJFU0giLCJBQ1RJT05fUkVTVE9SRSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiQUNUSU9OX1NFUlZFUl9QQVRDSCIsImlzVGhlbmFibGUiLCJQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJ2YWx1ZSIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC91c2UtaW50ZXJzZWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBK0ZnQkE7OztlQUFBQTs7O21DQS9GeUM7aURBSWxEO0FBcUJQLE1BQU1DLDBCQUEwQixPQUFPQyx5QkFBeUI7QUFFaEUsTUFBTUMsWUFBWSxJQUFJQztBQUN0QixNQUFNQyxTQUF1QixFQUFFO0FBRS9CLFNBQVNDLGVBQWVDLE9BQW9DO0lBQzFELE1BQU1DLEtBQUs7UUFDVEMsTUFBTUYsUUFBUUUsSUFBSSxJQUFJO1FBQ3RCQyxRQUFRSCxRQUFRSSxVQUFVLElBQUk7SUFDaEM7SUFDQSxNQUFNQyxXQUFXUCxPQUFPUSxJQUFJLENBQzFCLENBQUNDLE1BQVFBLElBQUlMLElBQUksS0FBS0QsR0FBR0MsSUFBSSxJQUFJSyxJQUFJSixNQUFNLEtBQUtGLEdBQUdFLE1BQU07SUFFM0QsSUFBSUs7SUFFSixJQUFJSCxVQUFVO1FBQ1pHLFdBQVdaLFVBQVVhLEdBQUcsQ0FBQ0o7UUFDekIsSUFBSUcsVUFBVTtZQUNaLE9BQU9BO1FBQ1Q7SUFDRjtJQUVBLE1BQU1FLFdBQVcsSUFBSWI7SUFDckIsTUFBTWMsV0FBVyxJQUFJaEIscUJBQXFCLENBQUNpQjtRQUN6Q0EsUUFBUUMsT0FBTyxDQUFDLENBQUNDO1lBQ2YsTUFBTUMsV0FBV0wsU0FBU0QsR0FBRyxDQUFDSyxNQUFNRSxNQUFNO1lBQzFDLE1BQU1DLFlBQVlILE1BQU1JLGNBQWMsSUFBSUosTUFBTUssaUJBQWlCLEdBQUc7WUFDcEUsSUFBSUosWUFBWUUsV0FBVztnQkFDekJGLFNBQVNFO1lBQ1g7UUFDRjtJQUNGLEdBQUdqQjtJQUNIUSxXQUFXO1FBQ1RQO1FBQ0FVO1FBQ0FEO0lBQ0Y7SUFFQVosT0FBT3NCLElBQUksQ0FBQ25CO0lBQ1pMLFVBQVV5QixHQUFHLENBQUNwQixJQUFJTztJQUNsQixPQUFPQTtBQUNUO0FBRUEsU0FBU2MsUUFDUEMsT0FBZ0IsRUFDaEJSLFFBQXlCLEVBQ3pCZixPQUFvQztJQUVwQyxNQUFNLEVBQUVDLEVBQUUsRUFBRVUsUUFBUSxFQUFFRCxRQUFRLEVBQUUsR0FBR1gsZUFBZUM7SUFDbERVLFNBQVNXLEdBQUcsQ0FBQ0UsU0FBU1I7SUFFdEJKLFNBQVNXLE9BQU8sQ0FBQ0M7SUFDakIsT0FBTyxTQUFTQztRQUNkZCxTQUFTZSxNQUFNLENBQUNGO1FBQ2hCWixTQUFTYSxTQUFTLENBQUNEO1FBRW5CLHVEQUF1RDtRQUN2RCxJQUFJYixTQUFTZ0IsSUFBSSxLQUFLLEdBQUc7WUFDdkJmLFNBQVNnQixVQUFVO1lBQ25CL0IsVUFBVTZCLE1BQU0sQ0FBQ3hCO1lBQ2pCLE1BQU0yQixRQUFROUIsT0FBTytCLFNBQVMsQ0FDNUIsQ0FBQ3RCLE1BQVFBLElBQUlMLElBQUksS0FBS0QsR0FBR0MsSUFBSSxJQUFJSyxJQUFJSixNQUFNLEtBQUtGLEdBQUdFLE1BQU07WUFFM0QsSUFBSXlCLFFBQVEsQ0FBQyxHQUFHO2dCQUNkOUIsT0FBT2dDLE1BQU0sQ0FBQ0YsT0FBTztZQUN2QjtRQUNGO0lBQ0Y7QUFDRjtBQUVPLFNBQVNuQyxnQkFBbUNzQyxLQUlqQztJQUppQyxNQUNqREMsT0FBTyxFQUNQNUIsVUFBVSxFQUNWNkIsUUFBUSxFQUNRLEdBSmlDRjtJQUtqRCxNQUFNRyxhQUFzQkQsWUFBWSxDQUFDdkM7SUFFekMsTUFBTSxDQUFDeUMsU0FBU0MsV0FBVyxHQUFHQyxDQUFBQSxHQUFBQSxPQUFBQSxRQUFRLEVBQUM7SUFDdkMsTUFBTUMsYUFBYUMsQ0FBQUEsR0FBQUEsT0FBQUEsTUFBTSxFQUFXO0lBQ3BDLE1BQU1DLGFBQWFDLENBQUFBLEdBQUFBLE9BQUFBLFdBQVcsRUFBQyxDQUFDbEI7UUFDOUJlLFdBQVdJLE9BQU8sR0FBR25CO0lBQ3ZCLEdBQUcsRUFBRTtJQUVMb0IsQ0FBQUEsR0FBQUEsT0FBQUEsU0FBUyxFQUFDO1FBQ1IsSUFBSWpELHlCQUF5QjtZQUMzQixJQUFJd0MsY0FBY0MsU0FBUztZQUUzQixNQUFNWixVQUFVZSxXQUFXSSxPQUFPO1lBQ2xDLElBQUluQixXQUFXQSxRQUFRcUIsT0FBTyxFQUFFO2dCQUM5QixNQUFNcEIsWUFBWUYsUUFDaEJDLFNBQ0EsQ0FBQ04sWUFBY0EsYUFBYW1CLFdBQVduQixZQUN2QztvQkFBRWYsTUFBTThCLFdBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFFBQVNVLE9BQU87b0JBQUV0QztnQkFBVztnQkFHdkMsT0FBT29CO1lBQ1Q7UUFDRixPQUFPO1lBQ0wsSUFBSSxDQUFDVyxTQUFTO2dCQUNaLE1BQU1VLGVBQWVDLENBQUFBLEdBQUFBLHFCQUFBQSxtQkFBbUIsRUFBQyxJQUFNVixXQUFXO2dCQUMxRCxPQUFPLElBQU1XLENBQUFBLEdBQUFBLHFCQUFBQSxrQkFBa0IsRUFBQ0Y7WUFDbEM7UUFDRjtJQUNBLHVEQUF1RDtJQUN6RCxHQUFHO1FBQUNYO1FBQVk5QjtRQUFZNEI7UUFBU0c7UUFBU0csV0FBV0ksT0FBTztLQUFDO0lBRWpFLE1BQU1NLGVBQWVQLENBQUFBLEdBQUFBLE9BQUFBLFdBQVcsRUFBQztRQUMvQkwsV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLE9BQU87UUFBQ0k7UUFBWUw7UUFBU2E7S0FBYTtBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2NsaWVudC91c2UtaW50ZXJzZWN0aW9uLnRzeD81N2VlIl0sIm5hbWVzIjpbInVzZUludGVyc2VjdGlvbiIsImhhc0ludGVyc2VjdGlvbk9ic2VydmVyIiwiSW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJvYnNlcnZlcnMiLCJNYXAiLCJpZExpc3QiLCJjcmVhdGVPYnNlcnZlciIsIm9wdGlvbnMiLCJpZCIsInJvb3QiLCJtYXJnaW4iLCJyb290TWFyZ2luIiwiZXhpc3RpbmciLCJmaW5kIiwib2JqIiwiaW5zdGFuY2UiLCJnZXQiLCJlbGVtZW50cyIsIm9ic2VydmVyIiwiZW50cmllcyIsImZvckVhY2giLCJlbnRyeSIsImNhbGxiYWNrIiwidGFyZ2V0IiwiaXNWaXNpYmxlIiwiaXNJbnRlcnNlY3RpbmciLCJpbnRlcnNlY3Rpb25SYXRpbyIsInB1c2giLCJzZXQiLCJvYnNlcnZlIiwiZWxlbWVudCIsInVub2JzZXJ2ZSIsImRlbGV0ZSIsInNpemUiLCJkaXNjb25uZWN0IiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJzcGxpY2UiLCJwYXJhbSIsInJvb3RSZWYiLCJkaXNhYmxlZCIsImlzRGlzYWJsZWQiLCJ2aXNpYmxlIiwic2V0VmlzaWJsZSIsInVzZVN0YXRlIiwiZWxlbWVudFJlZiIsInVzZVJlZiIsInNldEVsZW1lbnQiLCJ1c2VDYWxsYmFjayIsImN1cnJlbnQiLCJ1c2VFZmZlY3QiLCJ0YWdOYW1lIiwiaWRsZUNhbGxiYWNrIiwicmVxdWVzdElkbGVDYWxsYmFjayIsImNhbmNlbElkbGVDYWxsYmFjayIsInJlc2V0VmlzaWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Layout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\n\n\n\nfunction Layout(param) {\n    let { children, title = \"BaroRide\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Book your ride with BaroRide - fixed fares and reliable service\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"/logo-icon.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col bg-white text-gray-900 overflow-x-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow w-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-100 border-t border-gray-200 py-4 mt-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center text-gray-600 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" BaroRide. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDNkI7QUFDQztBQU9mLFNBQVNFLE9BQU8sS0FBNkM7UUFBN0MsRUFBRUMsUUFBUSxFQUFFQyxRQUFRLFVBQVUsRUFBZSxHQUE3QztJQUM3QixxQkFDRTs7MEJBQ0UsOERBQUNKLGtEQUFJQTs7a0NBQ0gsOERBQUNJO2tDQUFPQTs7Ozs7O2tDQUNSLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBK0JDLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUF3Q0MsU0FBUTs7Ozs7O2tDQUMzRCw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQTZCQyxTQUFROzs7Ozs7a0NBQ2hELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBeUJDLFNBQVE7Ozs7OztrQ0FDNUMsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFnQkMsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUVqQyw4REFBQ0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDWCwrQ0FBTUE7Ozs7O2tDQUNQLDhEQUFDWTt3QkFBS0QsV0FBVTtrQ0FBb0JUOzs7Ozs7a0NBQ3BDLDhEQUFDVzt3QkFBT0YsV0FBVTtrQ0FDaEIsNEVBQUNEOzRCQUFJQyxXQUFVOztnQ0FBMkQ7Z0NBQ2hFLElBQUlHLE9BQU9DLFdBQVc7Z0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTdDO0tBM0J3QmQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0LnRzeD9kZThiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgTmF2YmFyIGZyb20gJy4vTmF2YmFyJztcblxuaW50ZXJmYWNlIExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbiAgdGl0bGU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuLCB0aXRsZSA9ICdCYXJvUmlkZScgfTogTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT57dGl0bGV9PC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkJvb2sgeW91ciByaWRlIHdpdGggQmFyb1JpZGUgLSBmaXhlZCBmYXJlcyBhbmQgcmVsaWFibGUgc2VydmljZVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MS4wLCBtYXhpbXVtLXNjYWxlPTEuMCwgdXNlci1zY2FsYWJsZT1ub1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjMjU2M2ViXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXN0YXR1cy1iYXItc3R5bGVcIiBjb250ZW50PVwiZGVmYXVsdFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsZS1tb2JpbGUtd2ViLWFwcC10aXRsZVwiIGNvbnRlbnQ9XCJCYXJvUmlkZVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJtb2JpbGUtd2ViLWFwcC1jYXBhYmxlXCIgY29udGVudD1cInllc1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2xvZ28taWNvbi5zdmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJhcHBsZS10b3VjaC1pY29uXCIgaHJlZj1cIi9sb2dvLWljb24uc3ZnXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwic2hvcnRjdXQgaWNvblwiIGhyZWY9XCIvbG9nby1pY29uLnN2Z1wiIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sIGJnLXdoaXRlIHRleHQtZ3JheS05MDAgb3ZlcmZsb3cteC1oaWRkZW5cIj5cbiAgICAgICAgPE5hdmJhciAvPlxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LWdyb3cgdy1mdWxsXCI+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHktNCBtdC1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHRleHQtY2VudGVyIHRleHQtZ3JheS02MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgJmNvcHk7IHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IEJhcm9SaWRlLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJOYXZiYXIiLCJMYXlvdXQiLCJjaGlsZHJlbiIsInRpdGxlIiwibWV0YSIsIm5hbWUiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIiwiZm9vdGVyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n"));

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NotificationBell */ \"./src/components/NotificationBell.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Navbar() {\n    _s();\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBackButton, setShowBackButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Determine if back button should be shown based on current route\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Show back button on all pages except home page\n        setShowBackButton(router.pathname !== \"/\");\n    }, [\n        router.pathname\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleBack = ()=>{\n        router.back();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-md border-b border-gray-200 sticky top-0 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-2 sm:px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between h-14 sm:h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                showBackButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBack,\n                                    className: \"mr-2 sm:mr-3 p-2 sm:p-3 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none touch-manipulation transition-colors\",\n                                    \"aria-label\": \"Go back\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 sm:w-7 sm:h-7 text-gray-700\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex-shrink-0 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo-icon.svg\",\n                                            alt: \"BaroRide Logo\",\n                                            className: \"h-8 w-8 sm:h-10 sm:w-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600 font-bold text-lg sm:text-xl\",\n                                            children: \"BaroRide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/book\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Book a Ride\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/driver/dashboard\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"flex items-center text-gray-700 hover:text-blue-600 focus:outline-none\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: user.fullName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this),\n                                            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"text-gray-700 hover:text-blue-600\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-1\",\n                            children: [\n                                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 22\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 text-gray-700 hover:text-blue-600 focus:outline-none touch-manipulation transition-colors\",\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: \"2\",\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-white border-t border-gray-200 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2 border-b border-gray-100 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 capitalize\",\n                                        children: user.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this),\n                            user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/book\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDCF1 Book a Ride\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 19\n                            }, this) : user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/driver/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDE97 Driver Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"⚙️ Admin Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    signOut();\n                                    setIsMenuOpen(false);\n                                },\n                                className: \"block w-full text-left px-3 py-3 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                children: \"\\uD83D\\uDEAA Sign Out\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\uD83D\\uDD11 Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: \"block px-3 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors touch-manipulation\",\n                                style: {\n                                    touchAction: \"manipulation\"\n                                },\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"✨ Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"1HJpKgE8Q5BRrDJDJ3D6t162MHQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n"));

/***/ }),

/***/ "./src/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./src/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationBell; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction NotificationBell() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!user) return;\n        // Listen for user notifications\n        // Note: We're not using orderBy to avoid Firestore index issues\n        const notificationsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\"), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.where)(\"userId\", \"==\", user.id));\n        const unsubscribe = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.onSnapshot)(notificationsQuery, (snapshot)=>{\n            const userNotifications = [];\n            let count = 0;\n            snapshot.forEach((doc)=>{\n                var _data_createdAt;\n                const data = doc.data();\n                const notification = {\n                    id: doc.id,\n                    ...data,\n                    createdAt: ((_data_createdAt = data.createdAt) === null || _data_createdAt === void 0 ? void 0 : _data_createdAt.toDate()) || new Date()\n                };\n                userNotifications.push(notification);\n                if (!notification.read) {\n                    count++;\n                }\n            });\n            // Sort notifications by createdAt in descending order (newest first)\n            userNotifications.sort((a, b)=>{\n                return b.createdAt.getTime() - a.createdAt.getTime();\n            });\n            setNotifications(userNotifications);\n            setUnreadCount(count);\n        });\n        return ()=>unsubscribe();\n    }, [\n        user\n    ]);\n    const markAsRead = async (notificationId)=>{\n        if (!user) return;\n        try {\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notificationId), {\n                read: true\n            });\n        } catch (error) {\n            console.error(\"Error marking notification as read:\", error);\n        }\n    };\n    const markAllAsRead = async ()=>{\n        if (!user) return;\n        try {\n            const promises = notifications.filter((notification)=>!notification.read).map((notification)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.db, \"notifications\", notification.id), {\n                    read: true\n                }));\n            await Promise.all(promises);\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n        }\n    };\n    const toggleDropdown = ()=>{\n        setShowDropdown(!showDropdown);\n    };\n    const getNotificationTypeStyles = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 text-blue-800\";\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleDropdown,\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none\",\n                \"aria-label\": \"Notifications\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full\",\n                        children: unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-100 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: markAllAsRead,\n                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 text-sm text-gray-500\",\n                                children: \"No notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this) : notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-3 border-b border-gray-100 \".concat(!notification.read ? \"bg-blue-50\" : \"\"),\n                                    onClick: ()=>markAsRead(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 rounded-full p-1 \".concat(getNotificationTypeStyles(notification.type)),\n                                                children: [\n                                                    notification.type === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"warning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    notification.type === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 w-0 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-900 whitespace-pre-line\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-500\",\n                                                        children: new Date(notification.createdAt).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    notification.driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 text-gray-600\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-semibold\",\n                                                                            children: notification.driverDetails.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                notification.driverDetails.vehicleColor,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleMake,\n                                                                                \" \",\n                                                                                notification.driverDetails.vehicleModel\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium\",\n                                                                            children: [\n                                                                                \"License: \",\n                                                                                notification.driverDetails.licensePlate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationBell, \"H72KrDXf27US4HLmmm+hWQjccvU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/NotificationBell.tsx\n"));

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Home = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [isNavigating, setIsNavigating] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Show welcome notification when the page loads\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (user) {\n            showNotification(\"Welcome back, \".concat(user.fullName, \"!\"), \"info\", 3000); // Auto-dismiss after 3 seconds\n        }\n    }, [\n        user,\n        showNotification\n    ]);\n    // We'll let the AuthContext handle the automatic redirection\n    // Function to navigate to the driver dashboard\n    const goToDriverDashboard = ()=>{\n        setIsNavigating(true);\n        showNotification(\"Opening Driver Dashboard...\", \"info\", 2000);\n        // Use both methods to ensure navigation works\n        window.location.href = \"/driver/dashboard\"; // Direct browser navigation as fallback\n        router.push(\"/driver/dashboard\");\n    };\n    // Function to navigate to the book a ride page\n    const goToBookRide = ()=>{\n        setIsNavigating(true);\n        showNotification(\"Opening Book a Ride...\", \"info\", 2000);\n        // Use both methods to ensure navigation works\n        window.location.href = \"/book\"; // Direct browser navigation as fallback\n        router.push(\"/book\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"BaroRide - Book Your Ride\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo.svg\",\n                                    alt: \"BaroRide Logo\",\n                                    className: \"h-32 w-auto sm:h-40 md:h-48\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4\",\n                                children: \"Welcome to BaroRide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl sm:text-2xl text-gray-600 mb-8\",\n                                children: \"Your reliable ride-sharing service in Gambela, Ethiopia\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined),\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: user.role === \"driver\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goToDriverDashboard,\n                            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation\",\n                            style: {\n                                touchAction: \"manipulation\"\n                            },\n                            children: \"Open Driver Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 17\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: goToBookRide,\n                            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 touch-manipulation\",\n                            style: {\n                                touchAction: \"manipulation\"\n                            },\n                            children: \"Book a Ride Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow-md rounded-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6 text-center\",\n                                children: \"Book your ride with fixed fares and professional drivers.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined),\n                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: user.role === \"rider\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation \".concat(isNavigating ? \"opacity-75 cursor-wait\" : \"\"),\n                                    onClick: goToBookRide,\n                                    disabled: isNavigating,\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: isNavigating ? \"Opening Booking...\" : \"Book a Ride\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-blue-600 text-white px-8 py-4 rounded-md hover:bg-blue-700 inline-block shadow-lg transition-all duration-200 transform hover:scale-105 font-medium text-lg touch-manipulation \".concat(isNavigating ? \"opacity-75 cursor-wait\" : \"\"),\n                                    onClick: goToDriverDashboard,\n                                    disabled: isNavigating,\n                                    style: {\n                                        touchAction: \"manipulation\"\n                                    },\n                                    children: isNavigating ? \"Opening Dashboard...\" : \"Go to Driver Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/login\",\n                                        className: \"bg-gray-200 text-gray-800 px-6 py-3 rounded-md hover:bg-gray-300 inline-block\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signup\",\n                                        className: \"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 inline-block\",\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Fixed Fares\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"Know exactly what you'll pay before booking your ride.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Professional Drivers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"All our drivers are vetted and professionally trained.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white shadow-md rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-3\",\n                                        children: \"Easy Booking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700\",\n                                        children: \"Book your ride in just a few clicks, anytime, anywhere.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Home, \"oE7LsV+50S1gQwoMENFN49BERCI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification,\n        next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = Home;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Home);\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Cindex.tsx&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);