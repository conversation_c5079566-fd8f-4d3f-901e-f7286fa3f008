/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/signup"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Csignup.tsx&page=%2Fsignup!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Csignup.tsx&page=%2Fsignup! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/signup\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/signup.tsx */ \"./src/pages/signup.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/signup\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNidXJhayU1Q0Rlc2t0b3AlNUNiYXJvJTIwcmlkZSU1Q3R0dCU1Q05ldyUyMGZvbGRlciU1Q3NyYyU1Q3BhZ2VzJTVDc2lnbnVwLnRzeCZwYWdlPSUyRnNpZ251cCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxzREFBd0I7QUFDL0M7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2YwN2QiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9zaWdudXBcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9zaWdudXAudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9zaWdudXBcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Csignup.tsx&page=%2Fsignup!\n"));

/***/ }),

/***/ "./src/pages/signup.tsx":
/*!******************************!*\
  !*** ./src/pages/signup.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Signup; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _utils_auth_helpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/auth-helpers */ \"./src/utils/auth-helpers.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Signup() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fullName, setFullName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [phoneNumber, setPhoneNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"rider\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { showNotification } = (0,_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    // Driver specific fields\n    const [vehicleMake, setVehicleMake] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [vehicleModel, setVehicleModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [vehicleColor, setVehicleColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licensePlate, setLicensePlate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [baroRideIdPassword, setBaroRideIdPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBaroRidePassword, setShowBaroRidePassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // The correct BaroRide ID password for driver verification\n    const CORRECT_BARO_RIDE_PASSWORD = \"244117\";\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            // Validate fields\n            if (!email || !password || !fullName || !phoneNumber) {\n                throw new Error(\"Please fill in all required fields\");\n            }\n            if (role === \"driver\" && (!vehicleMake || !vehicleModel || !vehicleColor || !licensePlate)) {\n                throw new Error(\"Please fill in all vehicle details\");\n            }\n            // Validate BaroRide ID password for drivers\n            if (role === \"driver\" && baroRideIdPassword !== CORRECT_BARO_RIDE_PASSWORD) {\n                throw new Error(\"Invalid BaroRide ID password. Please contact support if you need assistance.\");\n            }\n            // Create user in Firebase Auth\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.auth, email, password);\n            const uid = userCredential.user.uid;\n            // Normalize phone number for consistent storage\n            const normalizedPhoneNumber = (0,_utils_auth_helpers__WEBPACK_IMPORTED_MODULE_8__.normalizePhoneNumber)(phoneNumber);\n            // Create base user data\n            const userData = {\n                id: uid,\n                email,\n                phoneNumber: normalizedPhoneNumber,\n                fullName,\n                role,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            // Add role-specific data\n            if (role === \"rider\") {\n                const riderData = {\n                    ...userData,\n                    role: \"rider\",\n                    bookingHistory: []\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", uid), riderData);\n            } else {\n                const driverData = {\n                    ...userData,\n                    role: \"driver\",\n                    isOnline: false,\n                    isVerified: true,\n                    vehicleDetails: {\n                        make: vehicleMake,\n                        model: vehicleModel,\n                        color: vehicleColor,\n                        licensePlate\n                    },\n                    rating: 5.0,\n                    completedRides: 0\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.db, \"users\", uid), driverData);\n            }\n            // Show success notification that will auto-dismiss after 3 seconds\n            showNotification(\"Account created successfully! Redirecting...\", \"success\", 3000);\n            // Redirect based on user role\n            if (role === \"driver\") {\n                // If user is a driver, redirect to driver dashboard\n                router.push(\"/driver/dashboard\");\n            } else {\n                // If user is a rider, redirect to home page\n                router.push(\"/\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to create account. Please try again.\";\n            setError(errorMessage);\n            showNotification(errorMessage, \"error\", 3000);\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 safe-area-top safe-area-bottom\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_6___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BaroRide - Sign Up\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Create your BaroRide account\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 pt-8 pb-4 px-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo-icon.svg\",\n                                    alt: \"BaroRide Logo\",\n                                    className: \"h-16 w-16 sm:h-20 sm:w-20\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl sm:text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Join BaroRide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm sm:text-base text-gray-600\",\n                                children: \"Create your account to get started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-start justify-center px-4 pb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-sm\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 text-sm\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                    placeholder: \"Enter your email address\",\n                                                    value: email,\n                                                    onChange: (e)=>setEmail(e.target.value),\n                                                    autoCapitalize: \"none\",\n                                                    autoCorrect: \"off\",\n                                                    spellCheck: \"false\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"password\",\n                                                            name: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            autoComplete: \"new-password\",\n                                                            required: true,\n                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                            placeholder: \"Enter your password\",\n                                                            value: password,\n                                                            onChange: (e)=>setPassword(e.target.value),\n                                                            autoCapitalize: \"none\",\n                                                            autoCorrect: \"off\",\n                                                            spellCheck: \"false\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            \"aria-label\": showPassword ? \"Hide password\" : \"Show password\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-6 w-6\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-6 w-6\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"fullName\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Full Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"fullName\",\n                                                    name: \"fullName\",\n                                                    type: \"text\",\n                                                    autoComplete: \"name\",\n                                                    required: true,\n                                                    className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                    placeholder: \"Enter your full name\",\n                                                    value: fullName,\n                                                    onChange: (e)=>setFullName(e.target.value),\n                                                    autoCapitalize: \"words\",\n                                                    autoCorrect: \"off\",\n                                                    spellCheck: \"false\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"phoneNumber\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Phone Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"phoneNumber\",\n                                                    name: \"phoneNumber\",\n                                                    type: \"tel\",\n                                                    autoComplete: \"tel\",\n                                                    required: true,\n                                                    className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                    placeholder: \"Enter your phone number\",\n                                                    value: phoneNumber,\n                                                    onChange: (e)=>setPhoneNumber(e.target.value),\n                                                    autoCapitalize: \"none\",\n                                                    autoCorrect: \"off\",\n                                                    spellCheck: \"false\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                    children: \"I want to:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"rider\",\n                                                                    name: \"role\",\n                                                                    type: \"radio\",\n                                                                    checked: role === \"rider\",\n                                                                    onChange: ()=>setRole(\"rider\"),\n                                                                    className: \"focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"rider\",\n                                                                    className: \"ml-3 block text-base text-gray-900 cursor-pointer flex-1\",\n                                                                    children: \"Book rides (Rider)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center p-3 border-2 border-gray-300 rounded-lg hover:border-blue-500 transition-colors touch-target\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"driver\",\n                                                                    name: \"role\",\n                                                                    type: \"radio\",\n                                                                    checked: role === \"driver\",\n                                                                    onChange: ()=>setRole(\"driver\"),\n                                                                    className: \"focus:ring-blue-500 h-5 w-5 text-blue-600 border-gray-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"driver\",\n                                                                    className: \"ml-3 block text-base text-gray-900 cursor-pointer flex-1\",\n                                                                    children: \"Drive (Driver)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        role === \"driver\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 border-t-2 border-gray-200 pt-6 mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Vehicle Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"vehicleMake\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Vehicle Make\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"vehicleMake\",\n                                                            name: \"vehicleMake\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                            placeholder: \"e.g., Toyota, Honda, Ford\",\n                                                            value: vehicleMake,\n                                                            onChange: (e)=>setVehicleMake(e.target.value),\n                                                            autoCapitalize: \"words\",\n                                                            autoCorrect: \"off\",\n                                                            spellCheck: \"false\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"vehicleModel\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Vehicle Model\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"vehicleModel\",\n                                                            name: \"vehicleModel\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                            placeholder: \"e.g., Camry, Civic, Focus\",\n                                                            value: vehicleModel,\n                                                            onChange: (e)=>setVehicleModel(e.target.value),\n                                                            autoCapitalize: \"words\",\n                                                            autoCorrect: \"off\",\n                                                            spellCheck: \"false\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"vehicleColor\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Vehicle Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"vehicleColor\",\n                                                            name: \"vehicleColor\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                            placeholder: \"e.g., White, Black, Silver\",\n                                                            value: vehicleColor,\n                                                            onChange: (e)=>setVehicleColor(e.target.value),\n                                                            autoCapitalize: \"words\",\n                                                            autoCorrect: \"off\",\n                                                            spellCheck: \"false\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"licensePlate\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"License Plate Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"licensePlate\",\n                                                            name: \"licensePlate\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                            placeholder: \"Enter license plate number\",\n                                                            value: licensePlate,\n                                                            onChange: (e)=>setLicensePlate(e.target.value),\n                                                            autoCapitalize: \"characters\",\n                                                            autoCorrect: \"off\",\n                                                            spellCheck: \"false\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t-2 border-gray-200 pt-6 mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: \"Driver Verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"baroRideIdPassword\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"BaroRide ID Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"baroRideIdPassword\",\n                                                                            name: \"baroRideIdPassword\",\n                                                                            type: showBaroRidePassword ? \"text\" : \"password\",\n                                                                            required: true,\n                                                                            className: \"mobile-input w-full text-base border-2 border-gray-300 rounded-lg px-4 py-3 pr-12 focus:border-blue-500 focus:ring-0 transition-colors\",\n                                                                            placeholder: \"Enter BaroRide ID Password\",\n                                                                            value: baroRideIdPassword,\n                                                                            onChange: (e)=>setBaroRideIdPassword(e.target.value),\n                                                                            autoCapitalize: \"none\",\n                                                                            autoCorrect: \"off\",\n                                                                            spellCheck: \"false\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800 focus:outline-none touch-target\",\n                                                                            onClick: ()=>setShowBaroRidePassword(!showBaroRidePassword),\n                                                                            \"aria-label\": showBaroRidePassword ? \"Hide password\" : \"Show password\",\n                                                                            children: showBaroRidePassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                className: \"h-6 w-6\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                fill: \"currentColor\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                        lineNumber: 392,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 29\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                className: \"h-6 w-6\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                fill: \"currentColor\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                        lineNumber: 397,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                        lineNumber: 398,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                                    children: \"This password is required for all driver registrations. Contact support if you don't have it.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isLoading,\n                                            className: \"mobile-button w-full py-4 px-6 rounded-lg font-semibold text-base transition-all duration-200 touch-target \".concat(isLoading ? \"bg-blue-400 cursor-not-allowed\" : \"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl\", \" text-white flex items-center justify-center mt-8\"),\n                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mobile-spinner mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Creating Account...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Create Account\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/login\",\n                                                className: \"text-blue-600 hover:text-blue-700 font-semibold transition-colors touch-target\",\n                                                children: \"Sign in here\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\signup.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(Signup, \"F2wy/639N0Py5/P96RuepFeot1E=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_7__.useNotification\n    ];\n});\n_c = Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/signup.tsx\n"));

/***/ }),

/***/ "./src/utils/auth-helpers.ts":
/*!***********************************!*\
  !*** ./src/utils/auth-helpers.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: function() { return /* binding */ authenticateUser; },\n/* harmony export */   findUserByPhoneNumber: function() { return /* binding */ findUserByPhoneNumber; },\n/* harmony export */   isEmail: function() { return /* binding */ isEmail; },\n/* harmony export */   isPhoneNumber: function() { return /* binding */ isPhoneNumber; },\n/* harmony export */   normalizePhoneNumber: function() { return /* binding */ normalizePhoneNumber; }\n/* harmony export */ });\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n\n\n\n/**\n * Validates if the input is an email address\n * @param input The string to check\n * @returns True if the input is a valid email address\n */ const isEmail = (input)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(input);\n};\n/**\n * Normalizes a phone number by removing all non-digit characters except the leading +\n * @param phoneNumber The phone number to normalize\n * @returns Normalized phone number\n */ const normalizePhoneNumber = (phoneNumber)=>{\n    // Remove all non-digit characters except the leading +\n    let normalized = phoneNumber.replace(/[^\\d+]/g, \"\");\n    // If it starts with +, keep it, otherwise remove any + in the middle\n    if (normalized.startsWith(\"+\")) {\n        normalized = \"+\" + normalized.substring(1).replace(/\\+/g, \"\");\n    } else {\n        normalized = normalized.replace(/\\+/g, \"\");\n    }\n    return normalized;\n};\n/**\n * Validates if the input is a phone number\n * @param input The string to check\n * @returns True if the input is a valid phone number\n */ const isPhoneNumber = (input)=>{\n    // Normalize the input first\n    const normalized = normalizePhoneNumber(input);\n    // More comprehensive phone number validation\n    // Supports international format (+country code) and local formats\n    const phoneRegex = /^(\\+\\d{1,3})?\\d{7,15}$/;\n    // Also check for common formats without normalization\n    const commonFormats = [\n        /^[+]?[(]?[0-9]{3}[)]?[-\\s.]?[0-9]{3}[-\\s.]?[0-9]{4,6}$/,\n        /^[+]?[0-9]{1,4}[-\\s.]?[0-9]{3,4}[-\\s.]?[0-9]{3,4}[-\\s.]?[0-9]{3,4}$/,\n        /^[0-9]{10,15}$/,\n        /^\\+[0-9]{7,15}$/ // International with +\n    ];\n    return phoneRegex.test(normalized) || commonFormats.some((regex)=>regex.test(input));\n};\n/**\n * Finds a user by phone number\n * @param phoneNumber The phone number to search for\n * @returns The user document if found, null otherwise\n */ const findUserByPhoneNumber = async (phoneNumber)=>{\n    try {\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.db, \"users\");\n        // First, try to find with the exact phone number as entered\n        let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"phoneNumber\", \"==\", phoneNumber));\n        let querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n        if (!querySnapshot.empty) {\n            const userDoc = querySnapshot.docs[0];\n            return {\n                id: userDoc.id,\n                ...userDoc.data()\n            };\n        }\n        // If not found, try with normalized phone number\n        const normalizedInput = normalizePhoneNumber(phoneNumber);\n        // Get all users and check for phone number matches with normalization\n        const allUsersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usersRef);\n        const allUsersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(allUsersQuery);\n        for (const userDoc of allUsersSnapshot.docs){\n            const userData = userDoc.data();\n            if (userData.phoneNumber) {\n                const normalizedStored = normalizePhoneNumber(userData.phoneNumber);\n                if (normalizedStored === normalizedInput) {\n                    return {\n                        id: userDoc.id,\n                        ...userData\n                    };\n                }\n            }\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error finding user by phone number:\", error);\n        return null;\n    }\n};\n/**\n * Authenticates a user with either email or phone number\n * @param identifier Email or phone number\n * @param password User password\n * @returns User data if authentication is successful\n */ const authenticateUser = async (identifier, password)=>{\n    try {\n        let userCredential;\n        let userData;\n        if (isEmail(identifier)) {\n            // If the identifier is an email, use Firebase's email authentication\n            userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.signInWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.auth, identifier, password);\n            // Get user data from Firestore\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.db, \"users\", userCredential.user.uid));\n            userData = {\n                id: userDoc.id,\n                ...userDoc.data()\n            };\n        } else if (isPhoneNumber(identifier)) {\n            // If the identifier is a phone number, find the user first\n            const user = await findUserByPhoneNumber(identifier);\n            if (!user) {\n                throw new Error(\"User not found with this phone number\");\n            }\n            // Then authenticate with the associated email\n            userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.signInWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.auth, user.email, password);\n            userData = user;\n        } else {\n            throw new Error(\"Invalid identifier format. Please enter a valid email or phone number.\");\n        }\n        return userData;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth-helpers.ts\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cburak%5CDesktop%5Cbaro%20ride%5Cttt%5CNew%20folder%5Csrc%5Cpages%5Csignup.tsx&page=%2Fsignup!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);