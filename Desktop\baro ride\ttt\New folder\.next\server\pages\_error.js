/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Notification.tsx":
/*!*****************************************!*\
  !*** ./src/components/Notification.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Notification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Notification({ message, type = \"info\", duration = 5000, onClose, driverDetails }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setIsVisible(false);\n            if (onClose) onClose();\n        }, duration);\n        return ()=>clearTimeout(timer);\n    }, [\n        duration,\n        onClose\n    ]);\n    if (!isVisible) return null;\n    const getTypeStyles = ()=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-100 border-green-500 text-green-700\";\n            case \"warning\":\n                return \"bg-yellow-100 border-yellow-500 text-yellow-700\";\n            case \"error\":\n                return \"bg-red-100 border-red-500 text-red-700\";\n            case \"info\":\n            default:\n                return \"bg-blue-100 border-blue-500 text-blue-700\";\n        }\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this);\n            case \"info\":\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 max-w-md shadow-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `p-4 mb-4 text-sm rounded-lg border ${getTypeStyles()}`,\n            role: \"alert\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mr-2\",\n                            children: getIcon()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium flex-grow whitespace-pre-line\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500\",\n                            onClick: ()=>{\n                                setIsVisible(false);\n                                if (onClose) onClose();\n                            },\n                            \"aria-label\": \"Close\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                driverDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-gray-600\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold\",\n                                        children: driverDetails.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            driverDetails.vehicleColor,\n                                            \" \",\n                                            driverDetails.vehicleMake,\n                                            \" \",\n                                            driverDetails.vehicleModel\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs font-medium\",\n                                        children: [\n                                            \"License Plate: \",\n                                            driverDetails.licensePlate\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Phone: \",\n                                            driverDetails.phoneNumber\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\components\\\\Notification.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Notification.tsx\n");

/***/ }),

/***/ "./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/firebase/config */ \"./src/firebase/config.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__]);\n([_firebase_config__WEBPACK_IMPORTED_MODULE_2__, firebase_auth__WEBPACK_IMPORTED_MODULE_3__, firebase_firestore__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.onAuthStateChanged)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth, async (firebaseUser)=>{\n            if (firebaseUser) {\n                const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_4__.doc)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.db, \"users\", firebaseUser.uid));\n                const userData = userDoc.data();\n                setUser(userData);\n                setLoading(false);\n                // Auto-redirect users based on role if they're on the homepage\n                if (router.pathname === \"/\") {\n                    if (userData.role === \"admin\") {\n                        console.log(\"Admin detected on homepage, redirecting to admin dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/admin/dashboard\");\n                        }, 100);\n                    } else if (userData.role === \"driver\") {\n                        console.log(\"Driver detected on homepage, redirecting to driver dashboard...\");\n                        // Use a small timeout to ensure the redirect happens after the component is fully mounted\n                        setTimeout(()=>{\n                            router.push(\"/driver/dashboard\");\n                        }, 100);\n                    }\n                }\n            } else {\n                setUser(null);\n                setLoading(false);\n            }\n        });\n        return unsubscribe;\n    }, [\n        router\n    ]);\n    const signOut = async ()=>{\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth);\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1RTtBQUMxQjtBQUNrQztBQUM5QjtBQUNUO0FBU3hDLE1BQU1ZLDRCQUFjWixvREFBYUEsQ0FBa0I7SUFDakRhLE1BQU07SUFDTkMsU0FBUztJQUNUUCxTQUFTLFdBQWE7QUFDeEI7QUFFTyxTQUFTUSxhQUFhLEVBQUVDLFFBQVEsRUFBaUM7SUFDdEUsTUFBTSxDQUFDSCxNQUFNSSxRQUFRLEdBQUdkLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1csU0FBU0ksV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNZ0IsU0FBU1Isc0RBQVNBO0lBRXhCVCxnREFBU0EsQ0FBQztRQUNSLE1BQU1rQixjQUFjZCxpRUFBa0JBLENBQUNGLGtEQUFJQSxFQUFFLE9BQU9pQjtZQUNsRCxJQUFJQSxjQUFjO2dCQUNoQixNQUFNQyxVQUFVLE1BQU1aLDBEQUFNQSxDQUFDRCx1REFBR0EsQ0FBQ0osZ0RBQUVBLEVBQUUsU0FBU2dCLGFBQWFFLEdBQUc7Z0JBQzlELE1BQU1DLFdBQVdGLFFBQVFHLElBQUk7Z0JBQzdCUixRQUFRTztnQkFDUk4sV0FBVztnQkFFWCwrREFBK0Q7Z0JBQy9ELElBQUlDLE9BQU9PLFFBQVEsS0FBSyxLQUFLO29CQUMzQixJQUFJRixTQUFTRyxJQUFJLEtBQUssU0FBUzt3QkFDN0JDLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWiwwRkFBMEY7d0JBQzFGQyxXQUFXOzRCQUNUWCxPQUFPWSxJQUFJLENBQUM7d0JBQ2QsR0FBRztvQkFDTCxPQUFPLElBQUlQLFNBQVNHLElBQUksS0FBSyxVQUFVO3dCQUNyQ0MsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLDBGQUEwRjt3QkFDMUZDLFdBQVc7NEJBQ1RYLE9BQU9ZLElBQUksQ0FBQzt3QkFDZCxHQUFHO29CQUNMO2dCQUNGO1lBQ0YsT0FBTztnQkFDTGQsUUFBUTtnQkFDUkMsV0FBVztZQUNiO1FBQ0Y7UUFFQSxPQUFPRTtJQUNULEdBQUc7UUFBQ0Q7S0FBTztJQUVYLE1BQU1aLFVBQVU7UUFDZCxNQUFNQyxzREFBZUEsQ0FBQ0osa0RBQUlBO1FBQzFCYSxRQUFRO0lBQ1Y7SUFFQSxxQkFDRSw4REFBQ0wsWUFBWW9CLFFBQVE7UUFBQ0MsT0FBTztZQUFFcEI7WUFBTUM7WUFBU1A7UUFBUTtrQkFDbkRTOzs7Ozs7QUFHUDtBQUVPLE1BQU1rQixVQUFVLElBQU1qQyxpREFBVUEsQ0FBQ1csYUFBYSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpcnBvcnQtcmlkZS1ib29raW5nLy4vc3JjL2NvbnRleHRzL0F1dGhDb250ZXh0LnRzeD8xZmEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhdXRoLCBkYiB9IGZyb20gJ0AvZmlyZWJhc2UvY29uZmlnJztcbmltcG9ydCB7IG9uQXV0aFN0YXRlQ2hhbmdlZCwgc2lnbk91dCBhcyBmaXJlYmFzZVNpZ25PdXQgfSBmcm9tICdmaXJlYmFzZS9hdXRoJztcbmltcG9ydCB7IGRvYywgZ2V0RG9jIH0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvcm91dGVyJztcbmltcG9ydCB0eXBlIHsgVXNlciB9IGZyb20gJ0AvdHlwZXMvdXNlcic7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgc2lnbk91dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZT4oe1xuICB1c2VyOiBudWxsLFxuICBsb2FkaW5nOiB0cnVlLFxuICBzaWduT3V0OiBhc3luYyAoKSA9PiB7fSxcbn0pO1xuXG5leHBvcnQgZnVuY3Rpb24gQXV0aFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IG9uQXV0aFN0YXRlQ2hhbmdlZChhdXRoLCBhc3luYyAoZmlyZWJhc2VVc2VyKSA9PiB7XG4gICAgICBpZiAoZmlyZWJhc2VVc2VyKSB7XG4gICAgICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCAndXNlcnMnLCBmaXJlYmFzZVVzZXIudWlkKSk7XG4gICAgICAgIGNvbnN0IHVzZXJEYXRhID0gdXNlckRvYy5kYXRhKCkgYXMgVXNlcjtcbiAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuXG4gICAgICAgIC8vIEF1dG8tcmVkaXJlY3QgdXNlcnMgYmFzZWQgb24gcm9sZSBpZiB0aGV5J3JlIG9uIHRoZSBob21lcGFnZVxuICAgICAgICBpZiAocm91dGVyLnBhdGhuYW1lID09PSAnLycpIHtcbiAgICAgICAgICBpZiAodXNlckRhdGEucm9sZSA9PT0gJ2FkbWluJykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ0FkbWluIGRldGVjdGVkIG9uIGhvbWVwYWdlLCByZWRpcmVjdGluZyB0byBhZG1pbiBkYXNoYm9hcmQuLi4nKTtcbiAgICAgICAgICAgIC8vIFVzZSBhIHNtYWxsIHRpbWVvdXQgdG8gZW5zdXJlIHRoZSByZWRpcmVjdCBoYXBwZW5zIGFmdGVyIHRoZSBjb21wb25lbnQgaXMgZnVsbHkgbW91bnRlZFxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vZGFzaGJvYXJkJyk7XG4gICAgICAgICAgICB9LCAxMDApO1xuICAgICAgICAgIH0gZWxzZSBpZiAodXNlckRhdGEucm9sZSA9PT0gJ2RyaXZlcicpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdEcml2ZXIgZGV0ZWN0ZWQgb24gaG9tZXBhZ2UsIHJlZGlyZWN0aW5nIHRvIGRyaXZlciBkYXNoYm9hcmQuLi4nKTtcbiAgICAgICAgICAgIC8vIFVzZSBhIHNtYWxsIHRpbWVvdXQgdG8gZW5zdXJlIHRoZSByZWRpcmVjdCBoYXBwZW5zIGFmdGVyIHRoZSBjb21wb25lbnQgaXMgZnVsbHkgbW91bnRlZFxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvZHJpdmVyL2Rhc2hib2FyZCcpO1xuICAgICAgICAgICAgfSwgMTAwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIHVuc3Vic2NyaWJlO1xuICB9LCBbcm91dGVyXSk7XG5cbiAgY29uc3Qgc2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICBhd2FpdCBmaXJlYmFzZVNpZ25PdXQoYXV0aCk7XG4gICAgc2V0VXNlcihudWxsKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyB1c2VyLCBsb2FkaW5nLCBzaWduT3V0IH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7Il0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJhdXRoIiwiZGIiLCJvbkF1dGhTdGF0ZUNoYW5nZWQiLCJzaWduT3V0IiwiZmlyZWJhc2VTaWduT3V0IiwiZG9jIiwiZ2V0RG9jIiwidXNlUm91dGVyIiwiQXV0aENvbnRleHQiLCJ1c2VyIiwibG9hZGluZyIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwic2V0VXNlciIsInNldExvYWRpbmciLCJyb3V0ZXIiLCJ1bnN1YnNjcmliZSIsImZpcmViYXNlVXNlciIsInVzZXJEb2MiLCJ1aWQiLCJ1c2VyRGF0YSIsImRhdGEiLCJwYXRobmFtZSIsInJvbGUiLCJjb25zb2xlIiwibG9nIiwic2V0VGltZW91dCIsInB1c2giLCJQcm92aWRlciIsInZhbHVlIiwidXNlQXV0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Notification */ \"./src/components/Notification.tsx\");\n\n\n\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    showNotification: ()=>{}\n});\nfunction NotificationProvider({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showNotification = (message, type = \"info\", duration = 5000, driverDetails)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type,\n                    duration,\n                    driverDetails\n                }\n            ]);\n    };\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: {\n            showNotification\n        },\n        children: [\n            children,\n            notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Notification__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    message: notification.message,\n                    type: notification.type,\n                    duration: notification.duration,\n                    driverDetails: notification.driverDetails,\n                    onClose: ()=>removeNotification(notification.id)\n                }, notification.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nconst useNotification = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "./src/contexts/RBACContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/RBACContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RBACProvider: () => (/* binding */ RBACProvider),\n/* harmony export */   useRBAC: () => (/* binding */ useRBAC)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _NotificationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n// Create the context with default values\nconst RBACContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    hasAccess: ()=>false,\n    checkAccess: ()=>false,\n    userRole: \"guest\",\n    isAdmin: false,\n    isDriver: false,\n    isRider: false\n});\n// Define route access permissions\nconst routeAccess = {\n    \"/\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/login\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/signup\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/forgot-password\": [\n        \"admin\",\n        \"driver\",\n        \"rider\",\n        \"guest\"\n    ],\n    \"/book\": [\n        \"admin\",\n        \"rider\"\n    ],\n    \"/driver\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/driver/dashboard\": [\n        \"admin\",\n        \"driver\"\n    ],\n    \"/admin\": [\n        \"admin\"\n    ],\n    \"/admin/dashboard\": [\n        \"admin\"\n    ],\n    \"/admin/users\": [\n        \"admin\"\n    ],\n    \"/admin/bookings\": [\n        \"admin\"\n    ]\n};\n// RBAC Provider component\nfunction RBACProvider({ children }) {\n    const { user, loading } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showNotification } = (0,_NotificationContext__WEBPACK_IMPORTED_MODULE_4__.useNotification)();\n    const [authorized, setAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Determine user role\n    const userRole = user ? user.role === \"admin\" ? \"admin\" : user.role === \"driver\" ? \"driver\" : \"rider\" : \"guest\";\n    // Check if user has access to a specific route\n    const hasAccess = (route)=>{\n        // Check if the route exists in our access map\n        const allowedRoles = routeAccess[route];\n        // If route is not defined in our access map, default to admin-only\n        if (!allowedRoles) {\n            return userRole === \"admin\";\n        }\n        // Check if user's role is in the allowed roles for this route\n        return allowedRoles.includes(userRole);\n    };\n    // Check if user has one of the required roles\n    const checkAccess = (requiredRoles)=>{\n        return requiredRoles.includes(userRole);\n    };\n    // Role-specific boolean flags for easier checks\n    const isAdmin = userRole === \"admin\";\n    const isDriver = userRole === \"driver\";\n    const isRider = userRole === \"rider\";\n    // Route protection effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Skip during initial loading\n        if (loading) return;\n        // Get the current path\n        const path = router.pathname;\n        // Check if user has access to the current route\n        const hasRouteAccess = hasAccess(path);\n        if (!hasRouteAccess) {\n            // Redirect to appropriate page based on role\n            if (userRole === \"guest\") {\n                router.push(\"/login\");\n                showNotification(\"Please log in to access this page\", \"warning\");\n            } else if (userRole === \"driver\") {\n                router.push(\"/driver/dashboard\");\n                showNotification(\"Access denied. Redirected to driver dashboard.\", \"warning\");\n            } else if (userRole === \"rider\") {\n                router.push(\"/\");\n                showNotification(\"Access denied. Redirected to home page.\", \"warning\");\n            } else {\n                router.push(\"/\");\n                showNotification(\"Access denied. Please contact support if you believe this is an error.\", \"error\");\n            }\n        } else {\n            setAuthorized(true);\n        }\n    }, [\n        router.pathname,\n        userRole,\n        loading\n    ]);\n    // Provide the context value\n    const contextValue = {\n        hasAccess,\n        checkAccess,\n        userRole,\n        isAdmin,\n        isDriver,\n        isRider\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RBACContext.Provider, {\n        value: contextValue,\n        children: authorized ? children : null\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\contexts\\\\RBACContext.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the RBAC context\nconst useRBAC = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RBACContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/RBACContext.tsx\n");

/***/ }),

/***/ "./src/firebase/config.ts":
/*!********************************!*\
  !*** ./src/firebase/config.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"firebase/storage\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__, firebase_storage__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po\",\n    authDomain: \"baroride.firebaseapp.com\",\n    projectId: \"baroride\",\n    storageBucket: \"baroride.firebasestorage.app\",\n    messagingSenderId: \"191771619835\",\n    appId: \"1:191771619835:web:2fc57d131cf64a35e2db5e\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Configure Firestore for better performance in production\nif (false) {}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/firebase/config.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/RBACContext */ \"./src/contexts/RBACContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__, _contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_RBACContext__WEBPACK_IMPORTED_MODULE_4__.RBACProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQThCO0FBRXdCO0FBQ2dCO0FBQ2hCO0FBRXRELFNBQVNHLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDL0MscUJBQ0UsOERBQUNMLCtEQUFZQTtrQkFDWCw0RUFBQ0MsK0VBQW9CQTtzQkFDbkIsNEVBQUNDLCtEQUFZQTswQkFDWCw0RUFBQ0U7b0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2xDO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haXJwb3J0LXJpZGUtYm9va2luZy8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnQC9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgTm90aWZpY2F0aW9uUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL05vdGlmaWNhdGlvbkNvbnRleHQnO1xuaW1wb3J0IHsgUkJBQ1Byb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9SQkFDQ29udGV4dCc7XG5cbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPE5vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgICAgICA8UkJBQ1Byb3ZpZGVyPlxuICAgICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgICAgPC9SQkFDUHJvdmlkZXI+XG4gICAgICA8L05vdGlmaWNhdGlvblByb3ZpZGVyPlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcDtcbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJOb3RpZmljYXRpb25Qcm92aWRlciIsIlJCQUNQcm92aWRlciIsIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"BaroRide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-navbutton-color\",\n                        content: \"#1e3a5f\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baro ride\\\\ttt\\\\New folder\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FFSCw4REFBQ0s7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FHOUIsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUF5QkMsU0FBUTs7Ozs7O2tDQUM1Qyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQStCQyxTQUFROzs7Ozs7a0NBQ2xELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBd0NDLFNBQVE7Ozs7OztrQ0FDM0QsOERBQUNGO3dCQUFLQyxNQUFLO3dCQUE2QkMsU0FBUTs7Ozs7O2tDQUdoRCw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQW1CQyxTQUFROzs7Ozs7a0NBR3RDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQWdDQyxTQUFROzs7Ozs7a0NBQ25ELDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBd0NDLFNBQVE7Ozs7OztrQ0FHM0QsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFhQyxNQUFLOzs7Ozs7a0NBQzVCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzt3QkFBNEJDLGFBQVk7Ozs7Ozs7Ozs7OzswQkFFdEUsOERBQUNDO2dCQUFLQyxXQUFVOztrQ0FDZCw4REFBQ1osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpcnBvcnQtcmlkZS1ib29raW5nLy4vc3JjL3BhZ2VzL19kb2N1bWVudC50c3g/MTg4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxuICAgICAgPEhlYWQ+XG4gICAgICAgIHsvKiBFc3NlbnRpYWwgbW9iaWxlIHZpZXdwb3J0IG1ldGEgdGFnICovfVxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCwgbWF4aW11bS1zY2FsZT0xLjAsIHVzZXItc2NhbGFibGU9bm8sIHZpZXdwb3J0LWZpdD1jb3ZlclwiIC8+XG5cbiAgICAgICAgey8qIE1vYmlsZSB3ZWIgYXBwIGNhcGFiaWxpdGllcyAqL31cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXN0YXR1cy1iYXItc3R5bGVcIiBjb250ZW50PVwiZGVmYXVsdFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsZS1tb2JpbGUtd2ViLWFwcC10aXRsZVwiIGNvbnRlbnQ9XCJCYXJvUmlkZVwiIC8+XG5cbiAgICAgICAgey8qIFByZXZlbnQgYXV0b21hdGljIHBob25lIG51bWJlciBkZXRlY3Rpb24gKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJmb3JtYXQtZGV0ZWN0aW9uXCIgY29udGVudD1cInRlbGVwaG9uZT1ub1wiIC8+XG5cbiAgICAgICAgey8qIFRoZW1lIGNvbG9yIGZvciBtb2JpbGUgYnJvd3NlcnMgKi99XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjMWUzYTVmXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tbmF2YnV0dG9uLWNvbG9yXCIgY29udGVudD1cIiMxZTNhNWZcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJibGFjay10cmFuc2x1Y2VudFwiIC8+XG5cbiAgICAgICAgey8qIFByZWNvbm5lY3QgdG8gaW1wcm92ZSBwZXJmb3JtYW5jZSAqL31cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdzdGF0aWMuY29tXCIgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiY3Jvc3NPcmlnaW4iLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "firebase/storage":
/*!***********************************!*\
  !*** external "firebase/storage" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/storage");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();