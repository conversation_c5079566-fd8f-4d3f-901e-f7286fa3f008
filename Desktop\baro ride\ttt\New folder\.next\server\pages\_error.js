(()=>{var e={};e.id=820,e.ids=[820,888,660],e.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},6968:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>c,getServerSideProps:()=>h,getStaticPaths:()=>f,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>x});var s=r(7093),i=r(5244),a=r(1323),l=r(1070),o=r(3893),d=r(6971),u=e([o]);o=(u.then?(await u)():u)[0];let c=(0,a.l)(d,"default"),p=(0,a.l)(d,"getStaticProps"),f=(0,a.l)(d,"getStaticPaths"),h=(0,a.l)(d,"getServerSideProps"),m=(0,a.l)(d,"config"),g=(0,a.l)(d,"reportWebVitals"),x=(0,a.l)(d,"unstable_getStaticProps"),v=(0,a.l)(d,"unstable_getStaticPaths"),b=(0,a.l)(d,"unstable_getStaticParams"),y=(0,a.l)(d,"unstable_getServerProps"),j=(0,a.l)(d,"unstable_getServerSideProps"),w=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:o.default,Document:l.default},userland:d});n()}catch(e){n(e)}})},6971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(167),s=r(997),i=n._(r(6689)),a=n._(r(7828)),l={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function o(e){let{res:t,err:r}=e;return{statusCode:t&&t.statusCode?t.statusCode:r?r.statusCode:404}}let d={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class u extends i.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||l[e]||"An unexpected error has occurred";return(0,s.jsxs)("div",{style:d.error,children:[(0,s.jsx)(a.default,{children:(0,s.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,s.jsxs)("div",{style:d.desc,children:[(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,s.jsx)("h1",{className:"next-error-h1",style:d.h1,children:e}):null,(0,s.jsx)("div",{style:d.wrap,children:(0,s.jsxs)("h2",{style:d.h2,children:[this.props.title||e?r:(0,s.jsx)(s.Fragment,{children:"Application error: a client-side exception has occurred (see the browser console for more information)"}),"."]})})]})]})}}u.displayName="ErrorPage",u.getInitialProps=o,u.origGetInitialProps=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5495:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},7828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return c}});let n=r(167),s=r(8760),i=r(997),a=s._(r(6689)),l=n._(r(7215)),o=r(8039),d=r(1988),u=r(5495);function c(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(1997);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return s=>{let i=!0,a=!1;if(s.key&&"number"!=typeof s.key&&s.key.indexOf("$")>0){a=!0;let t=s.key.slice(s.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(s.type){case"title":case"base":t.has(s.type)?i=!1:t.add(s.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(s.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=s.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}}return i}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let m=function(e){let{children:t}=e,r=(0,a.useContext)(o.AmpStateContext),n=(0,a.useContext)(d.HeadManagerContext);return(0,i.jsx)(l.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,u.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7215:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(6689),s=()=>{},i=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function l(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),s(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),s(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},1997:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6317:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{H:()=>c,a:()=>f});var s=r(997),i=r(6689),a=r(3462),l=r(401),o=r(1492),d=r(1163),u=e([a,l,o]);[a,l,o]=u.then?(await u)():u;let p=(0,i.createContext)({user:null,loading:!0,signOut:async()=>{}});function c({children:e}){let[t,r]=(0,i.useState)(null),[n,o]=(0,i.useState)(!0);(0,d.useRouter)();let u=async()=>{await (0,l.signOut)(a.I),r(null)};return s.jsx(p.Provider,{value:{user:t,loading:n,signOut:u},children:e})}let f=()=>(0,i.useContext)(p);n()}catch(e){n(e)}})},1530:(e,t,r)=>{"use strict";r.d(t,{J:()=>l,l:()=>o});var n=r(997),s=r(6689);function i({message:e,type:t="info",duration:r=5e3,onClose:i,driverDetails:a}){let[l,o]=(0,s.useState)(!0);return l?n.jsx("div",{className:"fixed top-4 right-4 z-50 max-w-md shadow-lg",children:(0,n.jsxs)("div",{className:`p-4 mb-4 text-sm rounded-lg border ${(()=>{switch(t){case"success":return"bg-green-100 border-green-500 text-green-700";case"warning":return"bg-yellow-100 border-yellow-500 text-yellow-700";case"error":return"bg-red-100 border-red-500 text-red-700";default:return"bg-blue-100 border-blue-500 text-blue-700"}})()}`,role:"alert",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"mr-2",children:(()=>{switch(t){case"success":return n.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"warning":return n.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"error":return n.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});default:return n.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z",clipRule:"evenodd"})})}})()}),n.jsx("div",{className:"font-medium flex-grow whitespace-pre-line",children:e}),(0,n.jsxs)("button",{type:"button",className:"ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-opacity-25 hover:bg-gray-500",onClick:()=>{o(!1),i&&i()},"aria-label":"Close",children:[n.jsx("span",{className:"sr-only",children:"Close"}),n.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]}),a&&n.jsx("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,n.jsxs)("div",{className:"flex items-center",children:[n.jsx("div",{className:"w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-3",children:n.jsx("svg",{className:"w-6 h-6 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-semibold",children:a.fullName}),(0,n.jsxs)("p",{className:"text-xs",children:[a.vehicleColor," ",a.vehicleMake," ",a.vehicleModel]}),(0,n.jsxs)("p",{className:"text-xs font-medium",children:["License Plate: ",a.licensePlate]}),(0,n.jsxs)("p",{className:"text-xs",children:["Phone: ",a.phoneNumber]})]})]})})]})}):null}let a=(0,s.createContext)({showNotification:()=>{}});function l({children:e}){let[t,r]=(0,s.useState)([]),l=e=>{r(t=>t.filter(t=>t.id!==e))};return(0,n.jsxs)(a.Provider,{value:{showNotification:(e,t="info",n=5e3,s)=>{let i=Date.now().toString();r(r=>[...r,{id:i,message:e,type:t,duration:n,driverDetails:s}])}},children:[e,t.map(e=>n.jsx(i,{message:e.message,type:e.type,duration:e.duration,driverDetails:e.driverDetails,onClose:()=>l(e.id)},e.id))]})}let o=()=>(0,s.useContext)(a)},2942:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{r:()=>u,s:()=>f});var s=r(997),i=r(6689),a=r(6317),l=r(1163),o=r(1530),d=e([a]);a=(d.then?(await d)():d)[0];let c=(0,i.createContext)({hasAccess:()=>!1,checkAccess:()=>!1,userRole:"guest",isAdmin:!1,isDriver:!1,isRider:!1}),p={"/":["admin","driver","rider","guest"],"/login":["admin","driver","rider","guest"],"/signup":["admin","driver","rider","guest"],"/forgot-password":["admin","driver","rider","guest"],"/book":["admin","rider"],"/driver":["admin","driver"],"/driver/dashboard":["admin","driver"],"/admin":["admin"],"/admin/dashboard":["admin"],"/admin/users":["admin"],"/admin/bookings":["admin"]};function u({children:e}){let{user:t,loading:r}=(0,a.a)();(0,l.useRouter)();let{showNotification:n}=(0,o.l)(),[d,u]=(0,i.useState)(!1),f=t?"admin"===t.role?"admin":"driver"===t.role?"driver":"rider":"guest",h="admin"===f,m="driver"===f,g="rider"===f;return s.jsx(c.Provider,{value:{hasAccess:e=>{let t=p[e];return t?t.includes(f):"admin"===f},checkAccess:e=>e.includes(f),userRole:f,isAdmin:h,isDriver:m,isRider:g},children:d?e:null})}let f=()=>(0,i.useContext)(c);n()}catch(e){n(e)}})},3462:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{I:()=>u,db:()=>c});var s=r(3745),i=r(401),a=r(1492),l=r(3392),o=e([s,i,a,l]);[s,i,a,l]=o.then?(await o)():o;let d=(0,s.initializeApp)({apiKey:"AIzaSyCpM9kt3NzuzhenM7KwfGkNiO9B-p0-4Po",authDomain:"baroride.firebaseapp.com",projectId:"baroride",storageBucket:"baroride.firebasestorage.app",messagingSenderId:"191771619835",appId:"1:191771619835:web:2fc57d131cf64a35e2db5e"}),u=(0,i.getAuth)(d),c=(0,a.getFirestore)(d);(0,l.getStorage)(d),n()}catch(e){n(e)}})},3893:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>d});var s=r(997);r(108);var i=r(6317),a=r(1530),l=r(2942),o=e([i,l]);[i,l]=o.then?(await o)():o;let d=function({Component:e,pageProps:t}){return s.jsx(i.H,{children:s.jsx(a.J,{children:s.jsx(l.r,{children:s.jsx(e,{...t})})})})};n()}catch(e){n(e)}})},1070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(997),s=r(6859);function i(){return(0,n.jsxs)(s.Html,{lang:"en",children:[n.jsx(s.Head,{}),(0,n.jsxs)("body",{children:[n.jsx(s.Main,{}),n.jsx(s.NextScript,{})]})]})}},108:()=>{},5244:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},8039:(e,t,r)=>{"use strict";e.exports=r(7093).vendored.contexts.AmpContext},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3745:e=>{"use strict";e.exports=import("firebase/app")},401:e=>{"use strict";e.exports=import("firebase/auth")},1492:e=>{"use strict";e.exports=import("firebase/firestore")},3392:e=>{"use strict";e.exports=import("firebase/storage")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[567,163,859],()=>r(6968));module.exports=n})();