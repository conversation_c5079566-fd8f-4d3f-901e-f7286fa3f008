(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{2255:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/book",function(){return a(3663)}])},5695:function(e,t,a){"use strict";a.d(t,{Z:function(){return c}});var s=a(5893),o=a(7294),r=a(1163),n=a(837),l=a(7339),i=a(6492);function c(e){let{children:t,requiredRoles:a,redirectTo:c="/login"}=e,{user:d,loading:u}=(0,n.a)(),{checkAccess:m}=(0,l.s)(),x=(0,r.useRouter)(),{showNotification:g}=(0,i.l)();return((0,o.useEffect)(()=>{if(!u){if(!d){x.push(c),g("Please log in to access this page","warning");return}if(!m(a)){let e="/",t="Access denied. You do not have permission to view this page.";"driver"===d.role?(e="/driver/dashboard",t="Access denied. Redirected to driver dashboard."):"rider"===d.role&&(e="/",t="Access denied. Redirected to home page."),x.push(e),g(t,"warning")}}},[d,u,a,x,c,m]),u)?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):d&&m(a)?(0,s.jsx)(s.Fragment,{children:t}):null}},3663:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var s=a(5893),o=a(7294),r=a(837),n=a(404),l=a(109);function i(e){let{height:t="300px",selectable:a=!1,onLocationSelected:r,initialLocation:n}=e,l=(0,o.useRef)(null),[i,c]=(0,o.useState)(!1),d=(0,o.useRef)(null),u=e=>{d.current&&e&&0!==e.lat&&0!==e.lng&&(document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([e.lng,e.lat]).addTo(d.current),d.current.flyTo({center:[e.lng,e.lat],zoom:14,essential:!0}),e.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([e.lng,e.lat]).setHTML('<p style="margin: 0;">'.concat(e.address,"</p>")).addTo(d.current))};return(0,o.useEffect)(()=>{let e=()=>{if(!l.current||!window.mapboxgl)return;window.mapboxgl.accessToken="pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A";let e=34.5925,t=8.2483;n&&0!==n.lat&&0!==n.lng&&(e=n.lng,t=n.lat),console.log("Using manual location selection only - automatic geolocation disabled");let s=new window.mapboxgl.Map({container:l.current,style:"mapbox://styles/mapbox/streets-v11",center:[e,t],zoom:13});return d.current=s,s.addControl(new window.mapboxgl.NavigationControl),n&&0!==n.lat&&0!==n.lng?(new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([n.lng,n.lat]).addTo(s),n.address&&new window.mapboxgl.Popup({offset:25,closeButton:!1}).setLngLat([n.lng,n.lat]).setHTML('<p style="margin: 0;">'.concat(n.address,"</p>")).addTo(s)):new window.mapboxgl.Marker().setLngLat([e,t]).addTo(s),a&&r&&s.on("click",async e=>{let{lng:t,lat:a}=e.lngLat;document.querySelectorAll(".mapboxgl-marker").forEach(e=>e.remove()),new window.mapboxgl.Marker({color:"#3b82f6"}).setLngLat([t,a]).addTo(s),c(!0);try{let e=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(t,",").concat(a,".json?access_token=").concat(window.mapboxgl.accessToken)),o=await e.json(),n="Unknown location";o.features&&o.features.length>0&&(n=o.features[0].place_name);let l=new window.mapboxgl.Popup({offset:25}).setLngLat([t,a]).setHTML('\n                <div style="text-align: center;">\n                  <p style="margin-bottom: 8px;">'.concat(n,'</p>\n                  <button id="select-location" style="background-color: #3b82f6; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">\n                    Select this location\n                  </button>\n                </div>\n              ')).addTo(s);setTimeout(()=>{let e=document.getElementById("select-location");e&&e.addEventListener("click",()=>{r({lat:a,lng:t,address:n}),l.remove()})},100)}catch(e){console.error("Error geocoding location:",e)}finally{c(!1)}}),()=>s.remove()};(()=>{if(window.mapboxgl){e();return}let t=document.createElement("script");t.src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js",t.async=!0,t.onload=()=>{e()},document.head.appendChild(t);let a=document.createElement("link");a.href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css",a.rel="stylesheet",document.head.appendChild(a)})()},[a,r]),(0,o.useEffect)(()=>{if(n&&0!==n.lat&&0!==n.lng){let e=setInterval(()=>{d.current&&(u(n),clearInterval(e))},100);setTimeout(()=>clearInterval(e),5e3)}},[n]),(0,s.jsxs)("div",{style:{position:"relative"},children:[(0,s.jsx)("div",{ref:l,style:{width:"100%",height:t,position:"relative",border:"1px solid #ddd",borderRadius:"4px"}}),i&&(0,s.jsx)("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.2)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:10},children:(0,s.jsx)("div",{style:{backgroundColor:"white",padding:"12px",borderRadius:"50%",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},children:(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",opacity:"0.25"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",fill:"none",stroke:"#3b82f6",strokeWidth:"4",strokeDasharray:"60 30",style:{animation:"spin 1s linear infinite"}}),(0,s.jsx)("style",{children:"\n                @keyframes spin {\n                  0% { transform: rotate(0deg); }\n                  100% { transform: rotate(360deg); }\n                }\n              "})]})})}),a&&(0,s.jsx)("div",{style:{position:"absolute",top:"10px",left:"10px",right:"10px",backgroundColor:"white",padding:"8px 12px",borderRadius:"4px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)",zIndex:5,fontSize:"14px",textAlign:"center",color:"#4b5563"},children:"Click anywhere on the map to manually select your pickup location"})]})}var c=a(7994),d=a(6492);let u=async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,s=0;for(;s<a;)try{let a=(0,l.IO)((0,l.hJ)(n.db,e),...t),s=await (0,l.PL)(a),o=[];return s.forEach(e=>{o.push({id:e.id,...e.data()})}),o}catch(e){if(s++,console.error("Error querying documents (attempt ".concat(s,"/").concat(a,"):"),e),s>=a)throw e;await new Promise(e=>setTimeout(e,1e3))}return[]},m=async function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,s=0;for(;s<a;)try{return(await (0,l.ET)((0,l.hJ)(n.db,e),t)).id}catch(e){if(s++,console.error("Error adding document (attempt ".concat(s,"/").concat(a,"):"),e),s>=a)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to add document after maximum retries")},x=async function(e,t,a){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2,o=0;for(;o<s;)try{let s=(0,l.JU)(n.db,e,t);await (0,l.r7)(s,a);return}catch(e){if(o++,console.error("Error updating document (attempt ".concat(o,"/").concat(s,"):"),e),o>=s)throw e;await new Promise(e=>setTimeout(e,1e3))}throw Error("Failed to update document after maximum retries")},g=()=>!0,p=()=>{if(!g())return!1;let e=window.location.hostname;return e.includes("firebaseapp.com")||e.includes("web.app")||"baroride.web.app"===e};var h=a(5695);function b(e){let{amount:t,onPaymentComplete:a,onCancel:r,description:n="BaroRide Booking"}=e,{showNotification:l}=(0,d.l)(),[i,c]=(0,o.useState)("credit_card"),[u,m]=(0,o.useState)(""),[x,g]=(0,o.useState)(""),[p,h]=(0,o.useState)(""),[b,f]=(0,o.useState)(""),[y,w]=(0,o.useState)(!1),[v,j]=(0,o.useState)(!0),[k,N]=(0,o.useState)(!1);(0,o.useEffect)(()=>{j("credit_card"===i||"debit_card"===i),N("mobile_money"===i)},[i]);let C=e=>{let t=e.replace(/\s+/g,"").replace(/[^0-9]/gi,"").match(/\d{4,16}/g),a=t&&t[0]||"",s=[];for(let e=0,t=a.length;e<t;e+=4)s.push(a.substring(e,e+4));return s.length?s.join(" "):e},P=e=>{let t=e.replace(/\s+/g,"").replace(/[^0-9]/gi,"");return t.length>=2?"".concat(t.substring(0,2),"/").concat(t.substring(2,4)):t},D=async()=>{if("credit_card"===i||"debit_card"===i){if(!u||u.replace(/\s+/g,"").length<16){l("Please enter a valid card number","error");return}if(!x||x.length<5){l("Please enter a valid expiry date (MM/YY)","error");return}if(!p||p.length<3){l("Please enter a valid CVC code","error");return}}else if("mobile_money"===i&&(!b||b.length<10)){l("Please enter a valid mobile number","error");return}w(!0),l("Processing payment...","info");try{let e={amount:t,currency:"ETB",paymentMethod:i,description:n||"BaroRide Booking"};"credit_card"===i||"debit_card"===i?(e.cardNumber=u.replace(/\s+/g,""),e.cardExpiry=x,e.cardCVC=p):"mobile_money"===i&&(e.mobileNumber=b);let s=await L(e);s.success?l("Payment successful! Transaction ID: ".concat(s.transactionId),"success"):l("Payment failed: ".concat(s.message),"error"),a(s)}catch(t){console.error("Payment processing error:",t);let e={success:!1,status:"failed",message:t instanceof Error?t.message:"An unknown error occurred",timestamp:new Date};l("Payment failed: ".concat(e.message),"error"),a(e)}finally{w(!1)}},L=async e=>{await new Promise(e=>setTimeout(e,2e3));let t="TR".concat(Date.now().toString().slice(-8)).concat(Math.floor(1e3*Math.random())),a=.9>Math.random();return{success:a,transactionId:a?t:void 0,status:a?"completed":"failed",message:a?"Payment processed successfully":"Payment declined by issuer",timestamp:new Date}};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4 sm:p-6 w-full",children:[(0,s.jsxs)("div",{className:"text-center mb-4",children:[(0,s.jsx)("h2",{className:"text-lg sm:text-xl font-bold text-gray-900",children:"Payment Details"}),(0,s.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg",children:[(0,s.jsxs)("p",{className:"text-lg sm:text-xl font-bold text-blue-900",children:["$",t.toFixed(2)," ETB"]}),(0,s.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mt-1",children:n})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method"}),(0,s.jsxs)("select",{value:i,onChange:e=>c(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",disabled:y,children:[(0,s.jsx)("option",{value:"credit_card",children:"\uD83D\uDCB3 Credit Card"}),(0,s.jsx)("option",{value:"debit_card",children:"\uD83D\uDCB3 Debit Card"}),(0,s.jsx)("option",{value:"mobile_money",children:"\uD83D\uDCF1 Mobile Money"})]})]}),v&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Number"}),(0,s.jsx)("input",{type:"text",placeholder:"1234 5678 9012 3456",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",value:u,onChange:e=>m(C(e.target.value)),maxLength:19,disabled:y,autoComplete:"cc-number"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiry Date"}),(0,s.jsx)("input",{type:"text",placeholder:"MM/YY",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",value:x,onChange:e=>g(P(e.target.value)),maxLength:5,disabled:y,autoComplete:"cc-exp"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVC"}),(0,s.jsx)("input",{type:"text",placeholder:"123",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",value:p,onChange:e=>h(e.target.value.replace(/[^0-9]/g,"").substring(0,3)),maxLength:3,disabled:y,autoComplete:"cc-csc"})]})]})]}),k&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Mobile Number"}),(0,s.jsx)("input",{type:"tel",placeholder:"e.g., 0911234567",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-base",value:b,onChange:e=>f(e.target.value.replace(/[^0-9]/g,"")),disabled:y,autoComplete:"tel"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"\uD83D\uDCF1 You will receive a payment confirmation code via SMS"})]}),(0,s.jsxs)("div",{className:"mt-6 flex flex-col sm:flex-row gap-3 sm:justify-between",children:[(0,s.jsx)("button",{type:"button",onClick:r,className:"w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-lg text-base font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors touch-manipulation",disabled:y,style:{touchAction:"manipulation"},children:"Cancel"}),(0,s.jsx)("button",{type:"button",onClick:D,className:"w-full sm:w-auto px-6 py-3 rounded-lg text-base font-medium text-white transition-all duration-200 touch-manipulation ".concat(y?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg"),disabled:y,style:{touchAction:"manipulation"},children:y?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing Payment..."]}):(0,s.jsxs)("span",{className:"flex items-center justify-center",children:["\uD83D\uDCB3 Pay $",t.toFixed(2)]})})]})]})}let f=[{code:"GMB",name:"Gambela International Airport"}];function y(){let{user:e}=(0,r.a)(),{showNotification:t}=(0,d.l)(),[a,g]=(0,o.useState)({address:"",lat:0,lng:0}),[h,y]=(0,o.useState)(f[0]),[w,v]=(0,o.useState)(1),[j,k]=(0,o.useState)(!1),[N,C]=(0,o.useState)(0),[P,D]=(0,o.useState)([]),[L,E]=(0,o.useState)(!1),[T,M]=(0,o.useState)(!1),[S,I]=(0,o.useState)(null),[A,B]=(0,o.useState)(!1),[_,F]=(0,o.useState)(!1),[R,Y]=(0,o.useState)("pending"),[W,z]=(0,o.useState)(null);(0,o.useEffect)(()=>{e&&H()},[e]);let H=async()=>{if(e){E(!0);try{p()&&console.log("Fetching previous bookings in Firebase hosting environment");let t=[];if(p()){let a=[(0,l.ar)("riderId","==",e.id),(0,l.ar)("status","==","completed"),(0,l.Xo)("updatedAt","desc"),(0,l.b9)(5)];t=(await u("bookings",a)).map(e=>({id:e.id,...e,scheduledTime:e.scheduledTime?new Date(e.scheduledTime):new Date,createdAt:e.createdAt?new Date(e.createdAt):new Date,updatedAt:e.updatedAt?new Date(e.updatedAt):new Date,passengers:e.passengers||1}))}else{let a=(0,l.IO)((0,l.hJ)(n.db,"bookings"),(0,l.ar)("riderId","==",e.id),(0,l.ar)("status","==","completed"),(0,l.Xo)("updatedAt","desc"),(0,l.b9)(5));(await (0,l.PL)(a)).forEach(e=>{let a=e.data(),s={id:e.id,...a,scheduledTime:a.scheduledTime?new Date(a.scheduledTime):new Date,createdAt:a.createdAt?new Date(a.createdAt):new Date,updatedAt:a.updatedAt?new Date(a.updatedAt):new Date,passengers:a.passengers||1};t.push(s)})}D(t),console.log("Fetched ".concat(t.length," previous bookings"))}catch(e){console.error("Error fetching previous bookings:",e),t("Could not load your previous bookings. Please try again later.","warning")}finally{E(!1)}}},U=()=>{g({address:"",lat:0,lng:0}),I(null)},O=async()=>{if(!navigator.geolocation){t("Geolocation is not supported by your browser. Please enter your location manually.","error");return}B(!0),t("Getting your current location...","info");try{let{latitude:e,longitude:a}=(await new Promise((e,t)=>{navigator.geolocation.getCurrentPosition(t=>{console.log("GPS position obtained:",t.coords),e(t)},e=>{console.error("GPS error:",e.code,e.message);let a="Unable to retrieve your location.";1===e.code?a="Location access denied. Please enable location services in your browser settings.":2===e.code?a="Your current position is unavailable. Please try again later.":3===e.code&&(a="Location request timed out. Please try again."),t(Error(a))},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})).coords;console.log("GPS coordinates: ".concat(e,", ").concat(a));try{let s=await fetch("https://api.mapbox.com/geocoding/v5/mapbox.places/".concat(a,",").concat(e,".json?access_token=pk.eyJ1IjoiOTI0NDEzODI5IiwiYSI6ImNtOXZiNHN3eTBmcmUyanIyeWFxanhiZW8ifQ.Q8NockBZF7I7-sb7TN2O8A"));if(!s.ok)throw Error("Geocoding API error: ".concat(s.status));let o=await s.json();console.log("Geocoding response:",o);let r="Your current location";o.features&&o.features.length>0&&(r=o.features[0].place_name,console.log("Address found:",r));let n={lat:e,lng:a,address:r};g(n),I(null);let l=G(n);C(l),t("Your current location has been set as the pickup point.","success")}catch(r){console.error("Error getting address:",r);let s={lat:e,lng:a,address:"Your current location"};g(s),I(null);let o=G(s);C(o),t("Location set, but we couldn't get your exact address. You can edit it manually.","warning")}}catch(e){console.error("Error getting location:",e),t(e instanceof Error?e.message:"Unable to retrieve your location. Please try again or select manually.","error")}finally{B(!1)}},J=e=>{g(e.pickupLocation);let t=f.find(t=>t.code===e.airport.code);t&&y(t),I(e.id),M(!1)},G=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return 0===e.lat||0===e.lng?0:35+Math.floor(10+e.lat*e.lng%100/5)+(w-1)*5};(0,o.useEffect)(()=>{0!==a.lat&&0!==a.lng&&C(G(a))},[a,w]);let Z=async e=>{z(e),Y("completed"===e.status?"completed":"failed"),e.success?await Q(e.transactionId):(t("Payment failed: ".concat(e.message,". Please try again."),"error"),F(!1))},Q=async s=>{if(!e){t("Please log in to book a ride.","error");return}try{let o;t("Creating your booking...","info");let r=new Date,i={riderId:e.id,pickupLocation:{address:a.address,lat:a.lat,lng:a.lng},airport:{name:h.name,code:h.code},status:"pending",fare:N>0?N:G(),passengers:w,createdAt:r,updatedAt:r,paymentStatus:"completed",paymentTransactionId:s};if(console.log("Creating booking with data:",i),p())console.log("Using safe function to create booking in production"),o=await m("bookings",i),console.log("Booking created with ID:",o);else try{o=(await (0,l.ET)((0,l.hJ)(n.db,"bookings"),i)).id,console.log("Booking created with ID:",o)}catch(e){console.error("Error creating booking:",e),t("Retrying booking creation...","info"),await new Promise(e=>setTimeout(e,1e3)),o=(await (0,l.ET)((0,l.hJ)(n.db,"bookings"),i)).id,console.log("Booking created on second attempt with ID:",o)}try{if(p()){let t=await (0,l.QT)((0,l.JU)(n.db,"users",e.id));if(t.exists()){let a=t.data().bookingHistory||[];a.includes(o)||(await x("users",e.id,{bookingHistory:[...a,o]}),console.log("Updated user booking history"))}}else{let t=(0,l.JU)(n.db,"users",e.id),a=await (0,l.QT)(t);if(a.exists()){let e=a.data().bookingHistory||[];e.includes(o)||(await (0,l.r7)(t,{bookingHistory:[...e,o]}),console.log("Updated user booking history"))}}}catch(e){console.error("Error updating booking history:",e)}try{let t={userId:e.id,message:"Your ride has been booked and paid successfully. Waiting for a driver to accept.",type:"info",read:!1,relatedBookingId:o,createdAt:r};p()?await m("notifications",t):await (0,l.ET)((0,l.hJ)(n.db,"notifications"),t),console.log("Created notification for user")}catch(e){console.error("Error creating notification:",e)}t("Booking created and payment processed successfully! Waiting for a driver to accept.","success"),F(!1),g({address:"",lat:0,lng:0}),v(1),C(0),I(null)}catch(a){console.error("Error creating booking:",a);let e="Failed to create booking. Please try again.";a instanceof Error&&(a.message.includes("network")?e="Network error. Please check your internet connection and try again.":a.message.includes("permission-denied")?e="Permission denied. Please log out and log back in.":a.message.includes("not-found")&&(e="Database connection error. Please refresh the page and try again.")),t(e,"error"),F(!1)}};return(0,s.jsx)(c.Z,{title:"BaroRide - Book a Ride",children:(0,s.jsxs)("div",{className:"container mx-auto p-2 sm:p-4 max-w-4xl",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h1",{className:"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900",children:"Book Your Ride"}),(0,s.jsx)("p",{className:"text-sm sm:text-base text-gray-600 mt-1",children:"Quick and easy airport transportation"})]}),_&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto",children:(0,s.jsx)(b,{amount:N,onPaymentComplete:Z,onCancel:()=>{t("Payment cancelled. Your booking was not created.","info"),F(!1)},description:"Ride to ".concat(h.name," with ").concat(w," passenger").concat(w>1?"s":"")})})}),(0,s.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),!e){t("Please log in to book a ride.","error");return}if(!a.address||0===a.lat||0===a.lng){t("Please select a valid pickup location.","error");return}F(!0)},className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2 sm:mb-0",children:"Pickup Location"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{k(!j)},className:"text-xs sm:text-sm text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-colors touch-manipulation",style:{touchAction:"manipulation"},children:j?"\uD83D\uDCDD Manual Entry":"\uD83D\uDDFA️ Select on Map"}),(0,s.jsx)("button",{type:"button",onClick:O,className:"text-xs sm:text-sm text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-full transition-colors touch-manipulation flex items-center",style:{touchAction:"manipulation"},disabled:A,children:A?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-1 h-3 w-3 sm:h-4 sm:w-4 text-purple-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Getting..."]}):(0,s.jsx)(s.Fragment,{children:"\uD83D\uDCCD My Location"})}),P.length>0&&(0,s.jsx)("button",{type:"button",onClick:()=>{M(!T)},className:"text-sm text-green-500 hover:text-green-700",children:T?"Hide Previous":"Use Previous"})]})]}),j?(0,s.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Click on the map to manually select your pickup location"}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:"Enter your pickup address manually",className:"w-full p-2 border rounded",value:a.address,onChange:e=>{g(t=>({...t,address:e.target.value})),S&&I(null)}}),a.address&&(0,s.jsx)("button",{type:"button",onClick:U,className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[(0,s.jsx)("svg",{className:"w-3 h-3 inline-block mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),'Enter your address manually, click "Use My Location", or select it on the map']})]}),T&&(0,s.jsxs)("div",{className:"mt-2 border rounded shadow-sm overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 px-3 py-2 border-b",children:(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Previous Bookings"})}),(0,s.jsx)("div",{className:"max-h-60 overflow-y-auto",children:L?(0,s.jsxs)("div",{className:"p-4 text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Loading..."})]}):0===P.length?(0,s.jsx)("div",{className:"p-4 text-center text-sm text-gray-600",children:"No previous bookings found"}):(0,s.jsx)("ul",{className:"divide-y divide-gray-200",children:P.map(e=>(0,s.jsx)("li",{className:"p-3 hover:bg-gray-50 cursor-pointer transition-colors ".concat(S===e.id?"bg-blue-50 border-l-4 border-blue-500":""),onClick:()=>J(e),children:(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e.pickupLocation.address}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["To: ",e.airport.name]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"text-xs text-gray-500 mr-2",children:new Date(e.updatedAt).toLocaleDateString()}),S===e.id&&(0,s.jsx)("span",{className:"text-xs text-blue-500 font-medium",children:"Selected"})]})]})},e.id))})})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Pickup Location Map"}),(0,s.jsx)(i,{height:"300px",selectable:j,onLocationSelected:e=>{if(!e||"number"!=typeof e.lat||"number"!=typeof e.lng){t("Invalid location selected. Please try again.","error");return}let a={lat:e.lat,lng:e.lng,address:e.address||"Selected location"};console.log("Location selected from map:",a),g(a),I(null),k(!1),C(G(a)),t("Pickup location selected successfully!","success")},initialLocation:0!==a.lat?a:void 0}),0!==a.lat&&0!==a.lng&&(0,s.jsx)("div",{className:"mt-2 p-2 bg-blue-50 border border-blue-100 rounded",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("p",{className:"text-sm font-medium",children:["Selected Pickup: ",a.address]}),S&&(0,s.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:"From Previous Booking"})]}),(0,s.jsx)("button",{type:"button",onClick:U,className:"ml-2 text-xs text-red-500 hover:text-red-700",children:"Clear"})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Airport"}),(0,s.jsx)("select",{value:h.code,onChange:e=>{let t=f.find(t=>t.code===e.target.value);t&&y(t)},className:"w-full p-2 border rounded",children:f.map(e=>(0,s.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Number of Passengers"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{type:"button",onClick:()=>v(e=>Math.max(1,e-1)),className:"p-2 bg-gray-100 border rounded-l hover:bg-gray-200",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M20 12H4"})})}),(0,s.jsx)("input",{type:"number",min:"1",max:"8",value:w,onChange:e=>v(Math.max(1,Math.min(8,parseInt(e.target.value)||1))),className:"w-full p-2 border-t border-b text-center"}),(0,s.jsx)("button",{type:"button",onClick:()=>v(e=>Math.min(8,e+1)),className:"p-2 bg-gray-100 border rounded-r hover:bg-gray-200",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v12M6 12h12"})})})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum 8 passengers per ride"})]})]}),N>0&&(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded p-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-blue-800 mb-2",children:"Fare Estimate"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Base fare"}),(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Distance"}),w>1&&(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:["Additional passengers (",w-1,")"]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"$35.00"}),(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:["$",Math.floor(10+a.lat*a.lng%100/5).toFixed(2)]}),w>1&&(0,s.jsxs)("p",{className:"text-sm text-blue-700",children:["$",((w-1)*5).toFixed(2)]})]})]}),(0,s.jsxs)("div",{className:"border-t border-blue-200 mt-2 pt-2 flex justify-between items-center",children:[(0,s.jsx)("p",{className:"font-medium text-blue-800",children:"Total estimated fare"}),(0,s.jsxs)("p",{className:"font-medium text-blue-800",children:["$",N.toFixed(2)]})]}),(0,s.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"* Actual fare may vary based on traffic, weather, and other factors."})]}),(0,s.jsxs)("div",{className:"pt-4",children:[(0,s.jsx)("button",{type:"submit",className:"w-full ".concat(a.address&&0!==a.lat&&0!==a.lng?"bg-blue-500 hover:bg-blue-600":"bg-gray-400 cursor-not-allowed"," text-white p-3 rounded font-medium transition-colors"),disabled:!a.address||0===a.lat||0===a.lng,children:e?"Proceed to Payment":"Please Log In to Book a Ride"}),(!a.address||0===a.lat||0===a.lng)&&(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-red-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,s.jsx)("p",{className:"text-red-500 text-sm",children:"Please enter or select a pickup location"})]}),!e&&(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-yellow-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-yellow-500 text-sm",children:"You need to be logged in to book a ride"})]})]})]})]})})}function w(){return(0,s.jsx)(h.Z,{requiredRoles:["admin","rider"],children:(0,s.jsx)(y,{})})}}},function(e){e.O(0,[996,994,888,774,179],function(){return e(e.s=2255)}),_N_E=e.O()}]);